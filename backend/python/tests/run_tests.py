#!/usr/bin/env python3
"""
Test runner script for FileParser tests.
Provides convenient ways to run different test suites.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print('='*60)
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.stdout:
        print("STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    print(f"Return code: {result.returncode}")
    return result.returncode == 0


def main():
    parser = argparse.ArgumentParser(description="Run FileParser tests")
    parser.add_argument(
        "--test-type", 
        choices=["all", "basic", "language", "integration", "fast"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--language",
        help="Run tests for specific language only"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Run with coverage reporting"
    )
    parser.add_argument(
        "--parallel", "-n",
        type=int,
        help="Number of parallel workers"
    )
    
    args = parser.parse_args()
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test directory
    test_dir = Path(__file__).parent
    cmd.append(str(test_dir))
    
    # Add verbosity
    if args.verbose:
        cmd.append("-v")
    
    # Add parallel execution
    if args.parallel:
        cmd.extend(["-n", str(args.parallel)])
    
    # Add coverage
    if args.coverage:
        cmd.extend([
            "--cov=modules.integrations.tools.treesitter",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
    
    # Filter by test type
    if args.test_type == "basic":
        cmd.extend(["-k", "not language_specific and not integration"])
    elif args.test_type == "language":
        cmd.extend(["-m", "language_specific"])
    elif args.test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif args.test_type == "fast":
        cmd.extend(["-m", "not slow"])
    
    # Filter by language
    if args.language:
        cmd.extend(["-k", args.language])
    
    # Run the tests
    success = run_command(cmd, f"FileParser tests ({args.test_type})")
    
    if success:
        print("\n✅ All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

"""
Pytest configuration and fixtures for FileParser tests.
"""

import pytest
import sys
from pathlib import Path

# Add the backend/python directory to the Python path
backend_python_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_python_dir))

# Test configuration
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "language_specific: marks tests as language-specific"
    )

def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Mark language-specific tests
        if "language" in item.name.lower() or "specific" in item.name.lower():
            item.add_marker(pytest.mark.language_specific)
        
        # Mark slow tests
        if "concurrent" in item.name.lower() or "large" in item.name.lower():
            item.add_marker(pytest.mark.slow)

@pytest.fixture(scope="session")
def test_resources_dir():
    """Get the test resources directory."""
    return Path(__file__).parent / "resources"

@pytest.fixture(scope="session")
def sample_files_exist(test_resources_dir):
    """Check if sample files exist and skip tests if they don't."""
    required_languages = [
        "python", "java", "javascript", "typescript", 
        "c", "cpp", "go", "rust", "php", "ruby"
    ]
    
    missing_files = []
    for lang in required_languages:
        lang_dir = test_resources_dir / lang
        if not lang_dir.exists():
            missing_files.append(lang)
    
    if missing_files:
        pytest.skip(f"Missing sample files for languages: {missing_files}")
    
    return True

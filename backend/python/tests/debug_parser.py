#!/usr/bin/env python3
"""
Debug script to test FileParser functionality and see what it detects.
"""

import sys
from pathlib import Path

# Add the backend/python directory to the Python path
backend_python_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_python_dir))

from core.config import ChunkConfig
from modules.integrations.tools.treesitter.parser import FileParser


def debug_python_parsing():
    """Debug Python file parsing."""
    print("🔍 Debugging Python file parsing...")
    
    # Create parser
    chunk_config = ChunkConfig(
        name="test",
        min_chunk_size=10,
        max_chunk_size=50,
        overflow_size=5
    )
    parser = FileParser(chunk_config)
    
    # Load Python sample file
    resources_dir = Path(__file__).parent / "resources"
    python_file = resources_dir / "python" / "sample.py"
    
    if not python_file.exists():
        print(f"❌ Python sample file not found: {python_file}")
        return
    
    with open(python_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📁 File: {python_file}")
    print(f"📏 Content length: {len(content)} characters")
    print(f"📄 Line count: {len(content.splitlines())}")
    
    # Parse the file
    try:
        # First, let's test the language detection
        from modules.common.constants import LanguageEnum
        language_enum = LanguageEnum.from_suffix("py")
        print(f"\n🔍 Language detection:")
        print(f"   File extension: .py")
        print(f"   Detected language: {language_enum}")

        # Test tree-sitter parser loading directly
        print(f"\n🌳 Testing tree-sitter parser:")
        try:
            from tree_sitter_language_pack import get_parser
            ts_parser = get_parser("python")
            print(f"   Tree-sitter parser loaded: {ts_parser}")
        except Exception as e:
            print(f"   ❌ Failed to load tree-sitter parser: {e}")

        # Test query loading
        print(f"\n📋 Testing query loading:")
        query_file = Path(__file__).parent.parent / "modules" / "integrations" / "tools" / "treesitter" / "queries" / "python.scm"
        print(f"   Query file path: {query_file}")
        print(f"   Query file exists: {query_file.exists()}")

        if query_file.exists():
            try:
                query_content = query_file.read_text(encoding="utf-8")
                print(f"   Query content length: {len(query_content)} characters")
                print(f"   First 200 chars: {query_content[:200]}...")
            except Exception as e:
                print(f"   ❌ Failed to read query file: {e}")

        # Now try parsing
        print(f"\n🔄 Attempting to parse...")
        key_lines, chunks = parser.parse(str(python_file), content)

        print(f"\n🔑 Key structure lines found: {len(key_lines)}")
        for line_num, structure_type in sorted(key_lines.items()):
            print(f"   Line {line_num}: {structure_type}")

        print(f"\n📦 Chunks found: {len(chunks)}")
        for i, chunk in enumerate(chunks):
            print(f"   Chunk {i}: lines {chunk.start_line}-{chunk.end_line} ({chunk.end_line - chunk.start_line + 1} lines)")

        # Show first few lines of content to verify
        print(f"\n📝 First 10 lines of content:")
        lines = content.splitlines()
        for i, line in enumerate(lines[:10]):
            print(f"   {i:2d}: {line}")

        # Check if parser and query are loaded after parsing attempt
        python_enum = LanguageEnum.PYTHON

        print(f"\n🔧 Parser status after parsing:")
        print(f"   Python parser loaded: {python_enum in parser.language_parsers}")
        print(f"   Python query loaded: {python_enum in parser.language_queries}")

        if python_enum in parser.language_parsers:
            parser_obj = parser.language_parsers[python_enum]
            print(f"   Parser object: {parser_obj}")
            if parser_obj is None:
                print(f"   ⚠️  Parser is None (failed to load)")

        if python_enum in parser.language_queries:
            query_obj = parser.language_queries[python_enum]
            print(f"   Query object: {query_obj}")
            if query_obj is None:
                print(f"   ⚠️  Query is None (failed to load)")

    except Exception as e:
        print(f"❌ Error during parsing: {e}")
        import traceback
        traceback.print_exc()


def test_simple_python_content():
    """Test with simple Python content."""
    print("\n" + "="*60)
    print("🧪 Testing with simple Python content...")
    
    chunk_config = ChunkConfig(
        name="test",
        min_chunk_size=5,
        max_chunk_size=20,
        overflow_size=3
    )
    parser = FileParser(chunk_config)
    
    simple_content = '''
class TestClass:
    def __init__(self):
        self.value = 42
    
    def test_method(self):
        return self.value

def test_function():
    return "hello"

if __name__ == "__main__":
    obj = TestClass()
    print(obj.test_method())
'''
    
    try:
        key_lines, chunks = parser.parse("test.py", simple_content)
        
        print(f"🔑 Key structure lines: {len(key_lines)}")
        for line_num, structure_type in sorted(key_lines.items()):
            print(f"   Line {line_num}: {structure_type}")
        
        print(f"📦 Chunks: {len(chunks)}")
        for i, chunk in enumerate(chunks):
            print(f"   Chunk {i}: lines {chunk.start_line}-{chunk.end_line}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_python_parsing()
    test_simple_python_content()

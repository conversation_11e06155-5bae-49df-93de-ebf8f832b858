FileParser 解析结果报告 - SWIFT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift
  文件名: sample.swift
  内容长度: 8711 字符
  行数: 337


代码块信息 (5 个):
  块 1: 第 7-44 行 (38 行)
      7: protocol UserRepositoryProtocol {
      8:     func findUser(by id: Int) -> User?
      9:     func saveUser(_ user: User) -> Bool
    ... (还有 35 行)

  块 2: 第 47-146 行 (100 行)
     47: struct User: Codable, Validatable {
     48:     let id: Int
     49:     var name: String
    ... (还有 97 行)

  块 3: 第 148-218 行 (71 行)
    148: class UserService {
    149:     private let repository: UserRepositoryProtocol
    150:     private let logger: Logger
    ... (还有 68 行)

  块 4: 第 222-274 行 (53 行)
    222:     static func createSampleUser() -> User {
    223:         return User(id: 1, name: "<PERSON>", email: "<EMAIL>")
    224:     }
    ... (还有 50 行)

  块 5: 第 277-331 行 (55 行)
    277: func validateEmail(_ email: String) -> Bool {
    278:     let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    279:     let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
    ... (还有 52 行)


统计信息:
  覆盖率: 94.1%
  块中总行数: 317

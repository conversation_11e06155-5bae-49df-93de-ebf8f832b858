// Swift sample file for testing FileParser functionality.
// This file contains various Swift syntax structures that should be captured by the tree-sitter queries.

import Foundation
import UIKit

// Protocol definitions
protocol UserRepositoryProtocol {
    func findUser(by id: Int) -> User?
    func saveUser(_ user: User) -> Bool
    func deleteUser(by id: Int) -> Bool
}

protocol Validatable {
    func validate() -> [String]
    var isValid: Bool { get }
}

// Enum definitions
enum UserStatus: String, CaseIterable {
    case active = "active"
    case inactive = "inactive"
    case suspended = "suspended"
    case pending = "pending"
    
    var displayName: String {
        switch self {
        case .active:
            return "Active"
        case .inactive:
            return "Inactive"
        case .suspended:
            return "Suspended"
        case .pending:
            return "Pending Verification"
        }
    }
}

enum NetworkError: Error {
    case invalidURL
    case noData
    case decodingError(String)
    case serverError(Int)
}

// Struct definitions
struct User: Codable, Validatable {
    let id: Int
    var name: String
    var email: String
    var status: UserStatus
    var avatar: URL?
    let createdAt: Date
    var updatedAt: Date
    
    // Computed properties
    var isActive: Bool {
        return status == .active
    }
    
    var displayName: String {
        return name.isEmpty ? "Unknown User" : name
    }
    
    // Initializers
    init(id: Int, name: String, email: String, status: UserStatus = .pending) {
        self.id = id
        self.name = name
        self.email = email
        self.status = status
        self.avatar = nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    // Methods
    mutating func activate() {
        status = .active
        updatedAt = Date()
    }
    
    mutating func updateName(_ newName: String) {
        name = newName
        updatedAt = Date()
    }
    
    // Validation
    func validate() -> [String] {
        var errors: [String] = []
        
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("Name is required")
        }
        
        if email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("Email is required")
        } else if !isValidEmail(email) {
            errors.append("Invalid email format")
        }
        
        return errors
    }
    
    var isValid: Bool {
        return validate().isEmpty
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// Class definitions
class UserRepository: UserRepositoryProtocol {
    private var users: [Int: User] = [:]
    private let queue = DispatchQueue(label: "userRepository", attributes: .concurrent)
    
    func findUser(by id: Int) -> User? {
        return queue.sync {
            return users[id]
        }
    }
    
    func saveUser(_ user: User) -> Bool {
        guard user.isValid else { return false }
        
        return queue.sync(flags: .barrier) {
            users[user.id] = user
            return true
        }
    }
    
    func deleteUser(by id: Int) -> Bool {
        return queue.sync(flags: .barrier) {
            return users.removeValue(forKey: id) != nil
        }
    }
    
    func getAllUsers() -> [User] {
        return queue.sync {
            return Array(users.values)
        }
    }
}

class UserService {
    private let repository: UserRepositoryProtocol
    private let logger: Logger
    
    init(repository: UserRepositoryProtocol, logger: Logger = Logger()) {
        self.repository = repository
        self.logger = logger
    }
    
    func createUser(name: String, email: String) -> Result<User, NetworkError> {
        let user = User(id: generateUserId(), name: name, email: email)
        
        guard user.isValid else {
            let errors = user.validate().joined(separator: ", ")
            logger.error("Invalid user data: \(errors)")
            return .failure(.decodingError("Invalid user data: \(errors)"))
        }
        
        guard repository.saveUser(user) else {
            logger.error("Failed to save user")
            return .failure(.serverError(500))
        }
        
        logger.info("User created successfully: \(user.id)")
        return .success(user)
    }
    
    func activateUser(id: Int) -> Bool {
        guard var user = repository.findUser(by: id) else {
            logger.error("User not found: \(id)")
            return false
        }
        
        user.activate()
        return repository.saveUser(user)
    }
    
    private func generateUserId() -> Int {
        return Int.random(in: 1...1000000)
    }
}

// Generic class
class Repository<T: Codable> {
    private var items: [String: T] = [:]
    private let queue = DispatchQueue(label: "repository", attributes: .concurrent)
    
    func save(_ item: T, with key: String) {
        queue.async(flags: .barrier) {
            self.items[key] = item
        }
    }
    
    func find(by key: String) -> T? {
        return queue.sync {
            return items[key]
        }
    }
    
    func delete(by key: String) -> Bool {
        return queue.sync(flags: .barrier) {
            return items.removeValue(forKey: key) != nil
        }
    }
    
    func all() -> [T] {
        return queue.sync {
            return Array(items.values)
        }
    }
}

// Extension
extension User {
    static func createSampleUser() -> User {
        return User(id: 1, name: "John Doe", email: "<EMAIL>")
    }
    
    func toJSON() -> String? {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        
        guard let data = try? encoder.encode(self) else { return nil }
        return String(data: data, encoding: .utf8)
    }
}

extension UserStatus {
    var color: UIColor {
        switch self {
        case .active:
            return .systemGreen
        case .inactive:
            return .systemGray
        case .suspended:
            return .systemRed
        case .pending:
            return .systemOrange
        }
    }
}

// Logger class
class Logger {
    enum Level: String {
        case info = "INFO"
        case warning = "WARNING"
        case error = "ERROR"
    }
    
    func info(_ message: String) {
        log(message, level: .info)
    }
    
    func warning(_ message: String) {
        log(message, level: .warning)
    }
    
    func error(_ message: String) {
        log(message, level: .error)
    }
    
    private func log(_ message: String, level: Level) {
        let timestamp = DateFormatter.iso8601.string(from: Date())
        print("[\(timestamp)] [\(level.rawValue)] \(message)")
    }
}

// Functions
func validateEmail(_ email: String) -> Bool {
    let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
    return emailPredicate.evaluate(with: email)
}

func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {
    for user in users {
        transform(user)
    }
}

// Closures and higher-order functions
let userTransforms: [(User) -> String] = [
    { user in user.name.uppercased() },
    { user in user.email.lowercased() },
    { user in user.status.displayName }
]

// Example usage
func exampleUsage() {
    let logger = Logger()
    let repository = UserRepository()
    let service = UserService(repository: repository, logger: logger)
    
    // Create users
    let result1 = service.createUser(name: "Alice", email: "<EMAIL>")
    let result2 = service.createUser(name: "Bob", email: "<EMAIL>")
    
    switch result1 {
    case .success(let user):
        logger.info("Created user: \(user.name)")
        
        // Activate user
        if service.activateUser(id: user.id) {
            logger.info("User activated successfully")
        }
        
    case .failure(let error):
        logger.error("Failed to create user: \(error)")
    }
    
    // Process users
    let allUsers = repository.getAllUsers()
    processUsers(allUsers) { user in
        print("Processing user: \(user.displayName)")
    }
    
    // Use transforms
    for user in allUsers {
        for transform in userTransforms {
            print(transform(user))
        }
    }
}

// Main execution
if CommandLine.argc > 0 {
    exampleUsage()
}

{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift", "filename": "sample.swift", "content_length": 8711, "line_count": 337}, "parsing_results": {"key_structure": "protocol UserRepositoryProtocol {\n ...\n}\n...\nprotocol Validatable {\n ...\n}\n...\nenum UserStatus: String, CaseIterable {\n ...\n}\n...\nenum NetworkError: Error {\n ...\n}\n...\nstruct User: Codable, Validatable {\n    ...\n    mutating func activate() {\n     ...\n    }\n    ...\n    mutating func updateName(_ newName: String) {\n     ...\n    }\n    ...\n    func validate() -> [String] {\n     ...\n    }\n    ...\n    private func isValidEmail(_ email: String) -> Bool {\n     ...\n    }\n}\n...\nclass UserRepository: UserRepositoryProtocol {\n    ...\n    func findUser(by id: Int) -> User? {\n     ...\n    }\n    ...\n    func saveUser(_ user: User) -> Bool {\n     ...\n    }\n    ...\n    func deleteUser(by id: Int) -> <PERSON>ol {\n     ...\n    }\n    ...\n    func getAllUsers() -> [User] {\n     ...\n    }\n}\n...\nclass UserService {\n    ...\n    func createUser(name: String, email: String) -> Result<User, NetworkError> {\n     ...\n    }\n    ...\n    func activateUser(id: Int) -> Bool {\n     ...\n    }\n    ...\n    private func generateUserId() -> Int {\n     ...\n    }\n}\n...\nclass Repository<T: Codable> {\n    ...\n    func save(_ item: T, with key: String) {\n     ...\n    }\n    ...\n    func find(by key: String) -> T? {\n     ...\n    }\n    ...\n    func delete(by key: String) -> Bool {\n     ...\n    }\n    ...\n    func all() -> [T] {\n     ...\n    }\n}\n    ...\n    static func createSampleUser() -> User {\n     ...\n    }\n    ...\n    func toJSON() -> String? {\n     ...\n    }\n...\nclass Logger {\n    enum Level: String {\n     ...\n    }\n    ...\n    func info(_ message: String) {\n     ...\n    }\n    ...\n    func warning(_ message: String) {\n     ...\n    }\n    ...\n    func error(_ message: String) {\n     ...\n    }\n    ...\n    private func log(_ message: String, level: Level) {\n     ...\n    }\n}\n...\nfunc validateEmail(_ email: String) -> Bool {\n ...\n}\n...\nfunc processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {\n ...\n}\n...\nfunc exampleUsage() {\n ...\n}\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift", "start_line": 7, "end_line": 44, "content": "protocol UserRepositoryProtocol {\n    func findUser(by id: Int) -> User?\n    func saveUser(_ user: User) -> Bool\n    func deleteUser(by id: Int) -> Bool\n}\n\nprotocol Validatable {\n    func validate() -> [String]\n    var isValid: Bool { get }\n}\n\n// Enum definitions\nenum UserStatus: String, CaseIterable {\n    case active = \"active\"\n    case inactive = \"inactive\"\n    case suspended = \"suspended\"\n    case pending = \"pending\"\n    \n    var displayName: String {\n        switch self {\n        case .active:\n            return \"Active\"\n        case .inactive:\n            return \"Inactive\"\n        case .suspended:\n            return \"Suspended\"\n        case .pending:\n            return \"Pending Verification\"\n        }\n    }\n}\n\nenum NetworkError: Error {\n    case invalidURL\n    case noData\n    case decodingError(String)\n    case serverError(Int)\n}", "line_count": 38}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift", "start_line": 47, "end_line": 146, "content": "struct User: Codable, Validatable {\n    let id: Int\n    var name: String\n    var email: String\n    var status: UserStatus\n    var avatar: URL?\n    let createdAt: Date\n    var updatedAt: Date\n    \n    // Computed properties\n    var isActive: Bool {\n        return status == .active\n    }\n    \n    var displayName: String {\n        return name.isEmpty ? \"Unknown User\" : name\n    }\n    \n    // Initializers\n    init(id: Int, name: String, email: String, status: UserStatus = .pending) {\n        self.id = id\n        self.name = name\n        self.email = email\n        self.status = status\n        self.avatar = nil\n        self.createdAt = Date()\n        self.updatedAt = Date()\n    }\n    \n    // Methods\n    mutating func activate() {\n        status = .active\n        updatedAt = Date()\n    }\n    \n    mutating func updateName(_ newName: String) {\n        name = newName\n        updatedAt = Date()\n    }\n    \n    // Validation\n    func validate() -> [String] {\n        var errors: [String] = []\n        \n        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {\n            errors.append(\"Name is required\")\n        }\n        \n        if email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {\n            errors.append(\"Email is required\")\n        } else if !isValidEmail(email) {\n            errors.append(\"Invalid email format\")\n        }\n        \n        return errors\n    }\n    \n    var isValid: Bool {\n        return validate().isEmpty\n    }\n    \n    private func isValidEmail(_ email: String) -> Bool {\n        let emailRegex = \"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Za-z]{2,64}\"\n        let emailPredicate = NSPredicate(format:\"SELF MATCHES %@\", emailRegex)\n        return emailPredicate.evaluate(with: email)\n    }\n}\n\n// Class definitions\nclass UserRepository: UserRepositoryProtocol {\n    private var users: [Int: User] = [:]\n    private let queue = DispatchQueue(label: \"userRepository\", attributes: .concurrent)\n    \n    func findUser(by id: Int) -> User? {\n        return queue.sync {\n            return users[id]\n        }\n    }\n    \n    func saveUser(_ user: User) -> Bool {\n        guard user.isValid else { return false }\n        \n        return queue.sync(flags: .barrier) {\n            users[user.id] = user\n            return true\n        }\n    }\n    \n    func deleteUser(by id: Int) -> Bool {\n        return queue.sync(flags: .barrier) {\n            return users.removeValue(forKey: id) != nil\n        }\n    }\n    \n    func getAllUsers() -> [User] {\n        return queue.sync {\n            return Array(users.values)\n        }\n    }\n}", "line_count": 100}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift", "start_line": 148, "end_line": 218, "content": "class UserService {\n    private let repository: UserRepositoryProtocol\n    private let logger: Logger\n    \n    init(repository: UserRepositoryProtocol, logger: Logger = Logger()) {\n        self.repository = repository\n        self.logger = logger\n    }\n    \n    func createUser(name: String, email: String) -> Result<User, NetworkError> {\n        let user = User(id: generateUserId(), name: name, email: email)\n        \n        guard user.isValid else {\n            let errors = user.validate().joined(separator: \", \")\n            logger.error(\"Invalid user data: \\(errors)\")\n            return .failure(.decodingError(\"Invalid user data: \\(errors)\"))\n        }\n        \n        guard repository.saveUser(user) else {\n            logger.error(\"Failed to save user\")\n            return .failure(.serverError(500))\n        }\n        \n        logger.info(\"User created successfully: \\(user.id)\")\n        return .success(user)\n    }\n    \n    func activateUser(id: Int) -> Bool {\n        guard var user = repository.findUser(by: id) else {\n            logger.error(\"User not found: \\(id)\")\n            return false\n        }\n        \n        user.activate()\n        return repository.saveUser(user)\n    }\n    \n    private func generateUserId() -> Int {\n        return Int.random(in: 1...1000000)\n    }\n}\n\n// Generic class\nclass Repository<T: Codable> {\n    private var items: [String: T] = [:]\n    private let queue = DispatchQueue(label: \"repository\", attributes: .concurrent)\n    \n    func save(_ item: T, with key: String) {\n        queue.async(flags: .barrier) {\n            self.items[key] = item\n        }\n    }\n    \n    func find(by key: String) -> T? {\n        return queue.sync {\n            return items[key]\n        }\n    }\n    \n    func delete(by key: String) -> Bool {\n        return queue.sync(flags: .barrier) {\n            return items.removeValue(forKey: key) != nil\n        }\n    }\n    \n    func all() -> [T] {\n        return queue.sync {\n            return Array(items.values)\n        }\n    }\n}", "line_count": 71}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift", "start_line": 222, "end_line": 274, "content": "    static func createSampleUser() -> User {\n        return User(id: 1, name: \"<PERSON>\", email: \"<EMAIL>\")\n    }\n    \n    func toJSON() -> String? {\n        let encoder = JSONEncoder()\n        encoder.dateEncodingStrategy = .iso8601\n        \n        guard let data = try? encoder.encode(self) else { return nil }\n        return String(data: data, encoding: .utf8)\n    }\n}\n\nextension UserStatus {\n    var color: UIColor {\n        switch self {\n        case .active:\n            return .systemGreen\n        case .inactive:\n            return .systemGray\n        case .suspended:\n            return .systemRed\n        case .pending:\n            return .systemOrange\n        }\n    }\n}\n\n// Logger class\nclass Logger {\n    enum Level: String {\n        case info = \"INFO\"\n        case warning = \"WARNING\"\n        case error = \"ERROR\"\n    }\n    \n    func info(_ message: String) {\n        log(message, level: .info)\n    }\n    \n    func warning(_ message: String) {\n        log(message, level: .warning)\n    }\n    \n    func error(_ message: String) {\n        log(message, level: .error)\n    }\n    \n    private func log(_ message: String, level: Level) {\n        let timestamp = DateFormatter.iso8601.string(from: Date())\n        print(\"[\\(timestamp)] [\\(level.rawValue)] \\(message)\")\n    }\n}", "line_count": 53}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift", "start_line": 277, "end_line": 331, "content": "func validateEmail(_ email: String) -> Bool {\n    let emailRegex = \"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Za-z]{2,64}\"\n    let emailPredicate = NSPredicate(format:\"SELF MATCHES %@\", emailRegex)\n    return emailPredicate.evaluate(with: email)\n}\n\nfunc processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {\n    for user in users {\n        transform(user)\n    }\n}\n\n// Closures and higher-order functions\nlet userTransforms: [(User) -> String] = [\n    { user in user.name.uppercased() },\n    { user in user.email.lowercased() },\n    { user in user.status.displayName }\n]\n\n// Example usage\nfunc exampleUsage() {\n    let logger = Logger()\n    let repository = UserRepository()\n    let service = UserService(repository: repository, logger: logger)\n    \n    // Create users\n    let result1 = service.createUser(name: \"<PERSON>\", email: \"<EMAIL>\")\n    let result2 = service.createUser(name: \"<PERSON>\", email: \"<EMAIL>\")\n    \n    switch result1 {\n    case .success(let user):\n        logger.info(\"Created user: \\(user.name)\")\n        \n        // Activate user\n        if service.activateUser(id: user.id) {\n            logger.info(\"User activated successfully\")\n        }\n        \n    case .failure(let error):\n        logger.error(\"Failed to create user: \\(error)\")\n    }\n    \n    // Process users\n    let allUsers = repository.getAllUsers()\n    processUsers(allUsers) { user in\n        print(\"Processing user: \\(user.displayName)\")\n    }\n    \n    // Use transforms\n    for user in allUsers {\n        for transform in userTransforms {\n            print(transform(user))\n        }\n    }\n}", "line_count": 55}], "chunk_count": 5}, "analysis": {"total_lines_in_chunks": 317, "coverage_percentage": 94.06528189910979}}
FileParser 解析结果报告 - RUST
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs
  文件名: sample.rs
  内容长度: 9573 字符
  行数: 380


代码块信息 (5 个):
  块 1: 第 3-54 行 (52 行)
      3: use std::collections::HashMap;
      4: use std::fmt::{<PERSON><PERSON><PERSON>, Form<PERSON><PERSON>, Result as FmtResult};
      5: use std::sync::{Arc, Mutex};
    ... (还有 49 行)

  块 2: 第 56-126 行 (71 行)
     56: impl User {
     57:     // Associated function (constructor)
     58:     fn new(id: UserId, name: UserName, email: String) -> Self {
    ... (还有 68 行)

  块 3: 第 129-220 行 (92 行)
    129: trait Validate {
    130:     fn validate(&self) -> Result<()>;
    131: }
    ... (还有 89 行)

  块 4: 第 223-278 行 (56 行)
    223: fn generate_user_id() -> UserId {
    224:     use std::sync::atomic::{AtomicU64, Ordering};
    225:     static COUNTER: AtomicU64 = AtomicU64::new(1);
    ... (还有 53 行)

  块 5: 第 281-379 行 (99 行)
    281: fn divide(a: f64, b: f64) -> Result<f64> {
    282:     if b == 0.0 {
    283:         Err("Division by zero".into())
    ... (还有 96 行)


统计信息:
  覆盖率: 97.4%
  块中总行数: 370

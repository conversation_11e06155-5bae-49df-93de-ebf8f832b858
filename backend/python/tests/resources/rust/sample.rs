// Rust sample file for testing FileParser functionality.
// This file contains various Rust syntax structures that should be captured by the tree-sitter queries.

use std::collections::HashMap;
use std::fmt::{Display, Formatter, Result as FmtResult};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

// Constants
const MAX_USERS: usize = 1000;
const DEFAULT_TIMEOUT: Duration = Duration::from_secs(30);

// Type aliases
type UserId = u64;
type UserName = String;
type Result<T> = std::result::Result<T, Box<dyn std::error::Error>>;

// Enums
#[derive(Debug, Clone, PartialEq)]
enum UserStatus {
    Active,
    Inactive,
    Suspended { reason: String },
    Pending { verification_code: String },
}

#[derive(Debug)]
enum DatabaseError {
    ConnectionFailed,
    QueryFailed(String),
    NotFound,
}

impl Display for DatabaseError {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        match self {
            DatabaseError::ConnectionFailed => write!(f, "Database connection failed"),
            DatabaseError::QueryFailed(msg) => write!(f, "Query failed: {}", msg),
            DatabaseError::NotFound => write!(f, "Record not found"),
        }
    }
}

impl std::error::Error for DatabaseError {}

// Structs
#[derive(Debug, Clone)]
struct User {
    id: UserId,
    name: UserName,
    email: String,
    status: UserStatus,
    created_at: chrono::DateTime<chrono::Utc>,
}

impl User {
    // Associated function (constructor)
    fn new(id: UserId, name: UserName, email: String) -> Self {
        Self {
            id,
            name,
            email,
            status: UserStatus::Pending {
                verification_code: generate_verification_code(),
            },
            created_at: chrono::Utc::now(),
        }
    }
    
    // Method
    fn activate(&mut self) {
        self.status = UserStatus::Active;
    }
    
    // Method with reference
    fn is_active(&self) -> bool {
        matches!(self.status, UserStatus::Active)
    }
    
    // Method consuming self
    fn into_name(self) -> UserName {
        self.name
    }
}

impl Display for User {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "User(id: {}, name: {}, status: {:?})", self.id, self.name, self.status)
    }
}

// Generic struct
#[derive(Debug)]
struct Repository<T> {
    data: HashMap<UserId, T>,
    max_size: usize,
}

impl<T> Repository<T> {
    fn new(max_size: usize) -> Self {
        Self {
            data: HashMap::new(),
            max_size,
        }
    }
    
    fn insert(&mut self, id: UserId, item: T) -> Result<()> {
        if self.data.len() >= self.max_size {
            return Err("Repository is full".into());
        }
        self.data.insert(id, item);
        Ok(())
    }
    
    fn get(&self, id: &UserId) -> Option<&T> {
        self.data.get(id)
    }
    
    fn remove(&mut self, id: &UserId) -> Option<T> {
        self.data.remove(id)
    }
    
    fn len(&self) -> usize {
        self.data.len()
    }
}

// Traits
trait Validate {
    fn validate(&self) -> Result<()>;
}

trait Serialize {
    fn serialize(&self) -> String;
}

impl Validate for User {
    fn validate(&self) -> Result<()> {
        if self.name.is_empty() {
            return Err("Name cannot be empty".into());
        }
        if !self.email.contains('@') {
            return Err("Invalid email format".into());
        }
        Ok(())
    }
}

impl Serialize for User {
    fn serialize(&self) -> String {
        format!("{}|{}|{}", self.id, self.name, self.email)
    }
}

// Generic trait implementation
impl<T: Clone> Clone for Repository<T> {
    fn clone(&self) -> Self {
        Self {
            data: self.data.clone(),
            max_size: self.max_size,
        }
    }
}

// Service struct with thread-safe operations
#[derive(Clone)]
struct UserService {
    repository: Arc<Mutex<Repository<User>>>,
}

impl UserService {
    fn new() -> Self {
        Self {
            repository: Arc::new(Mutex::new(Repository::new(MAX_USERS))),
        }
    }
    
    fn create_user(&self, name: UserName, email: String) -> Result<UserId> {
        let user_id = generate_user_id();
        let user = User::new(user_id, name, email);
        
        user.validate()?;
        
        let mut repo = self.repository.lock().unwrap();
        repo.insert(user_id, user)?;
        
        Ok(user_id)
    }
    
    fn get_user(&self, id: UserId) -> Option<User> {
        let repo = self.repository.lock().unwrap();
        repo.get(&id).cloned()
    }
    
    fn activate_user(&self, id: UserId) -> Result<()> {
        let mut repo = self.repository.lock().unwrap();
        if let Some(user) = repo.data.get_mut(&id) {
            user.activate();
            Ok(())
        } else {
            Err(DatabaseError::NotFound.into())
        }
    }
    
    // Async method (requires tokio)
    async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {
        let mut users = Vec::new();
        
        for id in user_ids {
            if let Some(user) = self.get_user(id) {
                users.push(user);
            }
            
            // Simulate async work
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
        
        users
    }
}

// Free functions
fn generate_user_id() -> UserId {
    use std::sync::atomic::{AtomicU64, Ordering};
    static COUNTER: AtomicU64 = AtomicU64::new(1);
    COUNTER.fetch_add(1, Ordering::SeqCst)
}

fn generate_verification_code() -> String {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    (0..6).map(|_| rng.gen_range(0..10).to_string()).collect()
}

// Generic function
fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>
where
    F: Fn(&T) -> bool,
{
    items.iter().find(|&item| predicate(item))
}

// Function with lifetime parameters
fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {
    if x.len() > y.len() {
        x
    } else {
        y
    }
}

// Closure examples
fn closure_examples() {
    let numbers = vec![1, 2, 3, 4, 5];
    
    // Closure that captures environment
    let multiplier = 2;
    let doubled: Vec<i32> = numbers.iter().map(|x| x * multiplier).collect();
    
    // Closure with move
    let moved_numbers = numbers.clone();
    let sum_closure = move || moved_numbers.iter().sum::<i32>();
    
    println!("Doubled: {:?}", doubled);
    println!("Sum: {}", sum_closure());
}

// Pattern matching
fn process_user_status(status: &UserStatus) -> String {
    match status {
        UserStatus::Active => "User is active".to_string(),
        UserStatus::Inactive => "User is inactive".to_string(),
        UserStatus::Suspended { reason } => format!("User suspended: {}", reason),
        UserStatus::Pending { verification_code } => {
            format!("User pending verification: {}", verification_code)
        }
    }
}

// Error handling with Result
fn divide(a: f64, b: f64) -> Result<f64> {
    if b == 0.0 {
        Err("Division by zero".into())
    } else {
        Ok(a / b)
    }
}

// Option handling
fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {
    users.iter().find(|user| user.email == email)
}

// Macro definition
macro_rules! log_info {
    ($($arg:tt)*) => {
        println!("[INFO] {}", format!($($arg)*));
    };
}

// Tests module
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_user_creation() {
        let user = User::new(1, "John Doe".to_string(), "<EMAIL>".to_string());
        assert_eq!(user.id, 1);
        assert_eq!(user.name, "John Doe");
        assert!(!user.is_active());
    }
    
    #[test]
    fn test_user_activation() {
        let mut user = User::new(1, "John Doe".to_string(), "<EMAIL>".to_string());
        user.activate();
        assert!(user.is_active());
    }
    
    #[test]
    fn test_repository_operations() {
        let mut repo = Repository::new(10);
        let user = User::new(1, "John Doe".to_string(), "<EMAIL>".to_string());
        
        assert!(repo.insert(1, user).is_ok());
        assert!(repo.get(&1).is_some());
        assert_eq!(repo.len(), 1);
    }
}

// Main function
fn main() -> Result<()> {
    log_info!("Starting user service");
    
    let service = UserService::new();
    
    // Create users
    let user_id1 = service.create_user("Alice".to_string(), "<EMAIL>".to_string())?;
    let user_id2 = service.create_user("Bob".to_string(), "<EMAIL>".to_string())?;
    
    // Activate user
    service.activate_user(user_id1)?;
    
    // Get users
    if let Some(user) = service.get_user(user_id1) {
        println!("User: {}", user);
        println!("Status: {}", process_user_status(&user.status));
    }
    
    // Demonstrate closures
    closure_examples();
    
    // Demonstrate error handling
    match divide(10.0, 2.0) {
        Ok(result) => println!("Division result: {}", result),
        Err(e) => println!("Error: {}", e),
    }
    
    // Thread example
    let handles: Vec<_> = (0..3)
        .map(|i| {
            let service = service.clone();
            thread::spawn(move || {
                let user_id = service
                    .create_user(format!("User{}", i), format!("user{}@example.com", i))
                    .unwrap();
                println!("Created user {} in thread {}", user_id, i);
            })
        })
        .collect();
    
    for handle in handles {
        handle.join().unwrap();
    }
    
    log_info!("User service completed");
    Ok(())
}

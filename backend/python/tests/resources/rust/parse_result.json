{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs", "filename": "sample.rs", "content_length": 9573, "line_count": 380}, "parsing_results": {"key_structure": "enum UserStatus {\n ...\n}\n...\nenum DatabaseError {\n ...\n}\n    ...\n    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {\n     ...\n    }\n...\nstruct User {\n ...\n}\n    ...\n    fn new(id: UserId, name: UserName, email: String) -> Self {\n     ...\n    }\n    ...\n    fn activate(&mut self) {\n     ...\n    }\n    ...\n    fn is_active(&self) -> bool {\n     ...\n    }\n    ...\n    fn into_name(self) -> UserName {\n     ...\n    }\n    ...\n    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {\n     ...\n    }\n...\nstruct Repository<T> {\n ...\n}\n...\nimpl<T> Repository<T> {\n    fn new(max_size: usize) -> Self {\n     ...\n    }\n    ...\n    fn insert(&mut self, id: UserId, item: T) -> Result<()> {\n     ...\n    }\n    ...\n    fn get(&self, id: &UserId) -> Option<&T> {\n     ...\n    }\n    ...\n    fn remove(&mut self, id: &UserId) -> Option<T> {\n     ...\n    }\n    ...\n    fn len(&self) -> usize {\n     ...\n    }\n}\n    ...\n    fn validate(&self) -> Result<()> {\n     ...\n    }\n    ...\n    fn serialize(&self) -> String {\n     ...\n    }\n...\nimpl<T: Clone> Clone for Repository<T> {\n    fn clone(&self) -> Self {\n     ...\n    }\n}\n...\nstruct UserService {\n ...\n}\n    ...\n    fn new() -> Self {\n     ...\n    }\n    ...\n    fn create_user(&self, name: UserName, email: String) -> Result<UserId> {\n     ...\n    }\n    ...\n    fn get_user(&self, id: UserId) -> Option<User> {\n     ...\n    }\n    ...\n    fn activate_user(&self, id: UserId) -> Result<()> {\n     ...\n    }\n    ...\n    async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {\n     ...\n    }\n...\nfn generate_user_id() -> UserId {\n ...\n}\n...\nfn generate_verification_code() -> String {\n ...\n}\n...\nfn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>\n ...\n}\n...\nfn longest<'a>(x: &'a str, y: &'a str) -> &'a str {\n ...\n}\n...\nfn closure_examples() {\n ...\n}\n...\nfn process_user_status(status: &UserStatus) -> String {\n ...\n}\n...\nfn divide(a: f64, b: f64) -> Result<f64> {\n ...\n}\n...\nfn find_user_by_email(users: &[User], email: &str) -> Option<&User> {\n ...\n}\n    ...\n    fn test_user_creation() {\n     ...\n    }\n    ...\n    fn test_user_activation() {\n     ...\n    }\n    ...\n    fn test_repository_operations() {\n     ...\n    }\n...\nfn main() -> Result<()> {\n ...\n}\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs", "start_line": 3, "end_line": 54, "content": "use std::collections::HashMap;\nuse std::fmt::{Display, Formatter, Result as FmtResult};\nuse std::sync::{Arc, Mutex};\nuse std::thread;\nuse std::time::Duration;\n\n// Constants\nconst MAX_USERS: usize = 1000;\nconst DEFAULT_TIMEOUT: Duration = Duration::from_secs(30);\n\n// Type aliases\ntype UserId = u64;\ntype UserName = String;\ntype Result<T> = std::result::Result<T, Box<dyn std::error::Error>>;\n\n// Enums\n#[derive(Debug, Clone, PartialEq)]\nenum UserStatus {\n    Active,\n    Inactive,\n    Suspended { reason: String },\n    Pending { verification_code: String },\n}\n\n#[derive(Debug)]\nenum DatabaseError {\n    ConnectionFailed,\n    QueryFailed(String),\n    NotFound,\n}\n\nimpl Display for DatabaseError {\n    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {\n        match self {\n            DatabaseError::ConnectionFailed => write!(f, \"Database connection failed\"),\n            DatabaseError::QueryFailed(msg) => write!(f, \"Query failed: {}\", msg),\n            DatabaseError::NotFound => write!(f, \"Record not found\"),\n        }\n    }\n}\n\nimpl std::error::Error for DatabaseError {}\n\n// Structs\n#[derive(Debug, Clone)]\nstruct User {\n    id: UserId,\n    name: UserName,\n    email: String,\n    status: UserStatus,\n    created_at: chrono::DateTime<chrono::Utc>,\n}", "line_count": 52}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs", "start_line": 56, "end_line": 126, "content": "impl User {\n    // Associated function (constructor)\n    fn new(id: UserId, name: UserName, email: String) -> Self {\n        Self {\n            id,\n            name,\n            email,\n            status: UserStatus::Pending {\n                verification_code: generate_verification_code(),\n            },\n            created_at: chrono::Utc::now(),\n        }\n    }\n    \n    // Method\n    fn activate(&mut self) {\n        self.status = UserStatus::Active;\n    }\n    \n    // Method with reference\n    fn is_active(&self) -> bool {\n        matches!(self.status, UserStatus::Active)\n    }\n    \n    // Method consuming self\n    fn into_name(self) -> UserName {\n        self.name\n    }\n}\n\nimpl Display for User {\n    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {\n        write!(f, \"User(id: {}, name: {}, status: {:?})\", self.id, self.name, self.status)\n    }\n}\n\n// Generic struct\n#[derive(Debug)]\nstruct Repository<T> {\n    data: HashMap<UserId, T>,\n    max_size: usize,\n}\n\nimpl<T> Repository<T> {\n    fn new(max_size: usize) -> Self {\n        Self {\n            data: HashMap::new(),\n            max_size,\n        }\n    }\n    \n    fn insert(&mut self, id: UserId, item: T) -> Result<()> {\n        if self.data.len() >= self.max_size {\n            return Err(\"Repository is full\".into());\n        }\n        self.data.insert(id, item);\n        Ok(())\n    }\n    \n    fn get(&self, id: &UserId) -> Option<&T> {\n        self.data.get(id)\n    }\n    \n    fn remove(&mut self, id: &UserId) -> Option<T> {\n        self.data.remove(id)\n    }\n    \n    fn len(&self) -> usize {\n        self.data.len()\n    }\n}", "line_count": 71}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs", "start_line": 129, "end_line": 220, "content": "trait Validate {\n    fn validate(&self) -> Result<()>;\n}\n\ntrait Serialize {\n    fn serialize(&self) -> String;\n}\n\nimpl Validate for User {\n    fn validate(&self) -> Result<()> {\n        if self.name.is_empty() {\n            return Err(\"Name cannot be empty\".into());\n        }\n        if !self.email.contains('@') {\n            return Err(\"Invalid email format\".into());\n        }\n        Ok(())\n    }\n}\n\nimpl Serialize for User {\n    fn serialize(&self) -> String {\n        format!(\"{}|{}|{}\", self.id, self.name, self.email)\n    }\n}\n\n// Generic trait implementation\nimpl<T: Clone> Clone for Repository<T> {\n    fn clone(&self) -> Self {\n        Self {\n            data: self.data.clone(),\n            max_size: self.max_size,\n        }\n    }\n}\n\n// Service struct with thread-safe operations\n#[derive(Clone)]\nstruct UserService {\n    repository: Arc<Mutex<Repository<User>>>,\n}\n\nimpl UserService {\n    fn new() -> Self {\n        Self {\n            repository: Arc::new(Mutex::new(Repository::new(MAX_USERS))),\n        }\n    }\n    \n    fn create_user(&self, name: UserName, email: String) -> Result<UserId> {\n        let user_id = generate_user_id();\n        let user = User::new(user_id, name, email);\n        \n        user.validate()?;\n        \n        let mut repo = self.repository.lock().unwrap();\n        repo.insert(user_id, user)?;\n        \n        Ok(user_id)\n    }\n    \n    fn get_user(&self, id: UserId) -> Option<User> {\n        let repo = self.repository.lock().unwrap();\n        repo.get(&id).cloned()\n    }\n    \n    fn activate_user(&self, id: UserId) -> Result<()> {\n        let mut repo = self.repository.lock().unwrap();\n        if let Some(user) = repo.data.get_mut(&id) {\n            user.activate();\n            Ok(())\n        } else {\n            Err(DatabaseError::NotFound.into())\n        }\n    }\n    \n    // Async method (requires tokio)\n    async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {\n        let mut users = Vec::new();\n        \n        for id in user_ids {\n            if let Some(user) = self.get_user(id) {\n                users.push(user);\n            }\n            \n            // Simulate async work\n            tokio::time::sleep(Duration::from_millis(10)).await;\n        }\n        \n        users\n    }\n}", "line_count": 92}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs", "start_line": 223, "end_line": 278, "content": "fn generate_user_id() -> UserId {\n    use std::sync::atomic::{AtomicU64, Ordering};\n    static COUNTER: AtomicU64 = AtomicU64::new(1);\n    COUNTER.fetch_add(1, Ordering::SeqCst)\n}\n\nfn generate_verification_code() -> String {\n    use rand::Rng;\n    let mut rng = rand::thread_rng();\n    (0..6).map(|_| rng.gen_range(0..10).to_string()).collect()\n}\n\n// Generic function\nfn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>\nwhere\n    F: Fn(&T) -> bool,\n{\n    items.iter().find(|&item| predicate(item))\n}\n\n// Function with lifetime parameters\nfn longest<'a>(x: &'a str, y: &'a str) -> &'a str {\n    if x.len() > y.len() {\n        x\n    } else {\n        y\n    }\n}\n\n// Closure examples\nfn closure_examples() {\n    let numbers = vec![1, 2, 3, 4, 5];\n    \n    // Closure that captures environment\n    let multiplier = 2;\n    let doubled: Vec<i32> = numbers.iter().map(|x| x * multiplier).collect();\n    \n    // Closure with move\n    let moved_numbers = numbers.clone();\n    let sum_closure = move || moved_numbers.iter().sum::<i32>();\n    \n    println!(\"Doubled: {:?}\", doubled);\n    println!(\"Sum: {}\", sum_closure());\n}\n\n// Pattern matching\nfn process_user_status(status: &UserStatus) -> String {\n    match status {\n        UserStatus::Active => \"User is active\".to_string(),\n        UserStatus::Inactive => \"User is inactive\".to_string(),\n        UserStatus::Suspended { reason } => format!(\"User suspended: {}\", reason),\n        UserStatus::Pending { verification_code } => {\n            format!(\"User pending verification: {}\", verification_code)\n        }\n    }\n}", "line_count": 56}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/rust/sample.rs", "start_line": 281, "end_line": 379, "content": "fn divide(a: f64, b: f64) -> Result<f64> {\n    if b == 0.0 {\n        Err(\"Division by zero\".into())\n    } else {\n        Ok(a / b)\n    }\n}\n\n// Option handling\nfn find_user_by_email(users: &[User], email: &str) -> Option<&User> {\n    users.iter().find(|user| user.email == email)\n}\n\n// Macro definition\nmacro_rules! log_info {\n    ($($arg:tt)*) => {\n        println!(\"[INFO] {}\", format!($($arg)*));\n    };\n}\n\n// Tests module\n#[cfg(test)]\nmod tests {\n    use super::*;\n    \n    #[test]\n    fn test_user_creation() {\n        let user = User::new(1, \"<PERSON>\".to_string(), \"<EMAIL>\".to_string());\n        assert_eq!(user.id, 1);\n        assert_eq!(user.name, \"<PERSON>\");\n        assert!(!user.is_active());\n    }\n    \n    #[test]\n    fn test_user_activation() {\n        let mut user = User::new(1, \"<PERSON>\".to_string(), \"<EMAIL>\".to_string());\n        user.activate();\n        assert!(user.is_active());\n    }\n    \n    #[test]\n    fn test_repository_operations() {\n        let mut repo = Repository::new(10);\n        let user = User::new(1, \"John Doe\".to_string(), \"<EMAIL>\".to_string());\n        \n        assert!(repo.insert(1, user).is_ok());\n        assert!(repo.get(&1).is_some());\n        assert_eq!(repo.len(), 1);\n    }\n}\n\n// Main function\nfn main() -> Result<()> {\n    log_info!(\"Starting user service\");\n    \n    let service = UserService::new();\n    \n    // Create users\n    let user_id1 = service.create_user(\"Alice\".to_string(), \"<EMAIL>\".to_string())?;\n    let user_id2 = service.create_user(\"Bob\".to_string(), \"<EMAIL>\".to_string())?;\n    \n    // Activate user\n    service.activate_user(user_id1)?;\n    \n    // Get users\n    if let Some(user) = service.get_user(user_id1) {\n        println!(\"User: {}\", user);\n        println!(\"Status: {}\", process_user_status(&user.status));\n    }\n    \n    // Demonstrate closures\n    closure_examples();\n    \n    // Demonstrate error handling\n    match divide(10.0, 2.0) {\n        Ok(result) => println!(\"Division result: {}\", result),\n        Err(e) => println!(\"Error: {}\", e),\n    }\n    \n    // Thread example\n    let handles: Vec<_> = (0..3)\n        .map(|i| {\n            let service = service.clone();\n            thread::spawn(move || {\n                let user_id = service\n                    .create_user(format!(\"User{}\", i), format!(\"user{}@example.com\", i))\n                    .unwrap();\n                println!(\"Created user {} in thread {}\", user_id, i);\n            })\n        })\n        .collect();\n    \n    for handle in handles {\n        handle.join().unwrap();\n    }\n    \n    log_info!(\"User service completed\");\n    Ok(())\n}", "line_count": 99}], "chunk_count": 5}, "analysis": {"total_lines_in_chunks": 370, "coverage_percentage": 97.36842105263158}}
FileParser 解析结果报告 - HTML
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/html/sample.html
  文件名: sample.html
  内容长度: 8420 字符
  行数: 208

关键结构行 (0 个):
  无检测到关键结构

检测到的结构类型:
  无结构类型检测到

代码块信息 (52 个):
  块 1: 第 0-208 行 (209 行)
      0: <!DOCTYPE html>
      1: <html lang="en">
      2: <head>
    ... (还有 205 行)

  块 2: 第 0-50 行 (51 行)
      0: <!DOCTYPE html>
      1: <html lang="en">
      2: <head>
    ... (还有 49 行)

  块 3: 第 50-100 行 (51 行)
     50: ...
     51:                 <div class="about-content">
     52:                     <div class="about-text">
    ... (还有 50 行)

  块 4: 第 100-150 行 (51 行)
    100: ...
    101:                     <div class="form-group">
    102:                         <label for="email">Email:</label>
    ... (还有 50 行)

  块 5: 第 150-200 行 (51 行)
    150: ...
    151:         <div class="container">
    152:             <div class="footer-content">
    ... (还有 50 行)

  块 6: 第 200-208 行 (9 行)
    200: ...
    201:                     }
    202:                 });
    ... (还有 6 行)

  块 7: 第 0-0 行 (1 行)
      0: <!DOCTYPE html>

  块 8: 第 1-207 行 (207 行)
      1: <html lang="en">
      2: <head>
      3:     <meta charset="UTF-8">
    ... (还有 204 行)

  块 9: 第 1-51 行 (51 行)
      1: <html lang="en">
      2: <head>
      3:     <meta charset="UTF-8">
    ... (还有 49 行)

  块 10: 第 51-101 行 (51 行)
     51: ...
     52:                     <div class="about-text">
     53:                         <p>We are a company dedicated to providing excellent services.</p>
    ... (还有 50 行)

  块 11: 第 101-151 行 (51 行)
    101: ...
    102:                         <label for="email">Email:</label>
    103:                         <input type="email" id="email" name="email" required>
    ... (还有 50 行)

  块 12: 第 151-201 行 (51 行)
    151: ...
    152:             <div class="footer-content">
    153:                 <div class="footer-section">
    ... (还有 50 行)

  块 13: 第 201-207 行 (7 行)
    201: ...
    202:                 });
    203:             });
    ... (还有 5 行)

  块 14: 第 1-15 行 (15 行)
      1: <html lang="en">
      2: <head>
      3:     <meta charset="UTF-8">
    ... (还有 12 行)

  块 15: 第 2-14 行 (13 行)
      2: <head>
      3:     <meta charset="UTF-8">
      4:     <meta name="viewport" content="width=device-width, initial-scale=1.0">
    ... (还有 10 行)

  块 16: 第 9-14 行 (6 行)
      9:     <style>
     10:         .inline-style {
     11:             color: red;
    ... (还有 3 行)

  块 17: 第 16-206 行 (191 行)
     16: <body>
     17:     <!-- Header section -->
     18:     <header class="main-header">
    ... (还有 188 行)

  块 18: 第 16-66 行 (51 行)
     16: <body>
     17:     <!-- Header section -->
     18:     <header class="main-header">
    ... (还有 49 行)

  块 19: 第 66-116 行 (51 行)
     66: ...
     67: 
     68:         <!-- Services section -->
    ... (还有 50 行)

  块 20: 第 116-166 行 (51 行)
    116: ...
    117:                     </div>
    118:                     <div class="form-group">
    ... (还有 50 行)

  块 21: 第 166-206 行 (41 行)
    166: ...
    167:                     </ul>
    168:                 </div>
    ... (还有 39 行)

  块 22: 第 16-30 行 (15 行)
     16: <body>
     17:     <!-- Header section -->
     18:     <header class="main-header">
    ... (还有 12 行)

  块 23: 第 18-29 行 (12 行)
     18:     <header class="main-header">
     19:         <nav class="navigation">
     20:             <div class="logo">
    ... (还有 9 行)

  块 24: 第 19-32 行 (14 行)
     19:         <nav class="navigation">
     20:             <div class="logo">
     21:                 <img src="logo.png" alt="Company Logo" width="100" height="50">
    ... (还有 11 行)

  块 25: 第 33-125 行 (93 行)
     33:     <main class="main-content">
     34:         <!-- Hero section -->
     35:         <section id="home" class="hero-section">
    ... (还有 90 行)

  块 26: 第 33-83 行 (51 行)
     33:     <main class="main-content">
     34:         <!-- Hero section -->
     35:         <section id="home" class="hero-section">
    ... (还有 49 行)

  块 27: 第 83-125 行 (43 行)
     83: ...
     84:                         <h3>Consulting</h3>
     85:                         <p>Technical consulting and architecture design for your projects.</p>
    ... (还有 41 行)

  块 28: 第 33-44 行 (12 行)
     33:     <main class="main-content">
     34:         <!-- Hero section -->
     35:         <section id="home" class="hero-section">
    ... (还有 9 行)

  块 29: 第 35-46 行 (12 行)
     35:         <section id="home" class="hero-section">
     36:             <div class="container">
     37:                 <h1 class="hero-title">Welcome to Our Website</h1>
    ... (还有 9 行)

  块 30: 第 47-64 行 (18 行)
     47:         <section id="about" class="about-section">
     48:             <div class="container">
     49:                 <h2>About Us</h2>
    ... (还有 15 行)

  块 31: 第 48-63 行 (16 行)
     48:             <div class="container">
     49:                 <h2>About Us</h2>
     50:                 <div class="about-content">
    ... (还有 13 行)

  块 32: 第 50-62 行 (13 行)
     50:                 <div class="about-content">
     51:                     <div class="about-text">
     52:                         <p>We are a company dedicated to providing excellent services.</p>
    ... (还有 10 行)

  块 33: 第 60-89 行 (30 行)
     60:                     <div class="about-image">
     61:                         <img src="about-us.jpg" alt="About Us" class="responsive-image">
     62:                     </div>
    ... (还有 27 行)

  块 34: 第 68-88 行 (21 行)
     68:         <section id="services" class="services-section">
     69:             <div class="container">
     70:                 <h2>Our Services</h2>
    ... (还有 18 行)

  块 35: 第 69-87 行 (19 行)
     69:             <div class="container">
     70:                 <h2>Our Services</h2>
     71:                 <div class="services-grid">
    ... (还有 16 行)

  块 36: 第 71-81 行 (11 行)
     71:                 <div class="services-grid">
     72:                     <article class="service-card">
     73:                         <h3>Web Development</h3>
    ... (还有 8 行)

  块 37: 第 77-91 行 (15 行)
     77:                     <article class="service-card">
     78:                         <h3>Mobile Apps</h3>
     79:                         <p>Native and cross-platform mobile applications for iOS and Android.</p>
    ... (还有 12 行)

  块 38: 第 92-123 行 (32 行)
     92:         <section id="contact" class="contact-section">
     93:             <div class="container">
     94:                 <h2>Contact Us</h2>
    ... (还有 29 行)

  块 39: 第 93-122 行 (30 行)
     93:             <div class="container">
     94:                 <h2>Contact Us</h2>
     95:                 <form class="contact-form" action="/submit" method="post">
    ... (还有 27 行)

  块 40: 第 95-112 行 (18 行)
     95:                 <form class="contact-form" action="/submit" method="post">
     96:                     <div class="form-group">
     97:                         <label for="name">Name:</label>
    ... (还有 15 行)

  块 41: 第 104-116 行 (13 行)
    104:                     <div class="form-group">
    105:                         <label for="subject">Subject:</label>
    106:                         <select id="subject" name="subject">
    ... (还有 10 行)

  块 42: 第 113-127 行 (15 行)
    113:                     <div class="form-group">
    114:                         <label for="message">Message:</label>
    115:                         <textarea id="message" name="message" rows="5" required></textarea>
    ... (还有 12 行)

  块 43: 第 128-145 行 (18 行)
    128:     <aside class="sidebar">
    129:         <div class="widget">
    130:             <h3>Recent Posts</h3>
    ... (还有 15 行)

  块 44: 第 137-148 行 (12 行)
    137:         <div class="widget">
    138:             <h3>Categories</h3>
    139:             <ul>
    ... (还有 9 行)

  块 45: 第 149-180 行 (32 行)
    149:     <footer class="main-footer">
    150:         <div class="container">
    151:             <div class="footer-content">
    ... (还有 29 行)

  块 46: 第 150-176 行 (27 行)
    150:         <div class="container">
    151:             <div class="footer-content">
    152:                 <div class="footer-section">
    ... (还有 24 行)

  块 47: 第 151-167 行 (17 行)
    151:             <div class="footer-content">
    152:                 <div class="footer-section">
    153:                     <h4>Company</h4>
    ... (还有 14 行)

  块 48: 第 160-175 行 (16 行)
    160:                 <div class="footer-section">
    161:                     <h4>Support</h4>
    162:                     <ul>
    ... (还有 13 行)

  块 49: 第 168-179 行 (12 行)
    168:                 <div class="footer-section">
    169:                     <h4>Follow Us</h4>
    170:                     <div class="social-links">
    ... (还有 9 行)

  块 50: 第 177-204 行 (28 行)
    177:             <div class="footer-bottom">
    178:                 <p>&copy; 2024 Sample Company. All rights reserved.</p>
    179:             </div>
    ... (还有 25 行)

  块 51: 第 184-205 行 (22 行)
    184:     <script>
    185:         function handleClick() {
    186:             alert('Button clicked!');
    ... (还有 19 行)

  块 52: 第 205-205 行 (1 行)
    205:     <script src="main.js"></script>


统计信息:
  覆盖率: 936.5%
  块中总行数: 1948
  结构类型数: 0

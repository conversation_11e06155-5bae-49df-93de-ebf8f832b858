<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HTML sample file for testing FileParser functionality">
    <title>Sample HTML Document</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <style>
        .inline-style {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Header section -->
    <header class="main-header">
        <nav class="navigation">
            <div class="logo">
                <img src="logo.png" alt="Company Logo" width="100" height="50">
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link active">Home</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#services" class="nav-link">Services</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main content -->
    <main class="main-content">
        <!-- Hero section -->
        <section id="home" class="hero-section">
            <div class="container">
                <h1 class="hero-title">Welcome to Our Website</h1>
                <p class="hero-description">
                    This is a sample HTML document containing various HTML elements
                    for testing the FileParser functionality.
                </p>
                <button class="cta-button" onclick="handleClick()">Get Started</button>
            </div>
        </section>

        <!-- About section -->
        <section id="about" class="about-section">
            <div class="container">
                <h2>About Us</h2>
                <div class="about-content">
                    <div class="about-text">
                        <p>We are a company dedicated to providing excellent services.</p>
                        <p>Our team consists of experienced professionals who are passionate about their work.</p>
                        <ul>
                            <li>Quality service</li>
                            <li>Customer satisfaction</li>
                            <li>Innovation</li>
                        </ul>
                    </div>
                    <div class="about-image">
                        <img src="about-us.jpg" alt="About Us" class="responsive-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Services section -->
        <section id="services" class="services-section">
            <div class="container">
                <h2>Our Services</h2>
                <div class="services-grid">
                    <article class="service-card">
                        <h3>Web Development</h3>
                        <p>We create modern, responsive websites using the latest technologies.</p>
                        <a href="#" class="service-link">Learn More</a>
                    </article>
                    <article class="service-card">
                        <h3>Mobile Apps</h3>
                        <p>Native and cross-platform mobile applications for iOS and Android.</p>
                        <a href="#" class="service-link">Learn More</a>
                    </article>
                    <article class="service-card">
                        <h3>Consulting</h3>
                        <p>Technical consulting and architecture design for your projects.</p>
                        <a href="#" class="service-link">Learn More</a>
                    </article>
                </div>
            </div>
        </section>

        <!-- Contact section -->
        <section id="contact" class="contact-section">
            <div class="container">
                <h2>Contact Us</h2>
                <form class="contact-form" action="/submit" method="post">
                    <div class="form-group">
                        <label for="name">Name:</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject:</label>
                        <select id="subject" name="subject">
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="support">Support</option>
                            <option value="sales">Sales</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="message">Message:</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="newsletter" name="newsletter">
                        <label for="newsletter">Subscribe to our newsletter</label>
                    </div>
                    <button type="submit" class="submit-button">Send Message</button>
                </form>
            </div>
        </section>
    </main>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="widget">
            <h3>Recent Posts</h3>
            <ul>
                <li><a href="#">How to Build Better Websites</a></li>
                <li><a href="#">The Future of Web Development</a></li>
                <li><a href="#">Mobile-First Design Principles</a></li>
            </ul>
        </div>
        <div class="widget">
            <h3>Categories</h3>
            <ul>
                <li><a href="#">Web Development</a></li>
                <li><a href="#">Mobile Apps</a></li>
                <li><a href="#">Design</a></li>
                <li><a href="#">Technology</a></li>
            </ul>
        </div>
    </aside>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <div class="social-links">
                        <a href="#" class="social-link">Facebook</a>
                        <a href="#" class="social-link">Twitter</a>
                        <a href="#" class="social-link">LinkedIn</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Sample Company. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        function handleClick() {
            alert('Button clicked!');
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded');
            
            // Add smooth scrolling
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
    </script>
    <script src="main.js"></script>
</body>
</html>

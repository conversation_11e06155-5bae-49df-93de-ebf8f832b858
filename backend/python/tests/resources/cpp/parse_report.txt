FileParser 解析结果报告 - CPP
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp
  文件名: sample.cpp
  内容长度: 7645 字符
  行数: 282


代码块信息 (7 个):
  块 1: 第 5-11 行 (7 行)
      5: #include <iostream>
      6: #include <vector>
      7: #include <string>
    ... (还有 3 行)

  块 2: 第 13-143 行 (131 行)
     13: namespace geometry {
     14:     
     15:     // Class declaration
    ... (还有 128 行)

  块 3: 第 13-113 行 (101 行)
     13: namespace geometry {
     14:     
     15:     // Class declaration
    ... (还有 100 行)

  块 4: 第 113-143 行 (31 行)
    113: namespace geometry {
    114: ...
    115:     
    ... (还有 30 行)

  块 5: 第 146-196 行 (51 行)
    146: template<typename T>
    147: class Container {
    148: private:
    ... (还有 48 行)

  块 6: 第 201-254 行 (54 行)
    201: T max(const T& a, const T& b) {
    202:     return (a > b) ? a : b;
    203: }
    ... (还有 51 行)

  块 7: 第 257-281 行 (25 行)
    257: int main() {
    258:     using namespace geometry;
    259:     
    ... (还有 22 行)


统计信息:
  覆盖率: 141.8%
  块中总行数: 400

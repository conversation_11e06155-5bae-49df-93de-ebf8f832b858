/**
 * C++ sample file for testing FileParser functionality.
 * This file contains various C++ syntax structures that should be captured by the tree-sitter queries.
 */

#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <algorithm>
#include <functional>

// Namespace declaration
namespace geometry {
    
    // Class declaration
    class Point {
    private:
        double x_, y_;
        
    public:
        // Constructors
        Point() : x_(0.0), y_(0.0) {}
        Point(double x, double y) : x_(x), y_(y) {}
        Point(const Point& other) : x_(other.x_), y_(other.y_) {}
        
        // Destructor
        ~Point() = default;
        
        // Assignment operator
        Point& operator=(const Point& other) {
            if (this != &other) {
                x_ = other.x_;
                y_ = other.y_;
            }
            return *this;
        }
        
        // Move constructor
        Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {
            other.x_ = 0.0;
            other.y_ = 0.0;
        }
        
        // Move assignment operator
        Point& operator=(Point&& other) noexcept {
            if (this != &other) {
                x_ = other.x_;
                y_ = other.y_;
                other.x_ = 0.0;
                other.y_ = 0.0;
            }
            return *this;
        }
        
        // Getters
        double getX() const { return x_; }
        double getY() const { return y_; }
        
        // Setters
        void setX(double x) { x_ = x; }
        void setY(double y) { y_ = y; }
        
        // Operator overloading
        Point operator+(const Point& other) const {
            return Point(x_ + other.x_, y_ + other.y_);
        }
        
        Point& operator+=(const Point& other) {
            x_ += other.x_;
            y_ += other.y_;
            return *this;
        }
        
        bool operator==(const Point& other) const {
            return x_ == other.x_ && y_ == other.y_;
        }
        
        // Friend function
        friend std::ostream& operator<<(std::ostream& os, const Point& p) {
            os << "(" << p.x_ << ", " << p.y_ << ")";
            return os;
        }
        
        // Static method
        static double distance(const Point& p1, const Point& p2) {
            double dx = p1.x_ - p2.x_;
            double dy = p1.y_ - p2.y_;
            return std::sqrt(dx * dx + dy * dy);
        }
    };
    
    // Abstract base class
    class Shape {
    protected:
        std::string color_;
        
    public:
        Shape(const std::string& color) : color_(color) {}
        virtual ~Shape() = default;
        
        // Pure virtual functions
        virtual double area() const = 0;
        virtual double perimeter() const = 0;
        
        // Virtual function
        virtual void draw() const {
            std::cout << "Drawing a " << color_ << " shape" << std::endl;
        }
        
        // Getter
        const std::string& getColor() const { return color_; }
    };
    
    // Derived class
    class Circle : public Shape {
    private:
        Point center_;
        double radius_;
        
    public:
        Circle(const Point& center, double radius, const std::string& color)
            : Shape(color), center_(center), radius_(radius) {}
        
        // Override virtual functions
        double area() const override {
            return 3.14159 * radius_ * radius_;
        }
        
        double perimeter() const override {
            return 2 * 3.14159 * radius_;
        }
        
        void draw() const override {
            std::cout << "Drawing a " << color_ << " circle at " << center_ 
                      << " with radius " << radius_ << std::endl;
        }
        
        // Getters
        const Point& getCenter() const { return center_; }
        double getRadius() const { return radius_; }
    };
    
} // namespace geometry

// Template class
template<typename T>
class Container {
private:
    std::vector<T> data_;
    
public:
    // Constructor
    Container() = default;
    Container(std::initializer_list<T> init) : data_(init) {}
    
    // Template member function
    template<typename U>
    void add(U&& item) {
        data_.emplace_back(std::forward<U>(item));
    }
    
    // Iterator support
    auto begin() { return data_.begin(); }
    auto end() { return data_.end(); }
    auto begin() const { return data_.begin(); }
    auto end() const { return data_.end(); }
    
    // Size and access
    size_t size() const { return data_.size(); }
    T& operator[](size_t index) { return data_[index]; }
    const T& operator[](size_t index) const { return data_[index]; }
    
    // Template method with constraints (C++20)
    template<typename Predicate>
    auto filter(Predicate pred) const {
        Container<T> result;
        std::copy_if(data_.begin(), data_.end(), 
                     std::back_inserter(result.data_), pred);
        return result;
    }
};

// Template specialization
template<>
class Container<bool> {
private:
    std::vector<bool> data_;
    
public:
    void add(bool value) {
        data_.push_back(value);
    }
    
    size_t size() const { return data_.size(); }
    
    bool operator[](size_t index) const { return data_[index]; }
};

// Function templates
template<typename T>
T max(const T& a, const T& b) {
    return (a > b) ? a : b;
}

template<typename T, typename U>
auto add(const T& a, const U& b) -> decltype(a + b) {
    return a + b;
}

// Variadic template
template<typename... Args>
void print(Args... args) {
    ((std::cout << args << " "), ...);
    std::cout << std::endl;
}

// Lambda expressions and modern C++ features
void modernCppFeatures() {
    // Lambda expressions
    auto lambda1 = [](int x) { return x * x; };
    auto lambda2 = [](const auto& a, const auto& b) { return a + b; };
    
    // Capture by value and reference
    int multiplier = 10;
    auto lambda3 = [multiplier](int x) { return x * multiplier; };
    auto lambda4 = [&multiplier](int x) { multiplier += x; return multiplier; };
    
    // Generic lambda (C++14)
    auto generic_lambda = [](auto x, auto y) { return x + y; };
    
    // Smart pointers
    auto unique_ptr = std::make_unique<geometry::Point>(1.0, 2.0);
    auto shared_ptr = std::make_shared<geometry::Circle>(*unique_ptr, 5.0, "red");
    
    // Range-based for loop
    std::vector<int> numbers = {1, 2, 3, 4, 5};
    for (const auto& num : numbers) {
        std::cout << num << " ";
    }
    std::cout << std::endl;
    
    // Auto type deduction
    auto result = add(3.14, 2);
    
    // Structured bindings (C++17)
    auto [x, y] = std::make_pair(10, 20);
    
    // std::function
    std::function<int(int, int)> operation = [](int a, int b) { return a + b; };
    
    // Algorithm usage
    std::transform(numbers.begin(), numbers.end(), numbers.begin(),
                   [](int x) { return x * 2; });
}

// Main function
int main() {
    using namespace geometry;
    
    // Object creation
    Point p1(1.0, 2.0);
    Point p2(3.0, 4.0);
    
    // Method calls
    Point p3 = p1 + p2;
    std::cout << "p1 + p2 = " << p3 << std::endl;
    
    // Polymorphism
    std::unique_ptr<Shape> shape = std::make_unique<Circle>(p1, 5.0, "blue");
    shape->draw();
    std::cout << "Area: " << shape->area() << std::endl;
    
    // Template usage
    Container<int> int_container{1, 2, 3, 4, 5};
    Container<std::string> string_container{"hello", "world"};
    
    // Modern C++ features
    modernCppFeatures();
    
    return 0;
}

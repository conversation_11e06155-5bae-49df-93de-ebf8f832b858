FileParser 解析结果报告 - PYTHON
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py
  文件名: sample.py
  内容长度: 3946 字符
  行数: 169


代码块信息 (3 个):
  块 1: 第 6-62 行 (57 行)
      6: import os
      7: import sys
      8: from typing import List, Dict, Optional
    ... (还有 54 行)

  块 2: 第 65-124 行 (60 行)
     65: def simple_function(x: int, y: int = 10) -> int:
     66:     """A simple function with type annotations."""
     67:     return x + y
    ... (还有 57 行)

  块 3: 第 127-161 行 (35 行)
    127: def function_with_global_nonlocal():
    128:     """Function demonstrating global and nonlocal statements."""
    129:     global global_var
    ... (还有 32 行)


统计信息:
  覆盖率: 89.9%
  块中总行数: 152

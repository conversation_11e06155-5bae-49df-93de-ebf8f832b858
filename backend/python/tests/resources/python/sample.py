#!/usr/bin/env python3
"""
Python sample file for testing FileParser functionality.
This file contains various Python syntax structures that should be captured by the tree-sitter queries.
"""

import os
import sys
from typing import List, Dict, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod


# Global variable with type annotation
global_var: int = 42


@dataclass
class Person:
    """A simple dataclass representing a person."""
    name: str
    age: int
    email: Optional[str] = None
    
    def __post_init__(self):
        if self.age < 0:
            raise ValueError("Age cannot be negative")


class Animal(ABC):
    """Abstract base class for animals."""
    
    def __init__(self, name: str, species: str):
        self.name = name
        self.species = species
    
    @abstractmethod
    def make_sound(self) -> str:
        """Abstract method that must be implemented by subclasses."""
        pass
    
    @property
    def description(self) -> str:
        return f"{self.name} is a {self.species}"


class Dog(Animal):
    """Concrete implementation of Animal for dogs."""
    
    def __init__(self, name: str, breed: str):
        super().__init__(name, "dog")
        self.breed = breed
    
    def make_sound(self) -> str:
        return "Woof!"
    
    @staticmethod
    def is_good_boy() -> bool:
        return True
    
    @classmethod
    def create_puppy(cls, name: str, breed: str) -> 'Dog':
        return cls(name, breed)


def simple_function(x: int, y: int = 10) -> int:
    """A simple function with type annotations."""
    return x + y


async def async_function(data: List[str]) -> Dict[str, int]:
    """An async function example."""
    result = {}
    for item in data:
        result[item] = len(item)
    return result


def generator_function(n: int):
    """A generator function that yields numbers."""
    for i in range(n):
        yield i * 2


@property
def decorated_function():
    """A function with a decorator."""
    return "decorated"


# Lambda expressions
square = lambda x: x ** 2
add_numbers = lambda a, b: a + b

# List comprehension
numbers = [x for x in range(10) if x % 2 == 0]

# Dictionary comprehension
word_lengths = {word: len(word) for word in ["hello", "world", "python"]}

# Set comprehension
unique_squares = {x**2 for x in range(5)}


def function_with_context_managers():
    """Function demonstrating with statements."""
    with open("test.txt", "r") as file:
        content = file.read()
    
    with open("output.txt", "w") as output:
        output.write(content)


def function_with_exception_handling():
    """Function demonstrating try/except statements."""
    try:
        result = 10 / 0
    except ZeroDivisionError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
    else:
        print("No error occurred")
    finally:
        print("Cleanup code")


def function_with_global_nonlocal():
    """Function demonstrating global and nonlocal statements."""
    global global_var
    
    def inner_function():
        nonlocal local_var
        local_var = 100
    
    local_var = 50
    inner_function()
    global_var = 999


# Match case statement (Python 3.10+)
def match_case_example(value):
    """Function demonstrating match/case statements."""
    match value:
        case 1:
            return "one"
        case 2 | 3:
            return "two or three"
        case x if x > 10:
            return "greater than ten"
        case _:
            return "something else"


# Type annotations
def typed_function(
    param1: str,
    param2: List[int],
    param3: Dict[str, Optional[float]]
) -> Tuple[str, int]:
    """Function with complex type annotations."""
    return param1, len(param2)


if __name__ == "__main__":
    # Main execution block
    dog = Dog("Buddy", "Golden Retriever")
    print(dog.make_sound())
    print(Dog.is_good_boy())

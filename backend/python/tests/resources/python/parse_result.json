{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "filename": "sample.py", "content_length": 3946, "line_count": 169}, "parsing_results": {"key_structure": "@dataclass\nclass Person:\n    ...\n    def __post_init__(self):\n                                                      ...\n            raise ValueError(\"Age cannot be negative\")\n...\nclass Animal(ABC):\n    ...\n    def __init__(self, name: str, species: str):\n                              ...\n        self.species = species\n    ...\n    @abstractmethod\n    def make_sound(self) -> str:\n            ...\n        pass\n    ...\n    @property\n    def description(self) -> str:\n        return f\"{self.name} is a {self.species}\"\n...\nclass Dog(Animal):\n    ...\n    def __init__(self, name: str, breed: str):\n                          ...\n        self.breed = breed\n    ...\n    def make_sound(self) -> str:\n        return \"Woof!\"\n    ...\n    @staticmethod\n    def is_good_boy() -> bool:\n        return True\n    ...\n    @classmethod\n    def create_puppy(cls, name: str, breed: str) -> 'Dog':\n        return cls(name, breed)\n...\ndef simple_function(x: int, y: int = 10) -> int:\n                ...\n    return x + y\n...\nasync def async_function(data: List[str]) -> Dict[str, int]:\n                 ...\n    return result\n...\ndef generator_function(n: int):\n                   ...\n        yield i * 2\n...\n@property\ndef decorated_function():\n                      ...\n    return \"decorated\"\n...\ndef function_with_context_managers():\n                             ...\n        output.write(content)\n...\ndef function_with_exception_handling():\n                             ...\n        print(\"Cleanup code\")\n...\ndef function_with_global_nonlocal():\n    ...\n    def inner_function():\n                       ...\n        local_var = 100\n                    ...\n    global_var = 999\n...\ndef match_case_example(value):\n                                   ...\n            return \"something else\"\n...\ndef typed_function(\n                              ...\n    return param1, len(param2)\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "start_line": 6, "end_line": 62, "content": "import os\nimport sys\nfrom typing import List, Dict, Optional\nfrom dataclasses import dataclass\nfrom abc import ABC, abstractmethod\n\n\n# Global variable with type annotation\nglobal_var: int = 42\n\n\n@dataclass\nclass Person:\n    \"\"\"A simple dataclass representing a person.\"\"\"\n    name: str\n    age: int\n    email: Optional[str] = None\n    \n    def __post_init__(self):\n        if self.age < 0:\n            raise ValueError(\"Age cannot be negative\")\n\n\nclass Animal(ABC):\n    \"\"\"Abstract base class for animals.\"\"\"\n    \n    def __init__(self, name: str, species: str):\n        self.name = name\n        self.species = species\n    \n    @abstractmethod\n    def make_sound(self) -> str:\n        \"\"\"Abstract method that must be implemented by subclasses.\"\"\"\n        pass\n    \n    @property\n    def description(self) -> str:\n        return f\"{self.name} is a {self.species}\"\n\n\nclass Dog(Animal):\n    \"\"\"Concrete implementation of Animal for dogs.\"\"\"\n    \n    def __init__(self, name: str, breed: str):\n        super().__init__(name, \"dog\")\n        self.breed = breed\n    \n    def make_sound(self) -> str:\n        return \"Woof!\"\n    \n    @staticmethod\n    def is_good_boy() -> bool:\n        return True\n    \n    @classmethod\n    def create_puppy(cls, name: str, breed: str) -> 'Dog':\n        return cls(name, breed)", "line_count": 57}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "start_line": 65, "end_line": 124, "content": "def simple_function(x: int, y: int = 10) -> int:\n    \"\"\"A simple function with type annotations.\"\"\"\n    return x + y\n\n\nasync def async_function(data: List[str]) -> Dict[str, int]:\n    \"\"\"An async function example.\"\"\"\n    result = {}\n    for item in data:\n        result[item] = len(item)\n    return result\n\n\ndef generator_function(n: int):\n    \"\"\"A generator function that yields numbers.\"\"\"\n    for i in range(n):\n        yield i * 2\n\n\n@property\ndef decorated_function():\n    \"\"\"A function with a decorator.\"\"\"\n    return \"decorated\"\n\n\n# Lambda expressions\nsquare = lambda x: x ** 2\nadd_numbers = lambda a, b: a + b\n\n# List comprehension\nnumbers = [x for x in range(10) if x % 2 == 0]\n\n# Dictionary comprehension\nword_lengths = {word: len(word) for word in [\"hello\", \"world\", \"python\"]}\n\n# Set comprehension\nunique_squares = {x**2 for x in range(5)}\n\n\ndef function_with_context_managers():\n    \"\"\"Function demonstrating with statements.\"\"\"\n    with open(\"test.txt\", \"r\") as file:\n        content = file.read()\n    \n    with open(\"output.txt\", \"w\") as output:\n        output.write(content)\n\n\ndef function_with_exception_handling():\n    \"\"\"Function demonstrating try/except statements.\"\"\"\n    try:\n        result = 10 / 0\n    except ZeroDivisionError as e:\n        print(f\"Error: {e}\")\n    except Exception as e:\n        print(f\"Unexpected error: {e}\")\n    else:\n        print(\"No error occurred\")\n    finally:\n        print(\"Cleanup code\")", "line_count": 60}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "start_line": 127, "end_line": 161, "content": "def function_with_global_nonlocal():\n    \"\"\"Function demonstrating global and nonlocal statements.\"\"\"\n    global global_var\n    \n    def inner_function():\n        nonlocal local_var\n        local_var = 100\n    \n    local_var = 50\n    inner_function()\n    global_var = 999\n\n\n# Match case statement (Python 3.10+)\ndef match_case_example(value):\n    \"\"\"Function demonstrating match/case statements.\"\"\"\n    match value:\n        case 1:\n            return \"one\"\n        case 2 | 3:\n            return \"two or three\"\n        case x if x > 10:\n            return \"greater than ten\"\n        case _:\n            return \"something else\"\n\n\n# Type annotations\ndef typed_function(\n    param1: str,\n    param2: List[int],\n    param3: Dict[str, Optional[float]]\n) -> Tuple[str, int]:\n    \"\"\"Function with complex type annotations.\"\"\"\n    return param1, len(param2)", "line_count": 35}], "chunk_count": 3}, "analysis": {"total_lines_in_chunks": 152, "coverage_percentage": 89.94082840236686}}
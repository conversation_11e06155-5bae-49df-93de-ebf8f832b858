# Ruby sample file for testing FileParser functionality.
# This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.

require 'json'
require 'time'
require 'logger'

# Constants
MAX_USERS = 1000
DEFAULT_ROLE = 'user'

# Module definitions
module Validatable
  def valid?
    validate.empty?
  end
  
  def validate
    []
  end
end

module Timestampable
  attr_accessor :created_at, :updated_at
  
  def initialize_timestamps
    @created_at = Time.now
    @updated_at = Time.now
  end
  
  def touch
    @updated_at = Time.now
  end
end

# Class definitions
class User
  include Validatable
  include Timestampable
  
  attr_reader :id, :name, :email, :status, :roles
  attr_accessor :avatar
  
  def initialize(name, email, status: :pending)
    @name = name
    @email = email
    @status = status
    @roles = [DEFAULT_ROLE]
    @avatar = nil
    initialize_timestamps
  end
  
  # Getters and setters
  def name=(new_name)
    @name = new_name
    touch
  end
  
  def email=(new_email)
    @email = new_email
    touch
  end
  
  def status=(new_status)
    @status = new_status
    touch
  end
  
  # Role management
  def add_role(role)
    @roles << role unless @roles.include?(role)
    touch
    self
  end
  
  def remove_role(role)
    @roles.delete(role)
    touch
    self
  end
  
  def has_role?(role)
    @roles.include?(role)
  end
  
  # Status methods
  def active?
    @status == :active
  end
  
  def activate!
    @status = :active
    touch
    self
  end
  
  def suspend!
    @status = :suspended
    touch
    self
  end
  
  # Validation
  def validate
    errors = []
    errors << 'Name is required' if @name.nil? || @name.empty?
    errors << 'Email is required' if @email.nil? || @email.empty?
    errors << 'Invalid email format' unless valid_email?
    errors
  end
  
  # Serialization
  def to_hash
    {
      id: @id,
      name: @name,
      email: @email,
      status: @status,
      roles: @roles,
      avatar: @avatar,
      created_at: @created_at,
      updated_at: @updated_at
    }
  end
  
  def to_json(*args)
    to_hash.to_json(*args)
  end
  
  # String representation
  def to_s
    "#{@name} <#{@email}>"
  end
  
  def inspect
    "#<User:#{object_id} @name=\"#{@name}\" @email=\"#{@email}\" @status=#{@status}>"
  end
  
  private
  
  def valid_email?
    @email =~ /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
  end
end

# Inheritance example
class AdminUser < User
  def initialize(name, email)
    super(name, email, status: :active)
    add_role('admin')
  end
  
  def admin?
    true
  end
  
  def can_manage_users?
    true
  end
end

# Repository class
class UserRepository
  def initialize(logger = nil)
    @users = {}
    @next_id = 1
    @logger = logger || Logger.new(STDOUT)
  end
  
  def save(user)
    if user.valid?
      user.instance_variable_set(:@id, @next_id) unless user.id
      @users[@next_id] = user
      @next_id += 1
      @logger.info("User saved: #{user}")
      true
    else
      @logger.error("Invalid user: #{user.validate.join(', ')}")
      false
    end
  end
  
  def find(id)
    @users[id]
  end
  
  def find_by_email(email)
    @users.values.find { |user| user.email == email }
  end
  
  def all
    @users.values
  end
  
  def delete(id)
    user = @users.delete(id)
    @logger.info("User deleted: #{user}") if user
    user
  end
  
  def count
    @users.size
  end
end

# Service class
class UserService
  def initialize(repository, logger = nil)
    @repository = repository
    @logger = logger || Logger.new(STDOUT)
  end
  
  def create_user(name, email, admin: false)
    user = admin ? AdminUser.new(name, email) : User.new(name, email)
    
    if @repository.save(user)
      @logger.info("User created: #{user.id}")
      user
    else
      @logger.error("Failed to create user: #{user.validate.join(', ')}")
      nil
    end
  end
  
  def activate_user(id)
    user = @repository.find(id)
    return false unless user
    
    user.activate!
    @repository.save(user)
  end
  
  def find_active_users
    @repository.all.select(&:active?)
  end
  
  def user_stats
    users = @repository.all
    {
      total: users.size,
      active: users.count(&:active?),
      admins: users.count { |u| u.is_a?(AdminUser) }
    }
  end
end

# Singleton pattern
class Configuration
  include Singleton
  
  attr_accessor :max_users, :default_role, :log_level
  
  def initialize
    @max_users = MAX_USERS
    @default_role = DEFAULT_ROLE
    @log_level = :info
  end
  
  def self.configure
    yield(instance) if block_given?
  end
end

# Utility functions
def generate_random_string(length = 10)
  chars = ('a'..'z').to_a + ('A'..'Z').to_a + ('0'..'9').to_a
  Array.new(length) { chars.sample }.join
end

def validate_email(email)
  email =~ /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
end

# Proc and lambda examples
multiply = proc { |a, b| a * b }
add = lambda { |a, b| a + b }
square = ->(x) { x * x }

# Block examples
def process_users(users, &block)
  users.each(&block) if block_given?
end

def with_timing
  start_time = Time.now
  result = yield if block_given?
  end_time = Time.now
  puts "Execution time: #{end_time - start_time} seconds"
  result
end

# Method with keyword arguments
def create_user_with_options(name:, email:, admin: false, roles: [])
  user = admin ? AdminUser.new(name, email) : User.new(name, email)
  roles.each { |role| user.add_role(role) }
  user
end

# Class with class methods
class UserFactory
  class << self
    def create_admin(name, email)
      AdminUser.new(name, email)
    end
    
    def create_regular(name, email)
      User.new(name, email)
    end
    
    def create_batch(users_data)
      users_data.map do |data|
        create_regular(data[:name], data[:email])
      end
    end
  end
end

# Exception handling
class UserError < StandardError; end
class UserNotFoundError < UserError; end
class InvalidUserError < UserError; end

def find_user_safely(repository, id)
  user = repository.find(id)
  raise UserNotFoundError, "User with ID #{id} not found" unless user
  user
rescue UserNotFoundError => e
  puts "Error: #{e.message}"
  nil
end

# Example usage
if __FILE__ == $0
  logger = Logger.new(STDOUT)
  repository = UserRepository.new(logger)
  service = UserService.new(repository, logger)
  
  # Create users
  user1 = service.create_user('Alice', '<EMAIL>')
  user2 = service.create_user('Bob', '<EMAIL>', admin: true)
  
  # Activate user
  service.activate_user(user1.id) if user1
  
  # Process users with block
  process_users(repository.all) do |user|
    puts "Processing user: #{user}"
  end
  
  # Timing example
  with_timing do
    sleep(0.1)
    puts "Some operation completed"
  end
  
  # Stats
  stats = service.user_stats
  puts "User statistics: #{stats}"
  
  # Configuration
  Configuration.configure do |config|
    config.max_users = 500
    config.log_level = :debug
  end
  
  puts "Configuration: max_users = #{Configuration.instance.max_users}"
end

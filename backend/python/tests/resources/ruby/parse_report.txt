FileParser 解析结果报告 - RUBY
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb
  文件名: sample.rb
  内容长度: 7040 字符
  行数: 367

关键结构行 (55 个):
  第  13 行: name.definition.method: def valid?
           内容: def valid?
  第  17 行: name.definition.method: def validate
           内容: def validate
  第  25 行: name.definition.method: def initialize_timestamps
           内容: def initialize_timestamps
  第  30 行: name.definition.method: def touch
           内容: def touch
  第  36 行: name.definition.class: class User
           内容: class User
  第  43 行: name.definition.method: def initialize(name, email, status: :pending)
           内容: def initialize(name, email, status: :pending)
  第  53 行: definition.method_all: def name=(new_name)
           内容: def name=(new_name)
  第  58 行: definition.method_all: def email=(new_email)
           内容: def email=(new_email)
  第  63 行: definition.method_all: def status=(new_status)
           内容: def status=(new_status)
  第  69 行: name.definition.method: def add_role(role)
           内容: def add_role(role)
  第  75 行: name.definition.method: def remove_role(role)
           内容: def remove_role(role)
  第  81 行: name.definition.method: def has_role?(role)
           内容: def has_role?(role)
  第  86 行: name.definition.method: def active?
           内容: def active?
  第  90 行: name.definition.method: def activate!
           内容: def activate!
  第  96 行: name.definition.method: def suspend!
           内容: def suspend!
  第 103 行: name.definition.method: def validate
           内容: def validate
  第 112 行: name.definition.method: def to_hash
           内容: def to_hash
  第 125 行: name.definition.method: def to_json(*args)
           内容: def to_json(*args)
  第 130 行: name.definition.method: def to_s
           内容: def to_s
  第 134 行: name.definition.method: def inspect
           内容: def inspect
  第 140 行: name.definition.method: def valid_email?
           内容: def valid_email?
  第 146 行: name.definition.class: class AdminUser < User
           内容: class AdminUser < User
  第 147 行: name.definition.method: def initialize(name, email)
           内容: def initialize(name, email)
  第 152 行: name.definition.method: def admin?
           内容: def admin?
  第 156 行: name.definition.method: def can_manage_users?
           内容: def can_manage_users?
  第 162 行: name.definition.class: class UserRepository
           内容: class UserRepository
  第 163 行: name.definition.method: def initialize(logger = nil)
           内容: def initialize(logger = nil)
  第 169 行: name.definition.method: def save(user)
           内容: def save(user)
  第 182 行: name.definition.method: def find(id)
           内容: def find(id)
  第 186 行: name.definition.method: def find_by_email(email)
           内容: def find_by_email(email)
  第 190 行: name.definition.method: def all
           内容: def all
  第 194 行: name.definition.method: def delete(id)
           内容: def delete(id)
  第 200 行: name.definition.method: def count
           内容: def count
  第 206 行: name.definition.class: class UserService
           内容: class UserService
  第 207 行: name.definition.method: def initialize(repository, logger = nil)
           内容: def initialize(repository, logger = nil)
  第 212 行: name.definition.method: def create_user(name, email, admin: false)
           内容: def create_user(name, email, admin: false)
  第 224 行: name.definition.method: def activate_user(id)
           内容: def activate_user(id)
  第 232 行: name.definition.method: def find_active_users
           内容: def find_active_users
  第 236 行: name.definition.method: def user_stats
           内容: def user_stats
  第 247 行: name.definition.class: class Configuration
           内容: class Configuration
  第 252 行: name.definition.method: def initialize
           内容: def initialize
  第 258 行: name.definition.method: def self.configure
           内容: def self.configure
  第 264 行: name.definition.method: def generate_random_string(length = 10)
           内容: def generate_random_string(length = 10)
  第 269 行: name.definition.method: def validate_email(email)
           内容: def validate_email(email)
  第 279 行: name.definition.method: def process_users(users, &block)
           内容: def process_users(users, &block)
  第 283 行: name.definition.method: def with_timing
           内容: def with_timing
  第 292 行: name.definition.method: def create_user_with_options(name:, email:, admin: false, roles: [])
           内容: def create_user_with_options(name:, email:, admin: false, roles: [])
  第 299 行: name.definition.class: class UserFactory
           内容: class UserFactory
  第 301 行: name.definition.method: def create_admin(name, email)
           内容: def create_admin(name, email)
  第 305 行: name.definition.method: def create_regular(name, email)
           内容: def create_regular(name, email)
  第 309 行: name.definition.method: def create_batch(users_data)
           内容: def create_batch(users_data)
  第 318 行: name.definition.class: class UserError < StandardError; end
           内容: class UserError < StandardError; end
  第 319 行: name.definition.class: class UserNotFoundError < UserError; end
           内容: class UserNotFoundError < UserError; end
  第 320 行: name.definition.class: class InvalidUserError < UserError; end
           内容: class InvalidUserError < UserError; end
  第 322 行: name.definition.method: def find_user_safely(repository, id)
           内容: def find_user_safely(repository, id)

检测到的结构类型:
  - definition.method_all: def email=(new_email): 1 个
  - definition.method_all: def name=(new_name): 1 个
  - definition.method_all: def status=(new_status): 1 个
  - name.definition.class: class AdminUser < User: 1 个
  - name.definition.class: class Configuration: 1 个
  - name.definition.class: class InvalidUserError < UserError; end: 1 个
  - name.definition.class: class User: 1 个
  - name.definition.class: class UserError < StandardError; end: 1 个
  - name.definition.class: class UserFactory: 1 个
  - name.definition.class: class UserNotFoundError < UserError; end: 1 个
  - name.definition.class: class UserRepository: 1 个
  - name.definition.class: class UserService: 1 个
  - name.definition.method: def activate!: 1 个
  - name.definition.method: def activate_user(id): 1 个
  - name.definition.method: def active?: 1 个
  - name.definition.method: def add_role(role): 1 个
  - name.definition.method: def admin?: 1 个
  - name.definition.method: def all: 1 个
  - name.definition.method: def can_manage_users?: 1 个
  - name.definition.method: def count: 1 个
  - name.definition.method: def create_admin(name, email): 1 个
  - name.definition.method: def create_batch(users_data): 1 个
  - name.definition.method: def create_regular(name, email): 1 个
  - name.definition.method: def create_user(name, email, admin: false): 1 个
  - name.definition.method: def create_user_with_options(name:, email:, admin: false, roles: []): 1 个
  - name.definition.method: def delete(id): 1 个
  - name.definition.method: def find(id): 1 个
  - name.definition.method: def find_active_users: 1 个
  - name.definition.method: def find_by_email(email): 1 个
  - name.definition.method: def find_user_safely(repository, id): 1 个
  - name.definition.method: def generate_random_string(length = 10): 1 个
  - name.definition.method: def has_role?(role): 1 个
  - name.definition.method: def initialize: 1 个
  - name.definition.method: def initialize(logger = nil): 1 个
  - name.definition.method: def initialize(name, email): 1 个
  - name.definition.method: def initialize(name, email, status: :pending): 1 个
  - name.definition.method: def initialize(repository, logger = nil): 1 个
  - name.definition.method: def initialize_timestamps: 1 个
  - name.definition.method: def inspect: 1 个
  - name.definition.method: def process_users(users, &block): 1 个
  - name.definition.method: def remove_role(role): 1 个
  - name.definition.method: def save(user): 1 个
  - name.definition.method: def self.configure: 1 个
  - name.definition.method: def suspend!: 1 个
  - name.definition.method: def to_hash: 1 个
  - name.definition.method: def to_json(*args): 1 个
  - name.definition.method: def to_s: 1 个
  - name.definition.method: def touch: 1 个
  - name.definition.method: def user_stats: 1 个
  - name.definition.method: def valid?: 1 个
  - name.definition.method: def valid_email?: 1 个
  - name.definition.method: def validate: 2 个
  - name.definition.method: def validate_email(email): 1 个
  - name.definition.method: def with_timing: 1 个

代码块信息 (55 个):
  块 1: 第 0-367 行 (368 行)
      0: # Ruby sample file for testing FileParser functionality.
      1: # This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.
      2: 
    ... (还有 364 行)

  块 2: 第 0-50 行 (51 行)
      0: # Ruby sample file for testing FileParser functionality.
      1: # This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.
      2: 
    ... (还有 49 行)

  块 3: 第 50-100 行 (51 行)
     50: ...
     51:   end
     52:   
    ... (还有 50 行)

  块 4: 第 100-150 行 (51 行)
    100: ...
    101:   end
    102:   
    ... (还有 50 行)

  块 5: 第 150-200 行 (51 行)
    150: ...
    151:   end
    152:   
    ... (还有 50 行)

  块 6: 第 200-250 行 (51 行)
    200: ...
    201:   def count
    202:     @users.size
    ... (还有 50 行)

  块 7: 第 250-300 行 (51 行)
    250: ...
    251:   attr_accessor :max_users, :default_role, :log_level
    252:   
    ... (还有 50 行)

  块 8: 第 300-350 行 (51 行)
    300: ...
    301:   class << self
    302:     def create_admin(name, email)
    ... (还有 50 行)

  块 9: 第 350-367 行 (18 行)
    350: ...
    351:   with_timing do
    352:     sleep(0.1)
    ... (还有 15 行)

  块 10: 第 0-11 行 (12 行)
      0: # Ruby sample file for testing FileParser functionality.
      1: # This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.
      2: 
    ... (还有 9 行)

  块 11: 第 12-33 行 (22 行)
     12: module Validatable
     13:   def valid?
     14:     validate.empty?
    ... (还有 19 行)

  块 12: 第 22-32 行 (11 行)
     22: module Timestampable
     23:   attr_accessor :created_at, :updated_at
     24:   
    ... (还有 8 行)

  块 13: 第 30-30 行 (1 行)
     30:   def touch

  块 14: 第 36-143 行 (108 行)
     36: class User
     37:   include Validatable
     38:   include Timestampable
    ... (还有 105 行)

  块 15: 第 36-86 行 (51 行)
     36: class User
     37:   include Validatable
     38:   include Timestampable
    ... (还有 49 行)

  块 16: 第 86-136 行 (51 行)
     86: ...
     87:   def active?
     88:     @status == :active
    ... (还有 50 行)

  块 17: 第 136-143 行 (8 行)
    136: ...
    137:   end
    138:   
    ... (还有 6 行)

  块 18: 第 36-50 行 (15 行)
     36: class User
     37:   include Validatable
     38:   include Timestampable
    ... (还有 12 行)

  块 19: 第 43-56 行 (14 行)
     43:   def initialize(name, email, status: :pending)
     44:     @name = name
     45:     @email = email
    ... (还有 11 行)

  块 20: 第 54-66 行 (13 行)
     54:     @name = new_name
     55:     touch
     56:   end
    ... (还有 10 行)

  块 21: 第 64-79 行 (16 行)
     64:     @status = new_status
     65:     touch
     66:   end
    ... (还有 13 行)

  块 22: 第 75-85 行 (11 行)
     75:   def remove_role(role)
     76:     @roles.delete(role)
     77:     touch
    ... (还有 8 行)

  块 23: 第 86-100 行 (15 行)
     86:   def active?
     87:     @status == :active
     88:   end
    ... (还有 12 行)

  块 24: 第 96-109 行 (14 行)
     96:   def suspend!
     97:     @status = :suspended
     98:     touch
    ... (还有 11 行)

  块 25: 第 103-123 行 (21 行)
    103:   def validate
    104:     errors = []
    105:     errors << 'Name is required' if @name.nil? || @name.empty?
    ... (还有 18 行)

  块 26: 第 112-122 行 (11 行)
    112:   def to_hash
    113:     {
    114:       id: @id,
    ... (还有 8 行)

  块 27: 第 114-127 行 (14 行)
    114:       id: @id,
    115:       name: @name,
    116:       email: @email,
    ... (还有 11 行)

  块 28: 第 125-136 行 (12 行)
    125:   def to_json(*args)
    126:     to_hash.to_json(*args)
    127:   end
    ... (还有 9 行)

  块 29: 第 134-145 行 (12 行)
    134:   def inspect
    135:     "#<User:#{object_id} @name=\"#{@name}\" @email=\"#{@email}\" @status=#{@status}>"
    136:   end
    ... (还有 9 行)

  块 30: 第 146-158 行 (13 行)
    146: class AdminUser < User
    147:   def initialize(name, email)
    148:     super(name, email, status: :active)
    ... (还有 10 行)

  块 31: 第 156-203 行 (48 行)
    156:   def can_manage_users?
    157:     true
    158:   end
    ... (还有 45 行)

  块 32: 第 162-180 行 (19 行)
    162: class UserRepository
    163:   def initialize(logger = nil)
    164:     @users = {}
    ... (还有 16 行)

  块 33: 第 169-184 行 (16 行)
    169:   def save(user)
    170:     if user.valid?
    171:       user.instance_variable_set(:@id, @next_id) unless user.id
    ... (还有 13 行)

  块 34: 第 182-192 行 (11 行)
    182:   def find(id)
    183:     @users[id]
    184:   end
    ... (还有 8 行)

  块 35: 第 190-202 行 (13 行)
    190:   def all
    191:     @users.values
    192:   end
    ... (还有 10 行)

  块 36: 第 200-244 行 (45 行)
    200:   def count
    201:     @users.size
    202:   end
    ... (还有 42 行)

  块 37: 第 206-222 行 (17 行)
    206: class UserService
    207:   def initialize(repository, logger = nil)
    208:     @repository = repository
    ... (还有 14 行)

  块 38: 第 212-230 行 (19 行)
    212:   def create_user(name, email, admin: false)
    213:     user = admin ? AdminUser.new(name, email) : User.new(name, email)
    214:     
    ... (还有 16 行)

  块 39: 第 224-234 行 (11 行)
    224:   def activate_user(id)
    225:     user = @repository.find(id)
    226:     return false unless user
    ... (还有 8 行)

  块 40: 第 232-243 行 (12 行)
    232:   def find_active_users
    233:     @repository.all.select(&:active?)
    234:   end
    ... (还有 9 行)

  块 41: 第 236-246 行 (11 行)
    236:   def user_stats
    237:     users = @repository.all
    238:     {
    ... (还有 8 行)

  块 42: 第 247-260 行 (14 行)
    247: class Configuration
    248:   include Singleton
    249:   
    ... (还有 11 行)

  块 43: 第 258-271 行 (14 行)
    258:   def self.configure
    259:     yield(instance) if block_given?
    260:   end
    ... (还有 11 行)

  块 44: 第 269-281 行 (13 行)
    269: def validate_email(email)
    270:   email =~ /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
    271: end
    ... (还有 10 行)

  块 45: 第 279-289 行 (11 行)
    279: def process_users(users, &block)
    280:   users.each(&block) if block_given?
    281: end
    ... (还有 8 行)

  块 46: 第 283-296 行 (14 行)
    283: def with_timing
    284:   start_time = Time.now
    285:   result = yield if block_given?
    ... (还有 11 行)

  块 47: 第 292-315 行 (24 行)
    292: def create_user_with_options(name:, email:, admin: false, roles: [])
    293:   user = admin ? AdminUser.new(name, email) : User.new(name, email)
    294:   roles.each { |role| user.add_role(role) }
    ... (还有 21 行)

  块 48: 第 299-314 行 (16 行)
    299: class UserFactory
    300:   class << self
    301:     def create_admin(name, email)
    ... (还有 13 行)

  块 49: 第 301-313 行 (13 行)
    301:     def create_admin(name, email)
    302:       AdminUser.new(name, email)
    303:     end
    ... (还有 10 行)

  块 50: 第 309-319 行 (11 行)
    309:     def create_batch(users_data)
    310:       users_data.map do |data|
    311:         create_regular(data[:name], data[:email])
    ... (还有 8 行)

  块 51: 第 319-329 行 (11 行)
    319: class UserNotFoundError < UserError; end
    320: class InvalidUserError < UserError; end
    321: 
    ... (还有 8 行)

  块 52: 第 322-332 行 (11 行)
    322: def find_user_safely(repository, id)
    323:   user = repository.find(id)
    324:   raise UserNotFoundError, "User with ID #{id} not found" unless user
    ... (还有 8 行)

  块 53: 第 337-347 行 (11 行)
    337:   # Create users
    338:   user1 = service.create_user('Alice', '<EMAIL>')
    339:   user2 = service.create_user('Bob', '<EMAIL>', admin: true)
    ... (还有 8 行)

  块 54: 第 349-359 行 (11 行)
    349:   # Timing example
    350:   with_timing do
    351:     sleep(0.1)
    ... (还有 8 行)

  块 55: 第 360-363 行 (4 行)
    360:   Configuration.configure do |config|
    361:     config.max_users = 500
    362:     config.log_level = :debug
    ... (还有 1 行)


统计信息:
  覆盖率: 432.7%
  块中总行数: 1588
  结构类型数: 54

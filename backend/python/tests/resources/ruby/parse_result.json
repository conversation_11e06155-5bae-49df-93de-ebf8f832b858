{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "filename": "sample.rb", "content_length": 7040, "line_count": 367}, "parsing_results": {"key_structure_lines": {"13": "name.definition.method: def valid?", "17": "name.definition.method: def validate", "25": "name.definition.method: def initialize_timestamps", "30": "name.definition.method: def touch", "36": "name.definition.class: class User", "43": "name.definition.method: def initialize(name, email, status: :pending)", "53": "definition.method_all: def name=(new_name)", "58": "definition.method_all: def email=(new_email)", "63": "definition.method_all: def status=(new_status)", "69": "name.definition.method: def add_role(role)", "75": "name.definition.method: def remove_role(role)", "81": "name.definition.method: def has_role?(role)", "86": "name.definition.method: def active?", "90": "name.definition.method: def activate!", "96": "name.definition.method: def suspend!", "103": "name.definition.method: def validate", "112": "name.definition.method: def to_hash", "125": "name.definition.method: def to_json(*args)", "130": "name.definition.method: def to_s", "134": "name.definition.method: def inspect", "140": "name.definition.method: def valid_email?", "146": "name.definition.class: class AdminUser < User", "147": "name.definition.method: def initialize(name, email)", "152": "name.definition.method: def admin?", "156": "name.definition.method: def can_manage_users?", "162": "name.definition.class: class UserRepository", "163": "name.definition.method: def initialize(logger = nil)", "169": "name.definition.method: def save(user)", "182": "name.definition.method: def find(id)", "186": "name.definition.method: def find_by_email(email)", "190": "name.definition.method: def all", "194": "name.definition.method: def delete(id)", "200": "name.definition.method: def count", "206": "name.definition.class: class UserService", "207": "name.definition.method: def initialize(repository, logger = nil)", "212": "name.definition.method: def create_user(name, email, admin: false)", "224": "name.definition.method: def activate_user(id)", "232": "name.definition.method: def find_active_users", "236": "name.definition.method: def user_stats", "247": "name.definition.class: class Configuration", "252": "name.definition.method: def initialize", "258": "name.definition.method: def self.configure", "264": "name.definition.method: def generate_random_string(length = 10)", "269": "name.definition.method: def validate_email(email)", "279": "name.definition.method: def process_users(users, &block)", "283": "name.definition.method: def with_timing", "292": "name.definition.method: def create_user_with_options(name:, email:, admin: false, roles: [])", "299": "name.definition.class: class UserFactory", "301": "name.definition.method: def create_admin(name, email)", "305": "name.definition.method: def create_regular(name, email)", "309": "name.definition.method: def create_batch(users_data)", "318": "name.definition.class: class UserError < StandardError; end", "319": "name.definition.class: class UserNotFoundError < UserError; end", "320": "name.definition.class: class InvalidUserError < UserError; end", "322": "name.definition.method: def find_user_safely(repository, id)"}, "key_structure_count": 55, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 0, "end_line": 367, "content": "# Ruby sample file for testing FileParser functionality.\n# This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.\n\nrequire 'json'\nrequire 'time'\nrequire 'logger'\n\n# Constants\nMAX_USERS = 1000\nDEFAULT_ROLE = 'user'\n\n# Module definitions\nmodule Validatable\n  def valid?\n    validate.empty?\n  end\n  \n  def validate\n    []\n  end\nend\n\nmodule Timestampable\n  attr_accessor :created_at, :updated_at\n  \n  def initialize_timestamps\n    @created_at = Time.now\n    @updated_at = Time.now\n  end\n  \n  def touch\n    @updated_at = Time.now\n  end\nend\n\n# Class definitions\nclass User\n  include Validatable\n  include Timestampable\n  \n  attr_reader :id, :name, :email, :status, :roles\n  attr_accessor :avatar\n  \n  def initialize(name, email, status: :pending)\n    @name = name\n    @email = email\n    @status = status\n    @roles = [DEFAULT_ROLE]\n    @avatar = nil\n    initialize_timestamps\n  end\n  \n  # Getters and setters\n  def name=(new_name)\n    @name = new_name\n    touch\n  end\n  \n  def email=(new_email)\n    @email = new_email\n    touch\n  end\n  \n  def status=(new_status)\n    @status = new_status\n    touch\n  end\n  \n  # Role management\n  def add_role(role)\n    @roles << role unless @roles.include?(role)\n    touch\n    self\n  end\n  \n  def remove_role(role)\n    @roles.delete(role)\n    touch\n    self\n  end\n  \n  def has_role?(role)\n    @roles.include?(role)\n  end\n  \n  # Status methods\n  def active?\n    @status == :active\n  end\n  \n  def activate!\n    @status = :active\n    touch\n    self\n  end\n  \n  def suspend!\n    @status = :suspended\n    touch\n    self\n  end\n  \n  # Validation\n  def validate\n    errors = []\n    errors << 'Name is required' if @name.nil? || @name.empty?\n    errors << 'Email is required' if @email.nil? || @email.empty?\n    errors << 'Invalid email format' unless valid_email?\n    errors\n  end\n  \n  # Serialization\n  def to_hash\n    {\n      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }\n  end\n  \n  def to_json(*args)\n    to_hash.to_json(*args)\n  end\n  \n  # String representation\n  def to_s\n    \"#{@name} <#{@email}>\"\n  end\n  \n  def inspect\n    \"#<User:#{object_id} @name=\\\"#{@name}\\\" @email=\\\"#{@email}\\\" @status=#{@status}>\"\n  end\n  \n  private\n  \n  def valid_email?\n    @email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\n  end\nend\n\n# Inheritance example\nclass AdminUser < User\n  def initialize(name, email)\n    super(name, email, status: :active)\n    add_role('admin')\n  end\n  \n  def admin?\n    true\n  end\n  \n  def can_manage_users?\n    true\n  end\nend\n\n# Repository class\nclass UserRepository\n  def initialize(logger = nil)\n    @users = {}\n    @next_id = 1\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def save(user)\n    if user.valid?\n      user.instance_variable_set(:@id, @next_id) unless user.id\n      @users[@next_id] = user\n      @next_id += 1\n      @logger.info(\"User saved: #{user}\")\n      true\n    else\n      @logger.error(\"Invalid user: #{user.validate.join(', ')}\")\n      false\n    end\n  end\n  \n  def find(id)\n    @users[id]\n  end\n  \n  def find_by_email(email)\n    @users.values.find { |user| user.email == email }\n  end\n  \n  def all\n    @users.values\n  end\n  \n  def delete(id)\n    user = @users.delete(id)\n    @logger.info(\"User deleted: #{user}\") if user\n    user\n  end\n  \n  def count\n    @users.size\n  end\nend\n\n# Service class\nclass UserService\n  def initialize(repository, logger = nil)\n    @repository = repository\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def create_user(name, email, admin: false)\n    user = admin ? AdminUser.new(name, email) : User.new(name, email)\n    \n    if @repository.save(user)\n      @logger.info(\"User created: #{user.id}\")\n      user\n    else\n      @logger.error(\"Failed to create user: #{user.validate.join(', ')}\")\n      nil\n    end\n  end\n  \n  def activate_user(id)\n    user = @repository.find(id)\n    return false unless user\n    \n    user.activate!\n    @repository.save(user)\n  end\n  \n  def find_active_users\n    @repository.all.select(&:active?)\n  end\n  \n  def user_stats\n    users = @repository.all\n    {\n      total: users.size,\n      active: users.count(&:active?),\n      admins: users.count { |u| u.is_a?(AdminUser) }\n    }\n  end\nend\n\n# Singleton pattern\nclass Configuration\n  include Singleton\n  \n  attr_accessor :max_users, :default_role, :log_level\n  \n  def initialize\n    @max_users = MAX_USERS\n    @default_role = DEFAULT_ROLE\n    @log_level = :info\n  end\n  \n  def self.configure\n    yield(instance) if block_given?\n  end\nend\n\n# Utility functions\ndef generate_random_string(length = 10)\n  chars = ('a'..'z').to_a + ('A'..'Z').to_a + ('0'..'9').to_a\n  Array.new(length) { chars.sample }.join\nend\n\ndef validate_email(email)\n  email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\nend\n\n# Proc and lambda examples\nmultiply = proc { |a, b| a * b }\nadd = lambda { |a, b| a + b }\nsquare = ->(x) { x * x }\n\n# Block examples\ndef process_users(users, &block)\n  users.each(&block) if block_given?\nend\n\ndef with_timing\n  start_time = Time.now\n  result = yield if block_given?\n  end_time = Time.now\n  puts \"Execution time: #{end_time - start_time} seconds\"\n  result\nend\n\n# Method with keyword arguments\ndef create_user_with_options(name:, email:, admin: false, roles: [])\n  user = admin ? AdminUser.new(name, email) : User.new(name, email)\n  roles.each { |role| user.add_role(role) }\n  user\nend\n\n# Class with class methods\nclass UserFactory\n  class << self\n    def create_admin(name, email)\n      AdminUser.new(name, email)\n    end\n    \n    def create_regular(name, email)\n      User.new(name, email)\n    end\n    \n    def create_batch(users_data)\n      users_data.map do |data|\n        create_regular(data[:name], data[:email])\n      end\n    end\n  end\nend\n\n# Exception handling\nclass UserError < StandardError; end\nclass UserNotFoundError < UserError; end\nclass InvalidUserError < UserError; end\n\ndef find_user_safely(repository, id)\n  user = repository.find(id)\n  raise UserNotFoundError, \"User with ID #{id} not found\" unless user\n  user\nrescue UserNotFoundError => e\n  puts \"Error: #{e.message}\"\n  nil\nend\n\n# Example usage\nif __FILE__ == $0\n  logger = Logger.new(STDOUT)\n  repository = UserRepository.new(logger)\n  service = UserService.new(repository, logger)\n  \n  # Create users\n  user1 = service.create_user('Alice', '<EMAIL>')\n  user2 = service.create_user('Bob', '<EMAIL>', admin: true)\n  \n  # Activate user\n  service.activate_user(user1.id) if user1\n  \n  # Process users with block\n  process_users(repository.all) do |user|\n    puts \"Processing user: #{user}\"\n  end\n  \n  # Timing example\n  with_timing do\n    sleep(0.1)\n    puts \"Some operation completed\"\n  end\n  \n  # Stats\n  stats = service.user_stats\n  puts \"User statistics: #{stats}\"\n  \n  # Configuration\n  Configuration.configure do |config|\n    config.max_users = 500\n    config.log_level = :debug\n  end\n  \n  puts \"Configuration: max_users = #{Configuration.instance.max_users}\"\nend", "line_count": 368}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 0, "end_line": 50, "content": "# Ruby sample file for testing FileParser functionality.\n# This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.\n\nrequire 'json'\nrequire 'time'\nrequire 'logger'\n\n# Constants\nMAX_USERS = 1000\nDEFAULT_ROLE = 'user'\n\n# Module definitions\nmodule Validatable\n  def valid?\n    validate.empty?\n  end\n  \n  def validate\n    []\n  end\nend\n\nmodule Timestampable\n  attr_accessor :created_at, :updated_at\n  \n  def initialize_timestamps\n    @created_at = Time.now\n    @updated_at = Time.now\n  end\n  \n  def touch\n    @updated_at = Time.now\n  end\nend\n\n# Class definitions\nclass User\n  include Validatable\n  include Timestampable\n  \n  attr_reader :id, :name, :email, :status, :roles\n  attr_accessor :avatar\n  \n  def initialize(name, email, status: :pending)\n    @name = name\n    @email = email\n    @status = status\n    @roles = [DEFAULT_ROLE]\n    @avatar = nil\n    initialize_timestamps\n  end\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 50, "end_line": 100, "content": "...\n  end\n  \n  # Getters and setters\n  def name=(new_name)\n    @name = new_name\n    touch\n  end\n  \n  def email=(new_email)\n    @email = new_email\n    touch\n  end\n  \n  def status=(new_status)\n    @status = new_status\n    touch\n  end\n  \n  # Role management\n  def add_role(role)\n    @roles << role unless @roles.include?(role)\n    touch\n    self\n  end\n  \n  def remove_role(role)\n    @roles.delete(role)\n    touch\n    self\n  end\n  \n  def has_role?(role)\n    @roles.include?(role)\n  end\n  \n  # Status methods\n  def active?\n    @status == :active\n  end\n  \n  def activate!\n    @status = :active\n    touch\n    self\n  end\n  \n  def suspend!\n    @status = :suspended\n    touch\n    self\n  end\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 100, "end_line": 150, "content": "...\n  end\n  \n  # Validation\n  def validate\n    errors = []\n    errors << 'Name is required' if @name.nil? || @name.empty?\n    errors << 'Email is required' if @email.nil? || @email.empty?\n    errors << 'Invalid email format' unless valid_email?\n    errors\n  end\n  \n  # Serialization\n  def to_hash\n    {\n      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }\n  end\n  \n  def to_json(*args)\n    to_hash.to_json(*args)\n  end\n  \n  # String representation\n  def to_s\n    \"#{@name} <#{@email}>\"\n  end\n  \n  def inspect\n    \"#<User:#{object_id} @name=\\\"#{@name}\\\" @email=\\\"#{@email}\\\" @status=#{@status}>\"\n  end\n  \n  private\n  \n  def valid_email?\n    @email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\n  end\nend\n\n# Inheritance example\nclass AdminUser < User\n  def initialize(name, email)\n    super(name, email, status: :active)\n    add_role('admin')\n  end\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 150, "end_line": 200, "content": "...\n  end\n  \n  def admin?\n    true\n  end\n  \n  def can_manage_users?\n    true\n  end\nend\n\n# Repository class\nclass UserRepository\n  def initialize(logger = nil)\n    @users = {}\n    @next_id = 1\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def save(user)\n    if user.valid?\n      user.instance_variable_set(:@id, @next_id) unless user.id\n      @users[@next_id] = user\n      @next_id += 1\n      @logger.info(\"User saved: #{user}\")\n      true\n    else\n      @logger.error(\"Invalid user: #{user.validate.join(', ')}\")\n      false\n    end\n  end\n  \n  def find(id)\n    @users[id]\n  end\n  \n  def find_by_email(email)\n    @users.values.find { |user| user.email == email }\n  end\n  \n  def all\n    @users.values\n  end\n  \n  def delete(id)\n    user = @users.delete(id)\n    @logger.info(\"User deleted: #{user}\") if user\n    user\n  end\n  \n  def count\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 200, "end_line": 250, "content": "...\n  def count\n    @users.size\n  end\nend\n\n# Service class\nclass UserService\n  def initialize(repository, logger = nil)\n    @repository = repository\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def create_user(name, email, admin: false)\n    user = admin ? AdminUser.new(name, email) : User.new(name, email)\n    \n    if @repository.save(user)\n      @logger.info(\"User created: #{user.id}\")\n      user\n    else\n      @logger.error(\"Failed to create user: #{user.validate.join(', ')}\")\n      nil\n    end\n  end\n  \n  def activate_user(id)\n    user = @repository.find(id)\n    return false unless user\n    \n    user.activate!\n    @repository.save(user)\n  end\n  \n  def find_active_users\n    @repository.all.select(&:active?)\n  end\n  \n  def user_stats\n    users = @repository.all\n    {\n      total: users.size,\n      active: users.count(&:active?),\n      admins: users.count { |u| u.is_a?(AdminUser) }\n    }\n  end\nend\n\n# Singleton pattern\nclass Configuration\n  include Singleton\n  \n  attr_accessor :max_users, :default_role, :log_level\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 250, "end_line": 300, "content": "...\n  attr_accessor :max_users, :default_role, :log_level\n  \n  def initialize\n    @max_users = MAX_USERS\n    @default_role = DEFAULT_ROLE\n    @log_level = :info\n  end\n  \n  def self.configure\n    yield(instance) if block_given?\n  end\nend\n\n# Utility functions\ndef generate_random_string(length = 10)\n  chars = ('a'..'z').to_a + ('A'..'Z').to_a + ('0'..'9').to_a\n  Array.new(length) { chars.sample }.join\nend\n\ndef validate_email(email)\n  email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\nend\n\n# Proc and lambda examples\nmultiply = proc { |a, b| a * b }\nadd = lambda { |a, b| a + b }\nsquare = ->(x) { x * x }\n\n# Block examples\ndef process_users(users, &block)\n  users.each(&block) if block_given?\nend\n\ndef with_timing\n  start_time = Time.now\n  result = yield if block_given?\n  end_time = Time.now\n  puts \"Execution time: #{end_time - start_time} seconds\"\n  result\nend\n\n# Method with keyword arguments\ndef create_user_with_options(name:, email:, admin: false, roles: [])\n  user = admin ? AdminUser.new(name, email) : User.new(name, email)\n  roles.each { |role| user.add_role(role) }\n  user\nend\n\n# Class with class methods\nclass UserFactory\n  class << self\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 300, "end_line": 350, "content": "...\n  class << self\n    def create_admin(name, email)\n      AdminUser.new(name, email)\n    end\n    \n    def create_regular(name, email)\n      User.new(name, email)\n    end\n    \n    def create_batch(users_data)\n      users_data.map do |data|\n        create_regular(data[:name], data[:email])\n      end\n    end\n  end\nend\n\n# Exception handling\nclass UserError < StandardError; end\nclass UserNotFoundError < UserError; end\nclass InvalidUserError < UserError; end\n\ndef find_user_safely(repository, id)\n  user = repository.find(id)\n  raise UserNotFoundError, \"User with ID #{id} not found\" unless user\n  user\nrescue UserNotFoundError => e\n  puts \"Error: #{e.message}\"\n  nil\nend\n\n# Example usage\nif __FILE__ == $0\n  logger = Logger.new(STDOUT)\n  repository = UserRepository.new(logger)\n  service = UserService.new(repository, logger)\n  \n  # Create users\n  user1 = service.create_user('Alice', '<EMAIL>')\n  user2 = service.create_user('Bob', '<EMAIL>', admin: true)\n  \n  # Activate user\n  service.activate_user(user1.id) if user1\n  \n  # Process users with block\n  process_users(repository.all) do |user|\n    puts \"Processing user: #{user}\"\n  end\n  \n  # Timing example\n  with_timing do\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 350, "end_line": 367, "content": "...\n  with_timing do\n    sleep(0.1)\n    puts \"Some operation completed\"\n  end\n  \n  # Stats\n  stats = service.user_stats\n  puts \"User statistics: #{stats}\"\n  \n  # Configuration\n  Configuration.configure do |config|\n    config.max_users = 500\n    config.log_level = :debug\n  end\n  \n  puts \"Configuration: max_users = #{Configuration.instance.max_users}\"\nend", "line_count": 18}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 0, "end_line": 11, "content": "# Ruby sample file for testing FileParser functionality.\n# This file contains various Ruby syntax structures that should be captured by the tree-sitter queries.\n\nrequire 'json'\nrequire 'time'\nrequire 'logger'\n\n# Constants\nMAX_USERS = 1000\nDEFAULT_ROLE = 'user'\n\n# Module definitions", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 12, "end_line": 33, "content": "module Validatable\n  def valid?\n    validate.empty?\n  end\n  \n  def validate\n    []\n  end\nend\n\nmodule Timestampable\n  attr_accessor :created_at, :updated_at\n  \n  def initialize_timestamps\n    @created_at = Time.now\n    @updated_at = Time.now\n  end\n  \n  def touch\n    @updated_at = Time.now\n  end\nend", "line_count": 22}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 22, "end_line": 32, "content": "module Timestampable\n  attr_accessor :created_at, :updated_at\n  \n  def initialize_timestamps\n    @created_at = Time.now\n    @updated_at = Time.now\n  end\n  \n  def touch\n    @updated_at = Time.now\n  end", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 30, "end_line": 30, "content": "  def touch", "line_count": 1}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 36, "end_line": 143, "content": "class User\n  include Validatable\n  include Timestampable\n  \n  attr_reader :id, :name, :email, :status, :roles\n  attr_accessor :avatar\n  \n  def initialize(name, email, status: :pending)\n    @name = name\n    @email = email\n    @status = status\n    @roles = [DEFAULT_ROLE]\n    @avatar = nil\n    initialize_timestamps\n  end\n  \n  # Getters and setters\n  def name=(new_name)\n    @name = new_name\n    touch\n  end\n  \n  def email=(new_email)\n    @email = new_email\n    touch\n  end\n  \n  def status=(new_status)\n    @status = new_status\n    touch\n  end\n  \n  # Role management\n  def add_role(role)\n    @roles << role unless @roles.include?(role)\n    touch\n    self\n  end\n  \n  def remove_role(role)\n    @roles.delete(role)\n    touch\n    self\n  end\n  \n  def has_role?(role)\n    @roles.include?(role)\n  end\n  \n  # Status methods\n  def active?\n    @status == :active\n  end\n  \n  def activate!\n    @status = :active\n    touch\n    self\n  end\n  \n  def suspend!\n    @status = :suspended\n    touch\n    self\n  end\n  \n  # Validation\n  def validate\n    errors = []\n    errors << 'Name is required' if @name.nil? || @name.empty?\n    errors << 'Email is required' if @email.nil? || @email.empty?\n    errors << 'Invalid email format' unless valid_email?\n    errors\n  end\n  \n  # Serialization\n  def to_hash\n    {\n      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }\n  end\n  \n  def to_json(*args)\n    to_hash.to_json(*args)\n  end\n  \n  # String representation\n  def to_s\n    \"#{@name} <#{@email}>\"\n  end\n  \n  def inspect\n    \"#<User:#{object_id} @name=\\\"#{@name}\\\" @email=\\\"#{@email}\\\" @status=#{@status}>\"\n  end\n  \n  private\n  \n  def valid_email?\n    @email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\n  end\nend", "line_count": 108}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 36, "end_line": 86, "content": "class User\n  include Validatable\n  include Timestampable\n  \n  attr_reader :id, :name, :email, :status, :roles\n  attr_accessor :avatar\n  \n  def initialize(name, email, status: :pending)\n    @name = name\n    @email = email\n    @status = status\n    @roles = [DEFAULT_ROLE]\n    @avatar = nil\n    initialize_timestamps\n  end\n  \n  # Getters and setters\n  def name=(new_name)\n    @name = new_name\n    touch\n  end\n  \n  def email=(new_email)\n    @email = new_email\n    touch\n  end\n  \n  def status=(new_status)\n    @status = new_status\n    touch\n  end\n  \n  # Role management\n  def add_role(role)\n    @roles << role unless @roles.include?(role)\n    touch\n    self\n  end\n  \n  def remove_role(role)\n    @roles.delete(role)\n    touch\n    self\n  end\n  \n  def has_role?(role)\n    @roles.include?(role)\n  end\n  \n  # Status methods\n  def active?\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 86, "end_line": 136, "content": "...\n  def active?\n    @status == :active\n  end\n  \n  def activate!\n    @status = :active\n    touch\n    self\n  end\n  \n  def suspend!\n    @status = :suspended\n    touch\n    self\n  end\n  \n  # Validation\n  def validate\n    errors = []\n    errors << 'Name is required' if @name.nil? || @name.empty?\n    errors << 'Email is required' if @email.nil? || @email.empty?\n    errors << 'Invalid email format' unless valid_email?\n    errors\n  end\n  \n  # Serialization\n  def to_hash\n    {\n      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }\n  end\n  \n  def to_json(*args)\n    to_hash.to_json(*args)\n  end\n  \n  # String representation\n  def to_s\n    \"#{@name} <#{@email}>\"\n  end\n  \n  def inspect\n    \"#<User:#{object_id} @name=\\\"#{@name}\\\" @email=\\\"#{@email}\\\" @status=#{@status}>\"\n  end\n...", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 136, "end_line": 143, "content": "...\n  end\n  \n  private\n  \n  def valid_email?\n    @email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\n  end\nend", "line_count": 8}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 36, "end_line": 50, "content": "class User\n  include Validatable\n  include Timestampable\n  \n  attr_reader :id, :name, :email, :status, :roles\n  attr_accessor :avatar\n  \n  def initialize(name, email, status: :pending)\n    @name = name\n    @email = email\n    @status = status\n    @roles = [DEFAULT_ROLE]\n    @avatar = nil\n    initialize_timestamps\n  end", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 43, "end_line": 56, "content": "  def initialize(name, email, status: :pending)\n    @name = name\n    @email = email\n    @status = status\n    @roles = [DEFAULT_ROLE]\n    @avatar = nil\n    initialize_timestamps\n  end\n  \n  # Getters and setters\n  def name=(new_name)\n    @name = new_name\n    touch\n  end", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 54, "end_line": 66, "content": "    @name = new_name\n    touch\n  end\n  \n  def email=(new_email)\n    @email = new_email\n    touch\n  end\n  \n  def status=(new_status)\n    @status = new_status\n    touch\n  end", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 64, "end_line": 79, "content": "    @status = new_status\n    touch\n  end\n  \n  # Role management\n  def add_role(role)\n    @roles << role unless @roles.include?(role)\n    touch\n    self\n  end\n  \n  def remove_role(role)\n    @roles.delete(role)\n    touch\n    self\n  end", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 75, "end_line": 85, "content": "  def remove_role(role)\n    @roles.delete(role)\n    touch\n    self\n  end\n  \n  def has_role?(role)\n    @roles.include?(role)\n  end\n  \n  # Status methods", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 86, "end_line": 100, "content": "  def active?\n    @status == :active\n  end\n  \n  def activate!\n    @status = :active\n    touch\n    self\n  end\n  \n  def suspend!\n    @status = :suspended\n    touch\n    self\n  end", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 96, "end_line": 109, "content": "  def suspend!\n    @status = :suspended\n    touch\n    self\n  end\n  \n  # Validation\n  def validate\n    errors = []\n    errors << 'Name is required' if @name.nil? || @name.empty?\n    errors << 'Email is required' if @email.nil? || @email.empty?\n    errors << 'Invalid email format' unless valid_email?\n    errors\n  end", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 103, "end_line": 123, "content": "  def validate\n    errors = []\n    errors << 'Name is required' if @name.nil? || @name.empty?\n    errors << 'Email is required' if @email.nil? || @email.empty?\n    errors << 'Invalid email format' unless valid_email?\n    errors\n  end\n  \n  # Serialization\n  def to_hash\n    {\n      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }\n  end", "line_count": 21}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 112, "end_line": 122, "content": "  def to_hash\n    {\n      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 114, "end_line": 127, "content": "      id: @id,\n      name: @name,\n      email: @email,\n      status: @status,\n      roles: @roles,\n      avatar: @avatar,\n      created_at: @created_at,\n      updated_at: @updated_at\n    }\n  end\n  \n  def to_json(*args)\n    to_hash.to_json(*args)\n  end", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 125, "end_line": 136, "content": "  def to_json(*args)\n    to_hash.to_json(*args)\n  end\n  \n  # String representation\n  def to_s\n    \"#{@name} <#{@email}>\"\n  end\n  \n  def inspect\n    \"#<User:#{object_id} @name=\\\"#{@name}\\\" @email=\\\"#{@email}\\\" @status=#{@status}>\"\n  end", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 134, "end_line": 145, "content": "  def inspect\n    \"#<User:#{object_id} @name=\\\"#{@name}\\\" @email=\\\"#{@email}\\\" @status=#{@status}>\"\n  end\n  \n  private\n  \n  def valid_email?\n    @email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\n  end\nend\n\n# Inheritance example", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 146, "end_line": 158, "content": "class AdminUser < User\n  def initialize(name, email)\n    super(name, email, status: :active)\n    add_role('admin')\n  end\n  \n  def admin?\n    true\n  end\n  \n  def can_manage_users?\n    true\n  end", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 156, "end_line": 203, "content": "  def can_manage_users?\n    true\n  end\nend\n\n# Repository class\nclass UserRepository\n  def initialize(logger = nil)\n    @users = {}\n    @next_id = 1\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def save(user)\n    if user.valid?\n      user.instance_variable_set(:@id, @next_id) unless user.id\n      @users[@next_id] = user\n      @next_id += 1\n      @logger.info(\"User saved: #{user}\")\n      true\n    else\n      @logger.error(\"Invalid user: #{user.validate.join(', ')}\")\n      false\n    end\n  end\n  \n  def find(id)\n    @users[id]\n  end\n  \n  def find_by_email(email)\n    @users.values.find { |user| user.email == email }\n  end\n  \n  def all\n    @users.values\n  end\n  \n  def delete(id)\n    user = @users.delete(id)\n    @logger.info(\"User deleted: #{user}\") if user\n    user\n  end\n  \n  def count\n    @users.size\n  end\nend", "line_count": 48}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 162, "end_line": 180, "content": "class UserRepository\n  def initialize(logger = nil)\n    @users = {}\n    @next_id = 1\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def save(user)\n    if user.valid?\n      user.instance_variable_set(:@id, @next_id) unless user.id\n      @users[@next_id] = user\n      @next_id += 1\n      @logger.info(\"User saved: #{user}\")\n      true\n    else\n      @logger.error(\"Invalid user: #{user.validate.join(', ')}\")\n      false\n    end\n  end", "line_count": 19}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 169, "end_line": 184, "content": "  def save(user)\n    if user.valid?\n      user.instance_variable_set(:@id, @next_id) unless user.id\n      @users[@next_id] = user\n      @next_id += 1\n      @logger.info(\"User saved: #{user}\")\n      true\n    else\n      @logger.error(\"Invalid user: #{user.validate.join(', ')}\")\n      false\n    end\n  end\n  \n  def find(id)\n    @users[id]\n  end", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 182, "end_line": 192, "content": "  def find(id)\n    @users[id]\n  end\n  \n  def find_by_email(email)\n    @users.values.find { |user| user.email == email }\n  end\n  \n  def all\n    @users.values\n  end", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 190, "end_line": 202, "content": "  def all\n    @users.values\n  end\n  \n  def delete(id)\n    user = @users.delete(id)\n    @logger.info(\"User deleted: #{user}\") if user\n    user\n  end\n  \n  def count\n    @users.size\n  end", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 200, "end_line": 244, "content": "  def count\n    @users.size\n  end\nend\n\n# Service class\nclass UserService\n  def initialize(repository, logger = nil)\n    @repository = repository\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def create_user(name, email, admin: false)\n    user = admin ? AdminUser.new(name, email) : User.new(name, email)\n    \n    if @repository.save(user)\n      @logger.info(\"User created: #{user.id}\")\n      user\n    else\n      @logger.error(\"Failed to create user: #{user.validate.join(', ')}\")\n      nil\n    end\n  end\n  \n  def activate_user(id)\n    user = @repository.find(id)\n    return false unless user\n    \n    user.activate!\n    @repository.save(user)\n  end\n  \n  def find_active_users\n    @repository.all.select(&:active?)\n  end\n  \n  def user_stats\n    users = @repository.all\n    {\n      total: users.size,\n      active: users.count(&:active?),\n      admins: users.count { |u| u.is_a?(AdminUser) }\n    }\n  end\nend", "line_count": 45}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 206, "end_line": 222, "content": "class UserService\n  def initialize(repository, logger = nil)\n    @repository = repository\n    @logger = logger || Logger.new(STDOUT)\n  end\n  \n  def create_user(name, email, admin: false)\n    user = admin ? AdminUser.new(name, email) : User.new(name, email)\n    \n    if @repository.save(user)\n      @logger.info(\"User created: #{user.id}\")\n      user\n    else\n      @logger.error(\"Failed to create user: #{user.validate.join(', ')}\")\n      nil\n    end\n  end", "line_count": 17}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 212, "end_line": 230, "content": "  def create_user(name, email, admin: false)\n    user = admin ? AdminUser.new(name, email) : User.new(name, email)\n    \n    if @repository.save(user)\n      @logger.info(\"User created: #{user.id}\")\n      user\n    else\n      @logger.error(\"Failed to create user: #{user.validate.join(', ')}\")\n      nil\n    end\n  end\n  \n  def activate_user(id)\n    user = @repository.find(id)\n    return false unless user\n    \n    user.activate!\n    @repository.save(user)\n  end", "line_count": 19}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 224, "end_line": 234, "content": "  def activate_user(id)\n    user = @repository.find(id)\n    return false unless user\n    \n    user.activate!\n    @repository.save(user)\n  end\n  \n  def find_active_users\n    @repository.all.select(&:active?)\n  end", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 232, "end_line": 243, "content": "  def find_active_users\n    @repository.all.select(&:active?)\n  end\n  \n  def user_stats\n    users = @repository.all\n    {\n      total: users.size,\n      active: users.count(&:active?),\n      admins: users.count { |u| u.is_a?(AdminUser) }\n    }\n  end", "line_count": 12}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 236, "end_line": 246, "content": "  def user_stats\n    users = @repository.all\n    {\n      total: users.size,\n      active: users.count(&:active?),\n      admins: users.count { |u| u.is_a?(AdminUser) }\n    }\n  end\nend\n\n# Singleton pattern", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 247, "end_line": 260, "content": "class Configuration\n  include Singleton\n  \n  attr_accessor :max_users, :default_role, :log_level\n  \n  def initialize\n    @max_users = MAX_USERS\n    @default_role = DEFAULT_ROLE\n    @log_level = :info\n  end\n  \n  def self.configure\n    yield(instance) if block_given?\n  end", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 258, "end_line": 271, "content": "  def self.configure\n    yield(instance) if block_given?\n  end\nend\n\n# Utility functions\ndef generate_random_string(length = 10)\n  chars = ('a'..'z').to_a + ('A'..'Z').to_a + ('0'..'9').to_a\n  Array.new(length) { chars.sample }.join\nend\n\ndef validate_email(email)\n  email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\nend", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 269, "end_line": 281, "content": "def validate_email(email)\n  email =~ /\\A[\\w+\\-.]+@[a-z\\d\\-]+(\\.[a-z\\d\\-]+)*\\.[a-z]+\\z/i\nend\n\n# Proc and lambda examples\nmultiply = proc { |a, b| a * b }\nadd = lambda { |a, b| a + b }\nsquare = ->(x) { x * x }\n\n# Block examples\ndef process_users(users, &block)\n  users.each(&block) if block_given?\nend", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 279, "end_line": 289, "content": "def process_users(users, &block)\n  users.each(&block) if block_given?\nend\n\ndef with_timing\n  start_time = Time.now\n  result = yield if block_given?\n  end_time = Time.now\n  puts \"Execution time: #{end_time - start_time} seconds\"\n  result\nend", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 283, "end_line": 296, "content": "def with_timing\n  start_time = Time.now\n  result = yield if block_given?\n  end_time = Time.now\n  puts \"Execution time: #{end_time - start_time} seconds\"\n  result\nend\n\n# Method with keyword arguments\ndef create_user_with_options(name:, email:, admin: false, roles: [])\n  user = admin ? AdminUser.new(name, email) : User.new(name, email)\n  roles.each { |role| user.add_role(role) }\n  user\nend", "line_count": 14}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 292, "end_line": 315, "content": "def create_user_with_options(name:, email:, admin: false, roles: [])\n  user = admin ? AdminUser.new(name, email) : User.new(name, email)\n  roles.each { |role| user.add_role(role) }\n  user\nend\n\n# Class with class methods\nclass UserFactory\n  class << self\n    def create_admin(name, email)\n      AdminUser.new(name, email)\n    end\n    \n    def create_regular(name, email)\n      User.new(name, email)\n    end\n    \n    def create_batch(users_data)\n      users_data.map do |data|\n        create_regular(data[:name], data[:email])\n      end\n    end\n  end\nend", "line_count": 24}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 299, "end_line": 314, "content": "class UserFactory\n  class << self\n    def create_admin(name, email)\n      AdminUser.new(name, email)\n    end\n    \n    def create_regular(name, email)\n      User.new(name, email)\n    end\n    \n    def create_batch(users_data)\n      users_data.map do |data|\n        create_regular(data[:name], data[:email])\n      end\n    end\n  end", "line_count": 16}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 301, "end_line": 313, "content": "    def create_admin(name, email)\n      AdminUser.new(name, email)\n    end\n    \n    def create_regular(name, email)\n      User.new(name, email)\n    end\n    \n    def create_batch(users_data)\n      users_data.map do |data|\n        create_regular(data[:name], data[:email])\n      end\n    end", "line_count": 13}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 309, "end_line": 319, "content": "    def create_batch(users_data)\n      users_data.map do |data|\n        create_regular(data[:name], data[:email])\n      end\n    end\n  end\nend\n\n# Exception handling\nclass UserError < StandardError; end\nclass UserNotFoundError < UserError; end", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 319, "end_line": 329, "content": "class UserNotFoundError < UserError; end\nclass InvalidUserError < UserError; end\n\ndef find_user_safely(repository, id)\n  user = repository.find(id)\n  raise UserNotFoundError, \"User with ID #{id} not found\" unless user\n  user\nrescue UserNotFoundError => e\n  puts \"Error: #{e.message}\"\n  nil\nend", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 322, "end_line": 332, "content": "def find_user_safely(repository, id)\n  user = repository.find(id)\n  raise UserNotFoundError, \"User with ID #{id} not found\" unless user\n  user\nrescue UserNotFoundError => e\n  puts \"Error: #{e.message}\"\n  nil\nend\n\n# Example usage\nif __FILE__ == $0", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 337, "end_line": 347, "content": "  # Create users\n  user1 = service.create_user('Alice', '<EMAIL>')\n  user2 = service.create_user('Bob', '<EMAIL>', admin: true)\n  \n  # Activate user\n  service.activate_user(user1.id) if user1\n  \n  # Process users with block\n  process_users(repository.all) do |user|\n    puts \"Processing user: #{user}\"\n  end", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 349, "end_line": 359, "content": "  # Timing example\n  with_timing do\n    sleep(0.1)\n    puts \"Some operation completed\"\n  end\n  \n  # Stats\n  stats = service.user_stats\n  puts \"User statistics: #{stats}\"\n  \n  # Configuration", "line_count": 11}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/ruby/sample.rb", "start_line": 360, "end_line": 363, "content": "  Configuration.configure do |config|\n    config.max_users = 500\n    config.log_level = :debug\n  end", "line_count": 4}], "chunk_count": 55}, "analysis": {"detected_structures": ["name.definition.class: class UserRepository", "name.definition.method: def to_json(*args)", "name.definition.method: def with_timing", "name.definition.method: def all", "name.definition.method: def has_role?(role)", "name.definition.method: def suspend!", "name.definition.class: class UserFactory", "name.definition.method: def add_role(role)", "name.definition.method: def initialize(name, email, status: :pending)", "name.definition.method: def generate_random_string(length = 10)", "name.definition.method: def delete(id)", "definition.method_all: def status=(new_status)", "name.definition.method: def can_manage_users?", "name.definition.method: def create_batch(users_data)", "name.definition.class: class Configuration", "name.definition.method: def initialize", "name.definition.method: def count", "name.definition.class: class UserService", "name.definition.class: class UserError < StandardError; end", "name.definition.method: def create_user_with_options(name:, email:, admin: false, roles: [])", "name.definition.method: def user_stats", "name.definition.class: class InvalidUserError < UserError; end", "name.definition.method: def find_active_users", "definition.method_all: def name=(new_name)", "name.definition.method: def initialize(logger = nil)", "name.definition.method: def active?", "name.definition.method: def create_admin(name, email)", "name.definition.method: def admin?", "name.definition.method: def activate_user(id)", "name.definition.class: class UserNotFoundError < UserError; end", "name.definition.method: def save(user)", "name.definition.method: def activate!", "name.definition.method: def inspect", "name.definition.method: def to_s", "name.definition.method: def find_by_email(email)", "name.definition.method: def initialize(repository, logger = nil)", "name.definition.method: def valid_email?", "name.definition.class: class User", "name.definition.method: def validate_email(email)", "name.definition.method: def touch", "name.definition.method: def process_users(users, &block)", "name.definition.method: def self.configure", "name.definition.method: def to_hash", "name.definition.method: def create_user(name, email, admin: false)", "name.definition.class: class AdminUser < User", "name.definition.method: def valid?", "name.definition.method: def initialize(name, email)", "name.definition.method: def create_regular(name, email)", "name.definition.method: def find_user_safely(repository, id)", "name.definition.method: def remove_role(role)", "definition.method_all: def email=(new_email)", "name.definition.method: def find(id)", "name.definition.method: def validate", "name.definition.method: def initialize_timestamps"], "structure_types_count": 54, "total_lines_in_chunks": 1588, "coverage_percentage": 432.69754768392374}}
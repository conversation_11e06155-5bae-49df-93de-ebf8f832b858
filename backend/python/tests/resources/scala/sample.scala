// Scala sample file for testing FileParser functionality.
// This file contains various Scala syntax structures that should be captured by the tree-sitter queries.

package com.example.sample

import scala.concurrent.{Future, ExecutionContext}
import scala.util.{Try, Success, Failure}
import scala.collection.mutable
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap

// Case classes
case class User(
  id: Long,
  name: String,
  email: String,
  status: UserStatus = UserStatus.Pending,
  avatar: Option[String] = None,
  createdAt: LocalDateTime = LocalDateTime.now(),
  updatedAt: LocalDateTime = LocalDateTime.now()
) {
  def isActive: Boolean = status == UserStatus.Active
  
  def displayName: String = if (name.trim.isEmpty) "Unknown User" else name
  
  def activate: User = copy(status = UserStatus.Active, updatedAt = LocalDateTime.now())
  
  def updateName(newName: String): User = copy(name = newName, updatedAt = LocalDateTime.now())
}

case class UserStats(posts: Int = 0, followers: Int = 0, following: Int = 0)

case class ValidationError(field: String, message: String)

// Sealed traits and classes
sealed trait UserStatus {
  def displayName: String
}

object UserStatus {
  case object Active extends UserStatus {
    def displayName: String = "Active"
  }
  
  case object Inactive extends UserStatus {
    def displayName: String = "Inactive"
  }
  
  case object Suspended extends UserStatus {
    def displayName: String = "Suspended"
  }
  
  case object Pending extends UserStatus {
    def displayName: String = "Pending Verification"
  }
  
  def fromString(value: String): Option[UserStatus] = value.toLowerCase match {
    case "active" => Some(Active)
    case "inactive" => Some(Inactive)
    case "suspended" => Some(Suspended)
    case "pending" => Some(Pending)
    case _ => None
  }
}

sealed trait Result[+T]
case class Success[T](data: T) extends Result[T]
case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]
case object Loading extends Result[Nothing]

sealed trait UserEvent
case class UserCreated(user: User) extends UserEvent
case class UserUpdated(user: User) extends UserEvent
case class UserDeleted(userId: Long) extends UserEvent
case class UserStatusChanged(userId: Long, newStatus: UserStatus) extends UserEvent

// Traits
trait UserRepository {
  def findById(id: Long): Future[Option[User]]
  def save(user: User): Future[Boolean]
  def delete(id: Long): Future[Boolean]
  def findAll(): Future[List[User]]
  def findByStatus(status: UserStatus): Future[List[User]]
}

trait Logger {
  def info(message: String): Unit
  def error(message: String, throwable: Option[Throwable] = None): Unit
  def debug(message: String): Unit
}

trait Validator[T] {
  def validate(item: T): List[ValidationError]
  def isValid(item: T): Boolean = validate(item).isEmpty
}

// Abstract classes
abstract class BaseRepository[T, ID] {
  protected def doSave(item: T): Future[Boolean]
  protected def doFindById(id: ID): Future[Option[T]]
  protected def doDelete(id: ID): Future[Boolean]
  
  def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {
    doSave(item).map { success =>
      if (success) Success(item)
      else Error("Failed to save item")
    }.recover {
      case ex => Error("Save operation failed", Some(ex))
    }
  }
}

// Classes
class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {
  private val users = new ConcurrentHashMap[Long, User]()
  
  override def findById(id: Long): Future[Option[User]] = Future {
    Option(users.get(id))
  }
  
  override def save(user: User): Future[Boolean] = Future {
    users.put(user.id, user)
    true
  }
  
  override def delete(id: Long): Future[Boolean] = Future {
    users.remove(id) != null
  }
  
  override def findAll(): Future[List[User]] = Future {
    users.values().asScala.toList
  }
  
  override def findByStatus(status: UserStatus): Future[List[User]] = Future {
    users.values().asScala.filter(_.status == status).toList
  }
}

class UserValidator extends Validator[User] {
  override def validate(item: User): List[ValidationError] = {
    val errors = mutable.ListBuffer[ValidationError]()
    
    if (item.name.trim.isEmpty) {
      errors += ValidationError("name", "Name is required")
    }
    
    if (item.email.trim.isEmpty) {
      errors += ValidationError("email", "Email is required")
    } else if (!isValidEmail(item.email)) {
      errors += ValidationError("email", "Invalid email format")
    }
    
    errors.toList
  }
  
  private def isValidEmail(email: String): Boolean = {
    val emailRegex = """[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}""".r
    emailRegex.matches(email)
  }
}

class ConsoleLogger extends Logger {
  override def info(message: String): Unit = {
    println(s"[${LocalDateTime.now()}] [INFO] $message")
  }
  
  override def error(message: String, throwable: Option[Throwable] = None): Unit = {
    println(s"[${LocalDateTime.now()}] [ERROR] $message")
    throwable.foreach(_.printStackTrace())
  }
  
  override def debug(message: String): Unit = {
    println(s"[${LocalDateTime.now()}] [DEBUG] $message")
  }
}

class UserService(
  repository: UserRepository,
  validator: Validator[User],
  logger: Logger
)(implicit ec: ExecutionContext) {
  
  private val eventListeners = mutable.ListBuffer[(UserEvent) => Unit]()
  
  def createUser(name: String, email: String): Future[Result[User]] = {
    val user = User(
      id = generateUserId(),
      name = name,
      email = email
    )
    
    val validationErrors = validator.validate(user)
    if (validationErrors.nonEmpty) {
      val errorMessage = validationErrors.map(e => s"${e.field}: ${e.message}").mkString(", ")
      logger.error(s"Invalid user data: $errorMessage")
      Future.successful(Error(s"Invalid user data: $errorMessage"))
    } else {
      repository.save(user).map { success =>
        if (success) {
          logger.info(s"User created successfully: ${user.id}")
          notifyListeners(UserCreated(user))
          Success(user)
        } else {
          logger.error("Failed to save user")
          Error("Failed to save user")
        }
      }.recover {
        case ex =>
          logger.error("Error creating user", Some(ex))
          Error(s"Error creating user: ${ex.getMessage}", Some(ex))
      }
    }
  }
  
  def activateUser(id: Long): Future[Boolean] = {
    repository.findById(id).flatMap {
      case Some(user) =>
        val activatedUser = user.activate
        repository.save(activatedUser).map { success =>
          if (success) {
            notifyListeners(UserStatusChanged(id, UserStatus.Active))
          }
          success
        }
      case None =>
        logger.error(s"User not found: $id")
        Future.successful(false)
    }.recover {
      case ex =>
        logger.error("Error activating user", Some(ex))
        false
    }
  }
  
  def getUserStats(): Future[Map[UserStatus, Int]] = {
    repository.findAll().map { users =>
      users.groupBy(_.status).view.mapValues(_.size).toMap
    }.recover {
      case ex =>
        logger.error("Error getting user stats", Some(ex))
        Map.empty[UserStatus, Int]
    }
  }
  
  def addEventListener(listener: UserEvent => Unit): Unit = {
    eventListeners += listener
  }
  
  private def notifyListeners(event: UserEvent): Unit = {
    eventListeners.foreach(_(event))
  }
  
  private def generateUserId(): Long = System.currentTimeMillis()
}

// Object definitions
object UserFactory {
  def createSampleUser(): User = User(
    id = 1L,
    name = "John Doe",
    email = "<EMAIL>"
  )
  
  def createUsers(count: Int): List[User] = (1 to count).map { i =>
    User(
      id = i.toLong,
      name = s"User $i",
      email = s"user$<EMAIL>"
    )
  }.toList
}

object Constants {
  val MaxUsers = 1000
  val DefaultPageSize = 20
  val ApiVersion = "v1"
}

// Companion objects
class ApiClient private(baseUrl: String, timeoutMs: Long) {
  // Implementation would go here
}

object ApiClient {
  private val DefaultBaseUrl = "https://api.example.com"
  private val DefaultTimeout = 30000L
  
  def apply(): ApiClient = new ApiClient(DefaultBaseUrl, DefaultTimeout)
  
  def apply(baseUrl: String): ApiClient = new ApiClient(baseUrl, DefaultTimeout)
  
  def apply(baseUrl: String, timeoutMs: Long): ApiClient = new ApiClient(baseUrl, timeoutMs)
}

// Implicit classes (extension methods)
implicit class UserOps(user: User) {
  def toJson: String = {
    // Simplified JSON serialization
    s"""{"id":${user.id},"name":"${user.name}","email":"${user.email}","status":"${user.status}"}"""
  }
  
  def isOlderThan(days: Int): Boolean = {
    user.createdAt.isBefore(LocalDateTime.now().minusDays(days))
  }
}

implicit class UserListOps(users: List[User]) {
  def activeUsers: List[User] = users.filter(_.isActive)
  
  def byStatus(status: UserStatus): List[User] = users.filter(_.status == status)
  
  def sortedByName: List[User] = users.sortBy(_.name)
}

// Higher-order functions and functional programming
object UserUtils {
  def measureTime[T](operation: () => T): (T, Long) = {
    val startTime = System.currentTimeMillis()
    val result = operation()
    val endTime = System.currentTimeMillis()
    (result, endTime - startTime)
  }
  
  def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])
                       (implicit ec: ExecutionContext): Future[T] = {
    def attempt(remaining: Int): Future[T] = {
      operation().recoverWith {
        case ex if remaining > 1 =>
          Thread.sleep(delay)
          attempt(remaining - 1)
        case ex => Future.failed(ex)
      }
    }
    attempt(times)
  }
  
  def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {
    items.grouped(batchSize).map(processor).toList
  }
}

// Pattern matching examples
object PatternMatchingExamples {
  def processResult[T](result: Result[T]): String = result match {
    case Success(data) => s"Success: $data"
    case Error(message, Some(cause)) => s"Error: $message (caused by: ${cause.getMessage})"
    case Error(message, None) => s"Error: $message"
    case Loading => "Loading..."
  }
  
  def processUser(user: User): String = user match {
    case User(_, name, _, UserStatus.Active, _, _, _) => s"Active user: $name"
    case User(_, name, _, UserStatus.Suspended, _, _, _) => s"Suspended user: $name"
    case User(id, _, _, _, _, _, _) if id < 100 => "Early user"
    case _ => "Regular user"
  }
  
  def extractUserInfo(user: User): (String, String) = user match {
    case User(_, name, email, _, _, _, _) => (name, email)
  }
}

// Main application
object UserApp extends App {
  implicit val ec: ExecutionContext = ExecutionContext.global
  
  val logger = new ConsoleLogger()
  val repository = new InMemoryUserRepository()
  val validator = new UserValidator()
  val service = new UserService(repository, validator, logger)
  
  // Add event listener
  service.addEventListener {
    case UserCreated(user) => logger.info(s"User created: ${user.name}")
    case UserUpdated(user) => logger.info(s"User updated: ${user.name}")
    case UserDeleted(userId) => logger.info(s"User deleted: $userId")
    case UserStatusChanged(userId, newStatus) => logger.info(s"User $userId status changed to $newStatus")
  }
  
  // Example usage
  val futureResult = for {
    // Create user
    userResult <- service.createUser("Alice", "<EMAIL>")
    user = userResult match {
      case Success(u) => 
        logger.info(s"Created user: ${u.name}")
        Some(u)
      case Error(message, _) => 
        logger.error(s"Failed to create user: $message")
        None
      case Loading => None
    }
    
    // Activate user if created successfully
    activated <- user match {
      case Some(u) => service.activateUser(u.id)
      case None => Future.successful(false)
    }
    
    // Get stats
    stats <- service.getUserStats()
  } yield {
    logger.info(s"User activation result: $activated")
    logger.info(s"User statistics: $stats")
  }
  
  // Handle the future result
  futureResult.onComplete {
    case Success(_) => logger.info("Application completed successfully")
    case Failure(ex) => logger.error("Application failed", Some(ex))
  }
  
  // Create sample users
  val sampleUsers = UserFactory.createUsers(5)
  sampleUsers.foreach { user =>
    repository.save(user)
  }
  
  // Use extension methods
  val activeUsers = sampleUsers.activeUsers
  logger.info(s"Active users: ${activeUsers.size}")
  
  // Process in batches
  val batches = UserUtils.processInBatches(sampleUsers, 2) { batch =>
    logger.info(s"Processing batch with ${batch.size} users")
    batch.map(_.name)
  }
  
  // Pattern matching example
  sampleUsers.foreach { user =>
    val info = PatternMatchingExamples.processUser(user)
    logger.info(info)
  }
}

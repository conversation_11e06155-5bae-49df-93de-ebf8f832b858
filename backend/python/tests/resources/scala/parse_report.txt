FileParser 解析结果报告 - SCALA
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala
  文件名: sample.scala
  内容长度: 12951 字符
  行数: 434


代码块信息 (6 个):
  块 1: 第 3-63 行 (61 行)
      3: package com.example.sample
      4: 
      5: import scala.concurrent.{Future, ExecutionContext}
    ... (还有 58 行)

  块 2: 第 65-136 行 (72 行)
     65: sealed trait Result[+T]
     66: case class Success[T](data: T) extends Result[T]
     67: case class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]
    ... (还有 69 行)

  块 3: 第 138-174 行 (37 行)
    138: class UserValidator extends Validator[User] {
    139:   override def validate(item: User): List[ValidationError] = {
    140:     val errors = mutable.ListBuffer[ValidationError]()
    ... (还有 34 行)

  块 4: 第 176-270 行 (95 行)
    176: class UserService(
    177:   repository: UserRepository,
    178:   validator: Validator[User],
    ... (还有 92 行)

  块 5: 第 272-339 行 (68 行)
    272: object Constants {
    273:   val MaxUsers = 1000
    274:   val DefaultPageSize = 20
    ... (还有 65 行)

  块 6: 第 342-433 行 (92 行)
    342: object PatternMatchingExamples {
    343:   def processResult[T](result: Result[T]): String = result match {
    344:     case Success(data) => s"Success: $data"
    ... (还有 89 行)


统计信息:
  覆盖率: 97.9%
  块中总行数: 425

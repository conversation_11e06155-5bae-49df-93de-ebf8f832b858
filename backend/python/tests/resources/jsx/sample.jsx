/**
 * JSX sample file for testing FileParser functionality.
 * This file contains various JSX syntax structures that should be captured by the tree-sitter queries.
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import styled from 'styled-components';

// Styled components
const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Button = styled.button`
  background-color: ${props => props.primary ? '#007bff' : '#6c757d'};
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
`;

// Functional component with hooks
const UserProfile = ({ user, onUpdate, className }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    bio: ''
  });
  const [errors, setErrors] = useState({});
  const inputRef = useRef(null);

  // Effect hook
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        bio: user.bio || ''
      });
    }
  }, [user]);

  // Effect for focus management
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  // Memoized computed value
  const isFormValid = useMemo(() => {
    return formData.name.trim() && 
           formData.email.trim() && 
           formData.email.includes('@');
  }, [formData.name, formData.email]);

  // Callback hook
  const handleInputChange = useCallback((field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  const handleSubmit = useCallback((event) => {
    event.preventDefault();
    
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!formData.email.includes('@')) {
      newErrors.email = 'Invalid email format';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    onUpdate(formData);
    setIsEditing(false);
  }, [formData, onUpdate]);

  const handleCancel = useCallback(() => {
    setIsEditing(false);
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
      bio: user?.bio || ''
    });
    setErrors({});
  }, [user]);

  // Conditional rendering
  if (!user) {
    return (
      <Container className={className}>
        <div className="loading">
          <p>Loading user profile...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className={className}>
      <div className="user-profile">
        <div className="profile-header">
          <img 
            src={user.avatar || '/default-avatar.png'} 
            alt={`${user.name}'s avatar`}
            className="avatar"
          />
          <div className="user-info">
            {isEditing ? (
              <form onSubmit={handleSubmit} className="edit-form">
                <div className="form-group">
                  <label htmlFor="name">Name:</label>
                  <input
                    ref={inputRef}
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange('name')}
                    className={errors.name ? 'error' : ''}
                  />
                  {errors.name && <span className="error-message">{errors.name}</span>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">Email:</label>
                  <input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    className={errors.email ? 'error' : ''}
                  />
                  {errors.email && <span className="error-message">{errors.email}</span>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="bio">Bio:</label>
                  <textarea
                    id="bio"
                    value={formData.bio}
                    onChange={handleInputChange('bio')}
                    rows={4}
                  />
                </div>
                
                <div className="form-actions">
                  <Button type="submit" primary disabled={!isFormValid}>
                    Save Changes
                  </Button>
                  <Button type="button" onClick={handleCancel}>
                    Cancel
                  </Button>
                </div>
              </form>
            ) : (
              <div className="profile-display">
                <h2>{user.name}</h2>
                <p className="email">{user.email}</p>
                {user.bio && <p className="bio">{user.bio}</p>}
                <Button onClick={() => setIsEditing(true)} primary>
                  Edit Profile
                </Button>
              </div>
            )}
          </div>
        </div>
        
        <UserStats user={user} />
      </div>
    </Container>
  );
};

// PropTypes validation
UserProfile.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    bio: PropTypes.string,
    avatar: PropTypes.string,
    stats: PropTypes.object
  }),
  onUpdate: PropTypes.func.isRequired,
  className: PropTypes.string
};

UserProfile.defaultProps = {
  className: '',
  user: null
};

// Class component example
class UserStats extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      expanded: false
    };
  }

  componentDidMount() {
    console.log('UserStats component mounted');
  }

  componentDidUpdate(prevProps) {
    if (prevProps.user?.id !== this.props.user?.id) {
      console.log('User changed, resetting expanded state');
      this.setState({ expanded: false });
    }
  }

  toggleExpanded = () => {
    this.setState(prevState => ({
      expanded: !prevState.expanded
    }));
  }

  render() {
    const { user } = this.props;
    const { expanded } = this.state;

    if (!user?.stats) {
      return null;
    }

    return (
      <div className="user-stats">
        <h3 onClick={this.toggleExpanded} className="stats-header">
          User Statistics {expanded ? '▼' : '▶'}
        </h3>
        {expanded && (
          <div className="stats-content">
            <div className="stat-item">
              <span className="stat-label">Posts:</span>
              <span className="stat-value">{user.stats.posts || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Followers:</span>
              <span className="stat-value">{user.stats.followers || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Following:</span>
              <span className="stat-value">{user.stats.following || 0}</span>
            </div>
          </div>
        )}
      </div>
    );
  }
}

// Higher-order component
const withLoading = (WrappedComponent) => {
  return function WithLoadingComponent(props) {
    if (props.isLoading) {
      return (
        <div className="loading-wrapper">
          <div className="spinner" />
          <p>Loading...</p>
        </div>
      );
    }
    
    return <WrappedComponent {...props} />;
  };
};

// Custom hook
const useUserData = (userId) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!userId) return;

    const fetchUser = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  return { user, loading, error };
};

// Main App component
const App = () => {
  const [selectedUserId, setSelectedUserId] = useState(1);
  const { user, loading, error } = useUserData(selectedUserId);

  const handleUserUpdate = useCallback((updatedData) => {
    console.log('Updating user:', updatedData);
    // API call would go here
  }, []);

  if (error) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{error}</p>
        <Button onClick={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  const EnhancedUserProfile = withLoading(UserProfile);

  return (
    <div className="app">
      <header className="app-header">
        <h1>User Profile App</h1>
        <nav>
          <Button onClick={() => setSelectedUserId(1)}>User 1</Button>
          <Button onClick={() => setSelectedUserId(2)}>User 2</Button>
          <Button onClick={() => setSelectedUserId(3)}>User 3</Button>
        </nav>
      </header>
      
      <main className="app-main">
        <EnhancedUserProfile
          user={user}
          isLoading={loading}
          onUpdate={handleUserUpdate}
        />
      </main>
    </div>
  );
};

// Redux connection (example)
const mapStateToProps = (state) => ({
  currentUser: state.auth.user,
  theme: state.ui.theme
});

const mapDispatchToProps = (dispatch) => ({
  updateUser: (userData) => dispatch({ type: 'UPDATE_USER', payload: userData }),
  setTheme: (theme) => dispatch({ type: 'SET_THEME', payload: theme })
});

export default connect(mapStateToProps, mapDispatchToProps)(App);
export { UserProfile, UserStats, withLoading, useUserData };

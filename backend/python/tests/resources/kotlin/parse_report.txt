FileParser 解析结果报告 - KOTLIN
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt
  文件名: sample.kt
  内容长度: 11440 字符
  行数: 385


代码块信息 (5 个):
  块 1: 第 12-72 行 (61 行)
     12: @Serializable
     13: data class User(
     14:     val id: Long,
    ... (还有 58 行)

  块 2: 第 74-140 行 (67 行)
     74: enum class LogLevel {
     75:     DEBUG, INFO, WARNING, ERROR
     76: }
    ... (还有 64 行)

  块 3: 第 142-241 行 (100 行)
    142: class UserValidator : Validator<User> {
    143:     override fun validate(item: User): List<String> {
    144:         val errors = mutableListOf<String>()
    ... (还有 97 行)

  块 4: 第 244-306 行 (63 行)
    244: fun User.toJson(): String = Json.encodeToString(User.serializer(), this)
    245: 
    246: fun String.toUser(): User? = try {
    ... (还有 60 行)

  块 5: 第 308-384 行 (77 行)
    308: object Constants {
    309:     const val MAX_USERS = 1000
    310:     const val DEFAULT_PAGE_SIZE = 20
    ... (还有 74 行)


统计信息:
  覆盖率: 95.6%
  块中总行数: 368

// Kotlin sample file for testing FileParser functionality.
// This file contains various Kotlin syntax structures that should be captured by the tree-sitter queries.

package com.example.sample

import kotlinx.coroutines.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap

// Data classes
@Serializable
data class User(
    val id: Long,
    var name: String,
    var email: String,
    var status: UserStatus = UserStatus.PENDING,
    var avatar: String? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    val isActive: Boolean
        get() = status == UserStatus.ACTIVE
    
    val displayName: String
        get() = if (name.isBlank()) "Unknown User" else name
    
    fun activate() {
        status = UserStatus.ACTIVE
        updatedAt = LocalDateTime.now()
    }
    
    fun updateName(newName: String) {
        name = newName
        updatedAt = LocalDateTime.now()
    }
}

@Serializable
data class UserStats(
    val posts: Int = 0,
    val followers: Int = 0,
    val following: Int = 0
)

// Sealed classes
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()
    object Loading : Result<Nothing>()
}

sealed class UserEvent {
    data class Created(val user: User) : UserEvent()
    data class Updated(val user: User) : UserEvent()
    data class Deleted(val userId: Long) : UserEvent()
    data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()
}

// Enums
enum class UserStatus(val displayName: String) {
    ACTIVE("Active"),
    INACTIVE("Inactive"),
    SUSPENDED("Suspended"),
    PENDING("Pending Verification");
    
    companion object {
        fun fromString(value: String): UserStatus? {
            return values().find { it.name.equals(value, ignoreCase = true) }
        }
    }
}

enum class LogLevel {
    DEBUG, INFO, WARNING, ERROR
}

// Interfaces
interface UserRepository {
    suspend fun findById(id: Long): User?
    suspend fun save(user: User): Boolean
    suspend fun delete(id: Long): Boolean
    suspend fun findAll(): List<User>
    suspend fun findByStatus(status: UserStatus): List<User>
}

interface Logger {
    fun log(level: LogLevel, message: String, throwable: Throwable? = null)
    fun info(message: String) = log(LogLevel.INFO, message)
    fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)
    fun debug(message: String) = log(LogLevel.DEBUG, message)
}

interface Validator<T> {
    fun validate(item: T): List<String>
    fun isValid(item: T): Boolean = validate(item).isEmpty()
}

// Abstract classes
abstract class BaseRepository<T, ID> {
    protected abstract suspend fun doSave(item: T): Boolean
    protected abstract suspend fun doFindById(id: ID): T?
    protected abstract suspend fun doDelete(id: ID): Boolean
    
    suspend fun save(item: T): Result<T> = try {
        if (doSave(item)) {
            Result.Success(item)
        } else {
            Result.Error("Failed to save item")
        }
    } catch (e: Exception) {
        Result.Error("Save operation failed", e)
    }
}

// Classes
class InMemoryUserRepository : UserRepository {
    private val users = ConcurrentHashMap<Long, User>()
    
    override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {
        users[id]
    }
    
    override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {
        users[user.id] = user
        true
    }
    
    override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {
        users.remove(id) != null
    }
    
    override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {
        users.values.toList()
    }
    
    override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {
        users.values.filter { it.status == status }
    }
}

class UserValidator : Validator<User> {
    override fun validate(item: User): List<String> {
        val errors = mutableListOf<String>()
        
        if (item.name.isBlank()) {
            errors.add("Name is required")
        }
        
        if (item.email.isBlank()) {
            errors.add("Email is required")
        } else if (!isValidEmail(item.email)) {
            errors.add("Invalid email format")
        }
        
        return errors
    }
    
    private fun isValidEmail(email: String): Boolean {
        val emailRegex = Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
        return emailRegex.matches(email)
    }
}

class ConsoleLogger : Logger {
    override fun log(level: LogLevel, message: String, throwable: Throwable?) {
        val timestamp = LocalDateTime.now()
        println("[$timestamp] [$level] $message")
        throwable?.printStackTrace()
    }
}

class UserService(
    private val repository: UserRepository,
    private val validator: Validator<User>,
    private val logger: Logger
) {
    private val eventListeners = mutableListOf<(UserEvent) -> Unit>()
    
    suspend fun createUser(name: String, email: String): Result<User> = try {
        val user = User(
            id = generateUserId(),
            name = name,
            email = email
        )
        
        val validationErrors = validator.validate(user)
        if (validationErrors.isNotEmpty()) {
            logger.error("Invalid user data: ${validationErrors.joinToString(", ")}")
            return Result.Error("Invalid user data: ${validationErrors.joinToString(", ")}")
        }
        
        if (repository.save(user)) {
            logger.info("User created successfully: ${user.id}")
            notifyListeners(UserEvent.Created(user))
            Result.Success(user)
        } else {
            logger.error("Failed to save user")
            Result.Error("Failed to save user")
        }
    } catch (e: Exception) {
        logger.error("Error creating user", e)
        Result.Error("Error creating user: ${e.message}", e)
    }
    
    suspend fun activateUser(id: Long): Boolean = try {
        val user = repository.findById(id)
        if (user != null) {
            user.activate()
            val success = repository.save(user)
            if (success) {
                notifyListeners(UserEvent.StatusChanged(id, UserStatus.ACTIVE))
            }
            success
        } else {
            logger.error("User not found: $id")
            false
        }
    } catch (e: Exception) {
        logger.error("Error activating user", e)
        false
    }
    
    suspend fun getUserStats(): Map<UserStatus, Int> = try {
        val users = repository.findAll()
        users.groupingBy { it.status }.eachCount()
    } catch (e: Exception) {
        logger.error("Error getting user stats", e)
        emptyMap()
    }
    
    fun addEventListener(listener: (UserEvent) -> Unit) {
        eventListeners.add(listener)
    }
    
    private fun notifyListeners(event: UserEvent) {
        eventListeners.forEach { it(event) }
    }
    
    private fun generateUserId(): Long = System.currentTimeMillis()
}

// Extension functions
fun User.toJson(): String = Json.encodeToString(User.serializer(), this)

fun String.toUser(): User? = try {
    Json.decodeFromString(User.serializer(), this)
} catch (e: Exception) {
    null
}

fun List<User>.activeUsers(): List<User> = filter { it.isActive }

fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }

// Higher-order functions
inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {
    val startTime = System.currentTimeMillis()
    val result = operation()
    val endTime = System.currentTimeMillis()
    return result to (endTime - startTime)
}

suspend fun <T> retryOperation(
    times: Int = 3,
    delay: Long = 1000,
    operation: suspend () -> T
): T? {
    repeat(times) { attempt ->
        try {
            return operation()
        } catch (e: Exception) {
            if (attempt == times - 1) throw e
            delay(delay)
        }
    }
    return null
}

// Generic functions
fun <T> List<T>.chunked(size: Int): List<List<T>> {
    return if (size <= 0) {
        throw IllegalArgumentException("Size must be positive")
    } else {
        (0 until this.size step size).map { i ->
            this.subList(i, minOf(i + size, this.size))
        }
    }
}

// Object declarations
object UserFactory {
    fun createSampleUser(): User = User(
        id = 1L,
        name = "John Doe",
        email = "<EMAIL>"
    )
    
    fun createUsers(count: Int): List<User> = (1..count).map { i ->
        User(
            id = i.toLong(),
            name = "User $i",
            email = "user$<EMAIL>"
        )
    }
}

object Constants {
    const val MAX_USERS = 1000
    const val DEFAULT_PAGE_SIZE = 20
    const val API_VERSION = "v1"
}

// Companion objects
class ApiClient {
    companion object {
        private const val BASE_URL = "https://api.example.com"
        
        fun create(): ApiClient = ApiClient()
        
        fun createWithTimeout(timeoutMs: Long): ApiClient {
            // Implementation would configure timeout
            return ApiClient()
        }
    }
}

// Main function and example usage
suspend fun main() {
    val logger = ConsoleLogger()
    val repository = InMemoryUserRepository()
    val validator = UserValidator()
    val service = UserService(repository, validator, logger)
    
    // Add event listener
    service.addEventListener { event ->
        when (event) {
            is UserEvent.Created -> logger.info("User created: ${event.user.name}")
            is UserEvent.Updated -> logger.info("User updated: ${event.user.name}")
            is UserEvent.Deleted -> logger.info("User deleted: ${event.userId}")
            is UserEvent.StatusChanged -> logger.info("User ${event.userId} status changed to ${event.newStatus}")
        }
    }
    
    // Create users
    val (user1Result, time1) = measureTime {
        runBlocking { service.createUser("Alice", "<EMAIL>") }
    }
    logger.info("User creation took ${time1}ms")
    
    when (user1Result) {
        is Result.Success -> {
            logger.info("Created user: ${user1Result.data.name}")
            
            // Activate user
            val activated = service.activateUser(user1Result.data.id)
            if (activated) {
                logger.info("User activated successfully")
            }
        }
        is Result.Error -> logger.error("Failed to create user: ${user1Result.message}")
        is Result.Loading -> logger.info("Still loading...")
    }
    
    // Create multiple users
    val sampleUsers = UserFactory.createUsers(5)
    sampleUsers.forEach { user ->
        repository.save(user)
    }
    
    // Get stats
    val stats = service.getUserStats()
    logger.info("User statistics: $stats")
    
    // Use extension functions
    val allUsers = repository.findAll()
    val activeUsers = allUsers.activeUsers()
    logger.info("Active users: ${activeUsers.size}")
    
    // Process users in chunks
    allUsers.chunked(2).forEachIndexed { index, chunk ->
        logger.info("Processing chunk $index with ${chunk.size} users")
    }
}

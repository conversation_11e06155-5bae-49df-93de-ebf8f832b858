{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "filename": "sample.kt", "content_length": 11440, "line_count": 385}, "parsing_results": {"key_structure": "@Serializable\n    ...\n    fun activate() {\n     ...\n    }\n    ...\n    fun updateName(newName: String) {\n     ...\n    }\n}\n...\n@Serializable\n ...\n)\n...\nsealed class Result<out T> {\n    data class Success<T>(val data: T) : Result<T>()\n    data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()\n ...\n}\n...\nsealed class UserEvent {\n    data class Created(val user: User) : UserEvent()\n    data class Updated(val user: User) : UserEvent()\n    data class Deleted(val userId: Long) : UserEvent()\n    data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()\n}\n...\nenum class UserStatus(val displayName: String) {\n        ...\n        fun fromString(value: String): UserStatus? {\n         ...\n        }\n ...\n}\n...\nenum class LogLevel {\n ...\n}\n...\ninterface UserRepository {\n    suspend fun findById(id: Long): User?\n    suspend fun save(user: User): Boolean\n    suspend fun delete(id: Long): <PERSON>olean\n    suspend fun findAll(): List<User>\n    suspend fun findByStatus(status: UserStatus): List<User>\n}\n...\ninterface Logger {\n    fun log(level: LogLevel, message: String, throwable: Throwable? = null)\n    fun info(message: String) = log(LogLevel.INFO, message)\n    fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)\n    fun debug(message: String) = log(LogLevel.DEBUG, message)\n}\n...\ninterface Validator<T> {\n    fun validate(item: T): List<String>\n    fun isValid(item: T): Boolean = validate(item).isEmpty()\n}\n...\nabstract class BaseRepository<T, ID> {\n    protected abstract suspend fun doSave(item: T): Boolean\n    protected abstract suspend fun doFindById(id: ID): T?\n    protected abstract suspend fun doDelete(id: ID): Boolean\n    ...\n    suspend fun save(item: T): Result<T> = try {\n     ...\n    }\n}\n...\nclass InMemoryUserRepository : UserRepository {\n    ...\n    override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {\n     ...\n    }\n    ...\n    override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {\n     ...\n    }\n    ...\n    override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {\n     ...\n    }\n    ...\n    override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {\n     ...\n    }\n    ...\n    override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {\n     ...\n    }\n}\n...\nclass UserValidator : Validator<User> {\n    override fun validate(item: User): List<String> {\n     ...\n    }\n    ...\n    private fun isValidEmail(email: String): Boolean {\n     ...\n    }\n}\n...\nclass ConsoleLogger : Logger {\n    override fun log(level: LogLevel, message: String, throwable: Throwable?) {\n     ...\n    }\n}\n...\nclass UserService(\n    ...\n    suspend fun createUser(name: String, email: String): Result<User> = try {\n     ...\n    }\n    ...\n    suspend fun activateUser(id: Long): Boolean = try {\n     ...\n    }\n    ...\n    suspend fun getUserStats(): Map<UserStatus, Int> = try {\n     ...\n    }\n    ...\n    fun addEventListener(listener: (UserEvent) -> Unit) {\n     ...\n    }\n    ...\n    private fun notifyListeners(event: UserEvent) {\n     ...\n    }\n                                                                   ...\n    private fun generateUserId(): Long = System.currentTimeMillis()\n}\n                                                                        ...\nfun User.toJson(): String = Json.encodeToString(User.serializer(), this)\n...\nfun String.toUser(): User? = try {\n ...\n}\n                                                                 ...\nfun List<User>.activeUsers(): List<User> = filter { it.isActive }\n                                                                                        ...\nfun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }\n...\ninline fun <T> measureTime(operation: () -> T): Pair<T, Long> {\n ...\n}\n...\nsuspend fun <T> retryOperation(\n ...\n}\n...\nfun <T> List<T>.chunked(size: Int): List<List<T>> {\n ...\n}\n    ...\n    fun createSampleUser(): User = User(\n     ...\n    )\n    ...\n    fun createUsers(count: Int): List<User> = (1..count).map { i ->\n     ...\n    }\n...\nclass ApiClient {\n                                             ...\n        fun create(): ApiClient = ApiClient()\n        ...\n        fun createWithTimeout(timeoutMs: Long): ApiClient {\n         ...\n        }\n ...\n}\n...\nsuspend fun main() {\n ...\n}\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 12, "end_line": 72, "content": "@Serializable\ndata class User(\n    val id: Long,\n    var name: String,\n    var email: String,\n    var status: UserStatus = UserStatus.PENDING,\n    var avatar: String? = null,\n    val createdAt: LocalDateTime = LocalDateTime.now(),\n    var updatedAt: LocalDateTime = LocalDateTime.now()\n) {\n    val isActive: Boolean\n        get() = status == UserStatus.ACTIVE\n    \n    val displayName: String\n        get() = if (name.isBlank()) \"Unknown User\" else name\n    \n    fun activate() {\n        status = UserStatus.ACTIVE\n        updatedAt = LocalDateTime.now()\n    }\n    \n    fun updateName(newName: String) {\n        name = newName\n        updatedAt = LocalDateTime.now()\n    }\n}\n\n@Serializable\ndata class UserStats(\n    val posts: Int = 0,\n    val followers: Int = 0,\n    val following: Int = 0\n)\n\n// Sealed classes\nsealed class Result<out T> {\n    data class Success<T>(val data: T) : Result<T>()\n    data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>()\n    object Loading : Result<Nothing>()\n}\n\nsealed class UserEvent {\n    data class Created(val user: User) : UserEvent()\n    data class Updated(val user: User) : UserEvent()\n    data class Deleted(val userId: Long) : UserEvent()\n    data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent()\n}\n\n// Enums\nenum class UserStatus(val displayName: String) {\n    ACTIVE(\"Active\"),\n    INACTIVE(\"Inactive\"),\n    SUSPENDED(\"Suspended\"),\n    PENDING(\"Pending Verification\");\n    \n    companion object {\n        fun fromString(value: String): UserStatus? {\n            return values().find { it.name.equals(value, ignoreCase = true) }\n        }\n    }\n}", "line_count": 61}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 74, "end_line": 140, "content": "enum class LogLevel {\n    DEBUG, INFO, WARNING, ERROR\n}\n\n// Interfaces\ninterface UserRepository {\n    suspend fun findById(id: Long): User?\n    suspend fun save(user: User): Boolean\n    suspend fun delete(id: Long): Boolean\n    suspend fun findAll(): List<User>\n    suspend fun findByStatus(status: UserStatus): List<User>\n}\n\ninterface Logger {\n    fun log(level: LogLevel, message: String, throwable: Throwable? = null)\n    fun info(message: String) = log(LogLevel.INFO, message)\n    fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable)\n    fun debug(message: String) = log(LogLevel.DEBUG, message)\n}\n\ninterface Validator<T> {\n    fun validate(item: T): List<String>\n    fun isValid(item: T): Boolean = validate(item).isEmpty()\n}\n\n// Abstract classes\nabstract class BaseRepository<T, ID> {\n    protected abstract suspend fun doSave(item: T): Boolean\n    protected abstract suspend fun doFindById(id: ID): T?\n    protected abstract suspend fun doDelete(id: ID): Boolean\n    \n    suspend fun save(item: T): Result<T> = try {\n        if (doSave(item)) {\n            Result.Success(item)\n        } else {\n            Result.Error(\"Failed to save item\")\n        }\n    } catch (e: Exception) {\n        Result.Error(\"Save operation failed\", e)\n    }\n}\n\n// Classes\nclass InMemoryUserRepository : UserRepository {\n    private val users = ConcurrentHashMap<Long, User>()\n    \n    override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {\n        users[id]\n    }\n    \n    override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {\n        users[user.id] = user\n        true\n    }\n    \n    override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {\n        users.remove(id) != null\n    }\n    \n    override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {\n        users.values.toList()\n    }\n    \n    override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {\n        users.values.filter { it.status == status }\n    }\n}", "line_count": 67}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 142, "end_line": 241, "content": "class UserValidator : Validator<User> {\n    override fun validate(item: User): List<String> {\n        val errors = mutableListOf<String>()\n        \n        if (item.name.isBlank()) {\n            errors.add(\"Name is required\")\n        }\n        \n        if (item.email.isBlank()) {\n            errors.add(\"Email is required\")\n        } else if (!isValidEmail(item.email)) {\n            errors.add(\"Invalid email format\")\n        }\n        \n        return errors\n    }\n    \n    private fun isValidEmail(email: String): <PERSON><PERSON><PERSON> {\n        val emailRegex = Regex(\"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}\")\n        return emailRegex.matches(email)\n    }\n}\n\nclass ConsoleLogger : Logger {\n    override fun log(level: LogLevel, message: String, throwable: Throwable?) {\n        val timestamp = LocalDateTime.now()\n        println(\"[$timestamp] [$level] $message\")\n        throwable?.printStackTrace()\n    }\n}\n\nclass UserService(\n    private val repository: UserRepository,\n    private val validator: Validator<User>,\n    private val logger: Logger\n) {\n    private val eventListeners = mutableListOf<(UserEvent) -> Unit>()\n    \n    suspend fun createUser(name: String, email: String): Result<User> = try {\n        val user = User(\n            id = generateUserId(),\n            name = name,\n            email = email\n        )\n        \n        val validationErrors = validator.validate(user)\n        if (validationErrors.isNotEmpty()) {\n            logger.error(\"Invalid user data: ${validationErrors.joinToString(\", \")}\")\n            return Result.Error(\"Invalid user data: ${validationErrors.joinToString(\", \")}\")\n        }\n        \n        if (repository.save(user)) {\n            logger.info(\"User created successfully: ${user.id}\")\n            notifyListeners(UserEvent.Created(user))\n            Result.Success(user)\n        } else {\n            logger.error(\"Failed to save user\")\n            Result.Error(\"Failed to save user\")\n        }\n    } catch (e: Exception) {\n        logger.error(\"Error creating user\", e)\n        Result.Error(\"Error creating user: ${e.message}\", e)\n    }\n    \n    suspend fun activateUser(id: Long): Boolean = try {\n        val user = repository.findById(id)\n        if (user != null) {\n            user.activate()\n            val success = repository.save(user)\n            if (success) {\n                notifyListeners(UserEvent.StatusChanged(id, UserStatus.ACTIVE))\n            }\n            success\n        } else {\n            logger.error(\"User not found: $id\")\n            false\n        }\n    } catch (e: Exception) {\n        logger.error(\"Error activating user\", e)\n        false\n    }\n    \n    suspend fun getUserStats(): Map<UserStatus, Int> = try {\n        val users = repository.findAll()\n        users.groupingBy { it.status }.eachCount()\n    } catch (e: Exception) {\n        logger.error(\"Error getting user stats\", e)\n        emptyMap()\n    }\n    \n    fun addEventListener(listener: (UserEvent) -> Unit) {\n        eventListeners.add(listener)\n    }\n    \n    private fun notifyListeners(event: UserEvent) {\n        eventListeners.forEach { it(event) }\n    }\n    \n    private fun generateUserId(): Long = System.currentTimeMillis()\n}", "line_count": 100}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 244, "end_line": 306, "content": "fun User.toJson(): String = Json.encodeToString(User.serializer(), this)\n\nfun String.toUser(): User? = try {\n    Json.decodeFromString(User.serializer(), this)\n} catch (e: Exception) {\n    null\n}\n\nfun List<User>.activeUsers(): List<User> = filter { it.isActive }\n\nfun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }\n\n// Higher-order functions\ninline fun <T> measureTime(operation: () -> T): Pair<T, Long> {\n    val startTime = System.currentTimeMillis()\n    val result = operation()\n    val endTime = System.currentTimeMillis()\n    return result to (endTime - startTime)\n}\n\nsuspend fun <T> retryOperation(\n    times: Int = 3,\n    delay: Long = 1000,\n    operation: suspend () -> T\n): T? {\n    repeat(times) { attempt ->\n        try {\n            return operation()\n        } catch (e: Exception) {\n            if (attempt == times - 1) throw e\n            delay(delay)\n        }\n    }\n    return null\n}\n\n// Generic functions\nfun <T> List<T>.chunked(size: Int): List<List<T>> {\n    return if (size <= 0) {\n        throw IllegalArgumentException(\"Size must be positive\")\n    } else {\n        (0 until this.size step size).map { i ->\n            this.subList(i, minOf(i + size, this.size))\n        }\n    }\n}\n\n// Object declarations\nobject UserFactory {\n    fun createSampleUser(): User = User(\n        id = 1L,\n        name = \"John Doe\",\n        email = \"<EMAIL>\"\n    )\n    \n    fun createUsers(count: Int): List<User> = (1..count).map { i ->\n        User(\n            id = i.toLong(),\n            name = \"User $i\",\n            email = \"user$<EMAIL>\"\n        )\n    }\n}", "line_count": 63}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/kotlin/sample.kt", "start_line": 308, "end_line": 384, "content": "object Constants {\n    const val MAX_USERS = 1000\n    const val DEFAULT_PAGE_SIZE = 20\n    const val API_VERSION = \"v1\"\n}\n\n// Companion objects\nclass ApiClient {\n    companion object {\n        private const val BASE_URL = \"https://api.example.com\"\n        \n        fun create(): ApiClient = ApiClient()\n        \n        fun createWithTimeout(timeoutMs: Long): ApiClient {\n            // Implementation would configure timeout\n            return ApiClient()\n        }\n    }\n}\n\n// Main function and example usage\nsuspend fun main() {\n    val logger = ConsoleLogger()\n    val repository = InMemoryUserRepository()\n    val validator = UserValidator()\n    val service = UserService(repository, validator, logger)\n    \n    // Add event listener\n    service.addEventListener { event ->\n        when (event) {\n            is UserEvent.Created -> logger.info(\"User created: ${event.user.name}\")\n            is UserEvent.Updated -> logger.info(\"User updated: ${event.user.name}\")\n            is UserEvent.Deleted -> logger.info(\"User deleted: ${event.userId}\")\n            is UserEvent.StatusChanged -> logger.info(\"User ${event.userId} status changed to ${event.newStatus}\")\n        }\n    }\n    \n    // Create users\n    val (user1Result, time1) = measureTime {\n        runBlocking { service.createUser(\"Alice\", \"<EMAIL>\") }\n    }\n    logger.info(\"User creation took ${time1}ms\")\n    \n    when (user1Result) {\n        is Result.Success -> {\n            logger.info(\"Created user: ${user1Result.data.name}\")\n            \n            // Activate user\n            val activated = service.activateUser(user1Result.data.id)\n            if (activated) {\n                logger.info(\"User activated successfully\")\n            }\n        }\n        is Result.Error -> logger.error(\"Failed to create user: ${user1Result.message}\")\n        is Result.Loading -> logger.info(\"Still loading...\")\n    }\n    \n    // Create multiple users\n    val sampleUsers = UserFactory.createUsers(5)\n    sampleUsers.forEach { user ->\n        repository.save(user)\n    }\n    \n    // Get stats\n    val stats = service.getUserStats()\n    logger.info(\"User statistics: $stats\")\n    \n    // Use extension functions\n    val allUsers = repository.findAll()\n    val activeUsers = allUsers.activeUsers()\n    logger.info(\"Active users: ${activeUsers.size}\")\n    \n    // Process users in chunks\n    allUsers.chunked(2).forEachIndexed { index, chunk ->\n        logger.info(\"Processing chunk $index with ${chunk.size} users\")\n    }\n}", "line_count": 77}], "chunk_count": 5}, "analysis": {"total_lines_in_chunks": 368, "coverage_percentage": 95.58441558441558}}
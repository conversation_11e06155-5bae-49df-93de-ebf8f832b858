FileParser 解析结果报告 - TSX
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx
  文件名: sample.tsx
  内容长度: 13859 字符
  行数: 512


代码块信息 (9 个):
  块 1: 第 9-60 行 (52 行)
      9: interface User {
     10:   id: number;
     11:   name: string;
    ... (还有 49 行)

  块 2: 第 64-138 行 (75 行)
     64:   background-color: ${props => props.theme === 'dark' ? '#333' : '#fff'};
     65:   color: ${props => props.theme === 'dark' ? '#fff' : '#333'};
     66: `;
    ... (还有 72 行)

  块 3: 第 141-182 行 (42 行)
    141: const useUserData = (userId: number | null): AsyncState<User> => {
    142:   const [state, execute] = useAsyncState<User>();
    143: 
    ... (还有 39 行)

  块 4: 第 185-399 行 (215 行)
    185: const UserProfile: React.FC<UserProfileProps> = ({ 
    186:   user, 
    187:   onUpdate, 
    ... (还有 212 行)

  块 5: 第 185-285 行 (101 行)
    185: const UserProfile: React.FC<UserProfileProps> = ({ 
    186:   user, 
    187:   onUpdate, 
    ... (还有 100 行)

  块 6: 第 285-385 行 (101 行)
    285: const UserProfile: React.FC<UserProfileProps> = ({ 
    286: ...
    287:         bio: user.bio || ''
    ... (还有 102 行)

  块 7: 第 385-399 行 (15 行)
    385: const UserProfile: React.FC<UserProfileProps> = ({ 
    386: ...
    387:                     variant="primary"
    ... (还有 14 行)

  块 8: 第 402-440 行 (39 行)
    402: const UserStatsComponent: React.FC<UserStatsProps> = ({ user, compact = false }) => {
    403:   const [expanded, setExpanded] = useState<boolean>(!compact);
    404: 
    ... (还有 36 行)

  块 9: 第 443-507 行 (65 行)
    443: const App: React.FC = () => {
    444:   const [selectedUserId, setSelectedUserId] = useState<number | null>(1);
    445:   const userState = useUserData(selectedUserId);
    ... (还有 62 行)


统计信息:
  覆盖率: 137.7%
  块中总行数: 705

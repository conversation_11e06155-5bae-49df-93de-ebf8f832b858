{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "filename": "sample.tsx", "content_length": 13859, "line_count": 512}, "parsing_results": {"key_structure": "interface User {\n ...\n}\n...\ninterface UserStats {\n ...\n}\n...\ninterface UserPreferences {\n ...\n}\n...\ninterface FormErrors {\n ...\n}\n...\ninterface UserProfileProps {\n ...\n}\n...\ninterface UserStatsProps {\n ...\n}\n...\ninterface LoadingProps {\n ...\n}\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 9, "end_line": 60, "content": "interface User {\n  id: number;\n  name: string;\n  email: string;\n  bio?: string;\n  avatar?: string;\n  role: UserRole;\n  stats?: UserStats;\n  preferences: UserPreferences;\n}\n\ninterface UserStats {\n  posts: number;\n  followers: number;\n  following: number;\n  joinDate: string;\n}\n\ninterface UserPreferences {\n  theme: 'light' | 'dark';\n  notifications: boolean;\n  language: string;\n}\n\ntype UserRole = 'admin' | 'moderator' | 'user' | 'guest';\n\ntype FormData = Pick<User, 'name' | 'email' | 'bio'>;\n\ninterface FormErrors {\n  [key: string]: string | null;\n}\n\n// Props interfaces\ninterface UserProfileProps {\n  user: User | null;\n  onUpdate: (data: FormData) => Promise<void>;\n  className?: string;\n  isEditable?: boolean;\n}\n\ninterface UserStatsProps {\n  user: User;\n  compact?: boolean;\n}\n\ninterface LoadingProps {\n  message?: string;\n  size?: 'small' | 'medium' | 'large';\n}\n\n// Styled components with TypeScript\nconst Container = styled.div<{ theme: 'light' | 'dark' }>`", "line_count": 52}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 64, "end_line": 138, "content": "  background-color: ${props => props.theme === 'dark' ? '#333' : '#fff'};\n  color: ${props => props.theme === 'dark' ? '#fff' : '#333'};\n`;\n\nconst Button = styled.button<{ \n  variant?: 'primary' | 'secondary' | 'danger';\n  size?: 'small' | 'medium' | 'large';\n  disabled?: boolean;\n}>`\n  background-color: ${props => {\n    switch (props.variant) {\n      case 'primary': return '#007bff';\n      case 'danger': return '#dc3545';\n      default: return '#6c757d';\n    }\n  }};\n  color: white;\n  border: none;\n  padding: ${props => {\n    switch (props.size) {\n      case 'small': return '5px 10px';\n      case 'large': return '15px 30px';\n      default: return '10px 20px';\n    }\n  }};\n  border-radius: 4px;\n  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};\n  opacity: ${props => props.disabled ? 0.6 : 1};\n  \n  &:hover {\n    opacity: ${props => props.disabled ? 0.6 : 0.8};\n  }\n`;\n\n// Generic utility types\ntype ApiResponse<T> = {\n  data: T;\n  status: number;\n  message: string;\n};\n\ntype AsyncState<T> = {\n  data: T | null;\n  loading: boolean;\n  error: string | null;\n};\n\n// Custom hook with TypeScript\nconst useAsyncState = <T,>(initialData: T | null = null): [\n  AsyncState<T>,\n  (promise: Promise<T>) => Promise<void>\n] => {\n  const [state, setState] = useState<AsyncState<T>>({\n    data: initialData,\n    loading: false,\n    error: null\n  });\n\n  const execute = useCallback(async (promise: Promise<T>) => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const data = await promise;\n      setState({ data, loading: false, error: null });\n    } catch (error) {\n      setState(prev => ({ \n        ...prev, \n        loading: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      }));\n    }\n  }, []);\n\n  return [state, execute];\n};", "line_count": 75}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 141, "end_line": 182, "content": "const useUserData = (userId: number | null): AsyncState<User> => {\n  const [state, execute] = useAsyncState<User>();\n\n  useEffect(() => {\n    if (!userId) return;\n\n    const fetchUser = async (): Promise<User> => {\n      const response = await fetch(`/api/users/${userId}`);\n      if (!response.ok) {\n        throw new Error(`Failed to fetch user: ${response.statusText}`);\n      }\n      const result: ApiResponse<User> = await response.json();\n      return result.data;\n    };\n\n    execute(fetchUser());\n  }, [userId, execute]);\n\n  return state;\n};\n\n// Loading component\nconst Loading: React.FC<LoadingProps> = ({ \n  message = 'Loading...', \n  size = 'medium' \n}) => {\n  const spinnerSize = {\n    small: '20px',\n    medium: '40px',\n    large: '60px'\n  }[size];\n\n  return (\n    <div className=\"loading-container\">\n      <div \n        className=\"spinner\" \n        style={{ width: spinnerSize, height: spinnerSize }}\n      />\n      <p>{message}</p>\n    </div>\n  );\n};", "line_count": 42}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 185, "end_line": 399, "content": "const UserProfile: React.FC<UserProfileProps> = ({ \n  user, \n  onUpdate, \n  className = '',\n  isEditable = true \n}) => {\n  const [isEditing, setIsEditing] = useState<boolean>(false);\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    bio: ''\n  });\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Initialize form data when user changes\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        bio: user.bio || ''\n      });\n    }\n  }, [user]);\n\n  // Focus management\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Form validation\n  const validateForm = useCallback((data: FormData): FormErrors => {\n    const newErrors: FormErrors = {};\n    \n    if (!data.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    \n    if (!data.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\n      newErrors.email = 'Invalid email format';\n    }\n    \n    return newErrors;\n  }, []);\n\n  // Memoized form validity\n  const isFormValid = useMemo(() => {\n    const validationErrors = validateForm(formData);\n    return Object.keys(validationErrors).length === 0;\n  }, [formData, validateForm]);\n\n  // Input change handler\n  const handleInputChange = useCallback((field: keyof FormData) => \n    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n      const value = event.target.value;\n      setFormData(prev => ({ ...prev, [field]: value }));\n      \n      // Clear error when user starts typing\n      if (errors[field]) {\n        setErrors(prev => ({ ...prev, [field]: null }));\n      }\n    }, [errors]);\n\n  // Form submission\n  const handleSubmit = useCallback(async (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    const validationErrors = validateForm(formData);\n    if (Object.keys(validationErrors).length > 0) {\n      setErrors(validationErrors);\n      return;\n    }\n\n    setIsSubmitting(true);\n    try {\n      await onUpdate(formData);\n      setIsEditing(false);\n      setErrors({});\n    } catch (error) {\n      setErrors({ \n        submit: error instanceof Error ? error.message : 'Failed to update profile' \n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [formData, onUpdate, validateForm]);\n\n  // Cancel editing\n  const handleCancel = useCallback(() => {\n    setIsEditing(false);\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        bio: user.bio || ''\n      });\n    }\n    setErrors({});\n  }, [user]);\n\n  if (!user) {\n    return (\n      <Container theme=\"light\" className={className}>\n        <Loading message=\"Loading user profile...\" />\n      </Container>\n    );\n  }\n\n  return (\n    <Container theme={user.preferences.theme} className={className}>\n      <div className=\"user-profile\">\n        <div className=\"profile-header\">\n          <img \n            src={user.avatar || '/default-avatar.png'} \n            alt={`${user.name}'s avatar`}\n            className=\"avatar\"\n          />\n          \n          <div className=\"user-info\">\n            {isEditing ? (\n              <form onSubmit={handleSubmit} className=\"edit-form\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"name\">Name:</label>\n                  <input\n                    ref={inputRef}\n                    id=\"name\"\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={handleInputChange('name')}\n                    className={errors.name ? 'error' : ''}\n                    disabled={isSubmitting}\n                  />\n                  {errors.name && (\n                    <span className=\"error-message\">{errors.name}</span>\n                  )}\n                </div>\n                \n                <div className=\"form-group\">\n                  <label htmlFor=\"email\">Email:</label>\n                  <input\n                    id=\"email\"\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange('email')}\n                    className={errors.email ? 'error' : ''}\n                    disabled={isSubmitting}\n                  />\n                  {errors.email && (\n                    <span className=\"error-message\">{errors.email}</span>\n                  )}\n                </div>\n                \n                <div className=\"form-group\">\n                  <label htmlFor=\"bio\">Bio:</label>\n                  <textarea\n                    id=\"bio\"\n                    value={formData.bio}\n                    onChange={handleInputChange('bio')}\n                    rows={4}\n                    disabled={isSubmitting}\n                  />\n                </div>\n                \n                {errors.submit && (\n                  <div className=\"error-message\">{errors.submit}</div>\n                )}\n                \n                <div className=\"form-actions\">\n                  <Button \n                    type=\"submit\" \n                    variant=\"primary\" \n                    disabled={!isFormValid || isSubmitting}\n                  >\n                    {isSubmitting ? 'Saving...' : 'Save Changes'}\n                  </Button>\n                  <Button \n                    type=\"button\" \n                    onClick={handleCancel}\n                    disabled={isSubmitting}\n                  >\n                    Cancel\n                  </Button>\n                </div>\n              </form>\n            ) : (\n              <div className=\"profile-display\">\n                <h2>{user.name}</h2>\n                <p className=\"email\">{user.email}</p>\n                <p className=\"role\">Role: {user.role}</p>\n                {user.bio && <p className=\"bio\">{user.bio}</p>}\n                \n                {isEditable && (\n                  <Button \n                    onClick={() => setIsEditing(true)} \n                    variant=\"primary\"\n                  >\n                    Edit Profile\n                  </Button>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n        \n        {user.stats && <UserStatsComponent user={user} />}\n      </div>\n    </Container>\n  );\n};", "line_count": 215}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 185, "end_line": 285, "content": "const UserProfile: React.FC<UserProfileProps> = ({ \n  user, \n  onUpdate, \n  className = '',\n  isEditable = true \n}) => {\n  const [isEditing, setIsEditing] = useState<boolean>(false);\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    bio: ''\n  });\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Initialize form data when user changes\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        bio: user.bio || ''\n      });\n    }\n  }, [user]);\n\n  // Focus management\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Form validation\n  const validateForm = useCallback((data: FormData): FormErrors => {\n    const newErrors: FormErrors = {};\n    \n    if (!data.name.trim()) {\n      newErrors.name = 'Name is required';\n    }\n    \n    if (!data.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\n      newErrors.email = 'Invalid email format';\n    }\n    \n    return newErrors;\n  }, []);\n\n  // Memoized form validity\n  const isFormValid = useMemo(() => {\n    const validationErrors = validateForm(formData);\n    return Object.keys(validationErrors).length === 0;\n  }, [formData, validateForm]);\n\n  // Input change handler\n  const handleInputChange = useCallback((field: keyof FormData) => \n    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n      const value = event.target.value;\n      setFormData(prev => ({ ...prev, [field]: value }));\n      \n      // Clear error when user starts typing\n      if (errors[field]) {\n        setErrors(prev => ({ ...prev, [field]: null }));\n      }\n    }, [errors]);\n\n  // Form submission\n  const handleSubmit = useCallback(async (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    const validationErrors = validateForm(formData);\n    if (Object.keys(validationErrors).length > 0) {\n      setErrors(validationErrors);\n      return;\n    }\n\n    setIsSubmitting(true);\n    try {\n      await onUpdate(formData);\n      setIsEditing(false);\n      setErrors({});\n    } catch (error) {\n      setErrors({ \n        submit: error instanceof Error ? error.message : 'Failed to update profile' \n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [formData, onUpdate, validateForm]);\n\n  // Cancel editing\n  const handleCancel = useCallback(() => {\n    setIsEditing(false);\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        bio: user.bio || ''\n...\n};", "line_count": 101}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 285, "end_line": 385, "content": "const UserProfile: React.FC<UserProfileProps> = ({ \n...\n        bio: user.bio || ''\n      });\n    }\n    setErrors({});\n  }, [user]);\n\n  if (!user) {\n    return (\n      <Container theme=\"light\" className={className}>\n        <Loading message=\"Loading user profile...\" />\n      </Container>\n    );\n  }\n\n  return (\n    <Container theme={user.preferences.theme} className={className}>\n      <div className=\"user-profile\">\n        <div className=\"profile-header\">\n          <img \n            src={user.avatar || '/default-avatar.png'} \n            alt={`${user.name}'s avatar`}\n            className=\"avatar\"\n          />\n          \n          <div className=\"user-info\">\n            {isEditing ? (\n              <form onSubmit={handleSubmit} className=\"edit-form\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"name\">Name:</label>\n                  <input\n                    ref={inputRef}\n                    id=\"name\"\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={handleInputChange('name')}\n                    className={errors.name ? 'error' : ''}\n                    disabled={isSubmitting}\n                  />\n                  {errors.name && (\n                    <span className=\"error-message\">{errors.name}</span>\n                  )}\n                </div>\n                \n                <div className=\"form-group\">\n                  <label htmlFor=\"email\">Email:</label>\n                  <input\n                    id=\"email\"\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange('email')}\n                    className={errors.email ? 'error' : ''}\n                    disabled={isSubmitting}\n                  />\n                  {errors.email && (\n                    <span className=\"error-message\">{errors.email}</span>\n                  )}\n                </div>\n                \n                <div className=\"form-group\">\n                  <label htmlFor=\"bio\">Bio:</label>\n                  <textarea\n                    id=\"bio\"\n                    value={formData.bio}\n                    onChange={handleInputChange('bio')}\n                    rows={4}\n                    disabled={isSubmitting}\n                  />\n                </div>\n                \n                {errors.submit && (\n                  <div className=\"error-message\">{errors.submit}</div>\n                )}\n                \n                <div className=\"form-actions\">\n                  <Button \n                    type=\"submit\" \n                    variant=\"primary\" \n                    disabled={!isFormValid || isSubmitting}\n                  >\n                    {isSubmitting ? 'Saving...' : 'Save Changes'}\n                  </Button>\n                  <Button \n                    type=\"button\" \n                    onClick={handleCancel}\n                    disabled={isSubmitting}\n                  >\n                    Cancel\n                  </Button>\n                </div>\n              </form>\n            ) : (\n              <div className=\"profile-display\">\n                <h2>{user.name}</h2>\n                <p className=\"email\">{user.email}</p>\n                <p className=\"role\">Role: {user.role}</p>\n                {user.bio && <p className=\"bio\">{user.bio}</p>}\n                \n                {isEditable && (\n                  <Button \n                    onClick={() => setIsEditing(true)} \n                    variant=\"primary\"\n...\n};", "line_count": 101}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 385, "end_line": 399, "content": "const UserProfile: React.FC<UserProfileProps> = ({ \n...\n                    variant=\"primary\"\n                  >\n                    Edit Profile\n                  </Button>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n        \n        {user.stats && <UserStatsComponent user={user} />}\n      </div>\n    </Container>\n  );\n};", "line_count": 15}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 402, "end_line": 440, "content": "const UserStatsComponent: React.FC<UserStatsProps> = ({ user, compact = false }) => {\n  const [expanded, setExpanded] = useState<boolean>(!compact);\n\n  const toggleExpanded = useCallback(() => {\n    setExpanded(prev => !prev);\n  }, []);\n\n  if (!user.stats) return null;\n\n  return (\n    <div className=\"user-stats\">\n      <h3 onClick={toggleExpanded} className=\"stats-header\">\n        User Statistics {expanded ? '▼' : '▶'}\n      </h3>\n      {expanded && (\n        <div className=\"stats-content\">\n          <div className=\"stat-item\">\n            <span className=\"stat-label\">Posts:</span>\n            <span className=\"stat-value\">{user.stats.posts}</span>\n          </div>\n          <div className=\"stat-item\">\n            <span className=\"stat-label\">Followers:</span>\n            <span className=\"stat-value\">{user.stats.followers}</span>\n          </div>\n          <div className=\"stat-item\">\n            <span className=\"stat-label\">Following:</span>\n            <span className=\"stat-value\">{user.stats.following}</span>\n          </div>\n          <div className=\"stat-item\">\n            <span className=\"stat-label\">Joined:</span>\n            <span className=\"stat-value\">\n              {new Date(user.stats.joinDate).toLocaleDateString()}\n            </span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};", "line_count": 39}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/tsx/sample.tsx", "start_line": 443, "end_line": 507, "content": "const App: React.FC = () => {\n  const [selectedUserId, setSelectedUserId] = useState<number | null>(1);\n  const userState = useUserData(selectedUserId);\n\n  const handleUserUpdate = useCallback(async (updatedData: FormData): Promise<void> => {\n    const response = await fetch(`/api/users/${selectedUserId}`, {\n      method: 'PUT',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify(updatedData)\n    });\n    \n    if (!response.ok) {\n      throw new Error('Failed to update user');\n    }\n  }, [selectedUserId]);\n\n  const handleUserSelect = useCallback((userId: number) => {\n    setSelectedUserId(userId);\n  }, []);\n\n  if (userState.error) {\n    return (\n      <div className=\"error-container\">\n        <h2>Error</h2>\n        <p>{userState.error}</p>\n        <Button \n          onClick={() => window.location.reload()}\n          variant=\"primary\"\n        >\n          Retry\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <h1>User Profile App</h1>\n        <nav>\n          {[1, 2, 3].map(userId => (\n            <Button \n              key={userId}\n              onClick={() => handleUserSelect(userId)}\n              variant={selectedUserId === userId ? 'primary' : 'secondary'}\n            >\n              User {userId}\n            </Button>\n          ))}\n        </nav>\n      </header>\n      \n      <main className=\"app-main\">\n        {userState.loading ? (\n          <Loading message=\"Loading user profile...\" size=\"large\" />\n        ) : (\n          <UserProfile\n            user={userState.data}\n            onUpdate={handleUserUpdate}\n          />\n        )}\n      </main>\n    </div>\n  );\n};", "line_count": 65}], "chunk_count": 9}, "analysis": {"total_lines_in_chunks": 705, "coverage_percentage": 137.6953125}}
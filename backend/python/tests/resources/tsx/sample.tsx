/**
 * TSX sample file for testing FileParser functionality.
 * This file contains various TSX syntax structures that should be captured by the tree-sitter queries.
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import styled from 'styled-components';

// Type definitions
interface User {
  id: number;
  name: string;
  email: string;
  bio?: string;
  avatar?: string;
  role: UserRole;
  stats?: UserStats;
  preferences: UserPreferences;
}

interface UserStats {
  posts: number;
  followers: number;
  following: number;
  joinDate: string;
}

interface UserPreferences {
  theme: 'light' | 'dark';
  notifications: boolean;
  language: string;
}

type UserRole = 'admin' | 'moderator' | 'user' | 'guest';

type FormData = Pick<User, 'name' | 'email' | 'bio'>;

interface FormErrors {
  [key: string]: string | null;
}

// Props interfaces
interface UserProfileProps {
  user: User | null;
  onUpdate: (data: FormData) => Promise<void>;
  className?: string;
  isEditable?: boolean;
}

interface UserStatsProps {
  user: User;
  compact?: boolean;
}

interface LoadingProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
}

// Styled components with TypeScript
const Container = styled.div<{ theme: 'light' | 'dark' }>`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: ${props => props.theme === 'dark' ? '#333' : '#fff'};
  color: ${props => props.theme === 'dark' ? '#fff' : '#333'};
`;

const Button = styled.button<{ 
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}>`
  background-color: ${props => {
    switch (props.variant) {
      case 'primary': return '#007bff';
      case 'danger': return '#dc3545';
      default: return '#6c757d';
    }
  }};
  color: white;
  border: none;
  padding: ${props => {
    switch (props.size) {
      case 'small': return '5px 10px';
      case 'large': return '15px 30px';
      default: return '10px 20px';
    }
  }};
  border-radius: 4px;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.6 : 1};
  
  &:hover {
    opacity: ${props => props.disabled ? 0.6 : 0.8};
  }
`;

// Generic utility types
type ApiResponse<T> = {
  data: T;
  status: number;
  message: string;
};

type AsyncState<T> = {
  data: T | null;
  loading: boolean;
  error: string | null;
};

// Custom hook with TypeScript
const useAsyncState = <T,>(initialData: T | null = null): [
  AsyncState<T>,
  (promise: Promise<T>) => Promise<void>
] => {
  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null
  });

  const execute = useCallback(async (promise: Promise<T>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const data = await promise;
      setState({ data, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }));
    }
  }, []);

  return [state, execute];
};

// Custom hook for user data
const useUserData = (userId: number | null): AsyncState<User> => {
  const [state, execute] = useAsyncState<User>();

  useEffect(() => {
    if (!userId) return;

    const fetchUser = async (): Promise<User> => {
      const response = await fetch(`/api/users/${userId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch user: ${response.statusText}`);
      }
      const result: ApiResponse<User> = await response.json();
      return result.data;
    };

    execute(fetchUser());
  }, [userId, execute]);

  return state;
};

// Loading component
const Loading: React.FC<LoadingProps> = ({ 
  message = 'Loading...', 
  size = 'medium' 
}) => {
  const spinnerSize = {
    small: '20px',
    medium: '40px',
    large: '60px'
  }[size];

  return (
    <div className="loading-container">
      <div 
        className="spinner" 
        style={{ width: spinnerSize, height: spinnerSize }}
      />
      <p>{message}</p>
    </div>
  );
};

// User Profile component
const UserProfile: React.FC<UserProfileProps> = ({ 
  user, 
  onUpdate, 
  className = '',
  isEditable = true 
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    bio: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        bio: user.bio || ''
      });
    }
  }, [user]);

  // Focus management
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  // Form validation
  const validateForm = useCallback((data: FormData): FormErrors => {
    const newErrors: FormErrors = {};
    
    if (!data.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!data.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      newErrors.email = 'Invalid email format';
    }
    
    return newErrors;
  }, []);

  // Memoized form validity
  const isFormValid = useMemo(() => {
    const validationErrors = validateForm(formData);
    return Object.keys(validationErrors).length === 0;
  }, [formData, validateForm]);

  // Input change handler
  const handleInputChange = useCallback((field: keyof FormData) => 
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const value = event.target.value;
      setFormData(prev => ({ ...prev, [field]: value }));
      
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: null }));
      }
    }, [errors]);

  // Form submission
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    
    const validationErrors = validateForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      await onUpdate(formData);
      setIsEditing(false);
      setErrors({});
    } catch (error) {
      setErrors({ 
        submit: error instanceof Error ? error.message : 'Failed to update profile' 
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, onUpdate, validateForm]);

  // Cancel editing
  const handleCancel = useCallback(() => {
    setIsEditing(false);
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
        bio: user.bio || ''
      });
    }
    setErrors({});
  }, [user]);

  if (!user) {
    return (
      <Container theme="light" className={className}>
        <Loading message="Loading user profile..." />
      </Container>
    );
  }

  return (
    <Container theme={user.preferences.theme} className={className}>
      <div className="user-profile">
        <div className="profile-header">
          <img 
            src={user.avatar || '/default-avatar.png'} 
            alt={`${user.name}'s avatar`}
            className="avatar"
          />
          
          <div className="user-info">
            {isEditing ? (
              <form onSubmit={handleSubmit} className="edit-form">
                <div className="form-group">
                  <label htmlFor="name">Name:</label>
                  <input
                    ref={inputRef}
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange('name')}
                    className={errors.name ? 'error' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.name && (
                    <span className="error-message">{errors.name}</span>
                  )}
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">Email:</label>
                  <input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    className={errors.email ? 'error' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.email && (
                    <span className="error-message">{errors.email}</span>
                  )}
                </div>
                
                <div className="form-group">
                  <label htmlFor="bio">Bio:</label>
                  <textarea
                    id="bio"
                    value={formData.bio}
                    onChange={handleInputChange('bio')}
                    rows={4}
                    disabled={isSubmitting}
                  />
                </div>
                
                {errors.submit && (
                  <div className="error-message">{errors.submit}</div>
                )}
                
                <div className="form-actions">
                  <Button 
                    type="submit" 
                    variant="primary" 
                    disabled={!isFormValid || isSubmitting}
                  >
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </Button>
                  <Button 
                    type="button" 
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            ) : (
              <div className="profile-display">
                <h2>{user.name}</h2>
                <p className="email">{user.email}</p>
                <p className="role">Role: {user.role}</p>
                {user.bio && <p className="bio">{user.bio}</p>}
                
                {isEditable && (
                  <Button 
                    onClick={() => setIsEditing(true)} 
                    variant="primary"
                  >
                    Edit Profile
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
        
        {user.stats && <UserStatsComponent user={user} />}
      </div>
    </Container>
  );
};

// User Stats component
const UserStatsComponent: React.FC<UserStatsProps> = ({ user, compact = false }) => {
  const [expanded, setExpanded] = useState<boolean>(!compact);

  const toggleExpanded = useCallback(() => {
    setExpanded(prev => !prev);
  }, []);

  if (!user.stats) return null;

  return (
    <div className="user-stats">
      <h3 onClick={toggleExpanded} className="stats-header">
        User Statistics {expanded ? '▼' : '▶'}
      </h3>
      {expanded && (
        <div className="stats-content">
          <div className="stat-item">
            <span className="stat-label">Posts:</span>
            <span className="stat-value">{user.stats.posts}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Followers:</span>
            <span className="stat-value">{user.stats.followers}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Following:</span>
            <span className="stat-value">{user.stats.following}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Joined:</span>
            <span className="stat-value">
              {new Date(user.stats.joinDate).toLocaleDateString()}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

// Main App component
const App: React.FC = () => {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(1);
  const userState = useUserData(selectedUserId);

  const handleUserUpdate = useCallback(async (updatedData: FormData): Promise<void> => {
    const response = await fetch(`/api/users/${selectedUserId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedData)
    });
    
    if (!response.ok) {
      throw new Error('Failed to update user');
    }
  }, [selectedUserId]);

  const handleUserSelect = useCallback((userId: number) => {
    setSelectedUserId(userId);
  }, []);

  if (userState.error) {
    return (
      <div className="error-container">
        <h2>Error</h2>
        <p>{userState.error}</p>
        <Button 
          onClick={() => window.location.reload()}
          variant="primary"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>User Profile App</h1>
        <nav>
          {[1, 2, 3].map(userId => (
            <Button 
              key={userId}
              onClick={() => handleUserSelect(userId)}
              variant={selectedUserId === userId ? 'primary' : 'secondary'}
            >
              User {userId}
            </Button>
          ))}
        </nav>
      </header>
      
      <main className="app-main">
        {userState.loading ? (
          <Loading message="Loading user profile..." size="large" />
        ) : (
          <UserProfile
            user={userState.data}
            onUpdate={handleUserUpdate}
          />
        )}
      </main>
    </div>
  );
};

export default App;
export type { User, UserStats, UserPreferences, FormData };
export { UserProfile, UserStatsComponent, useUserData, useAsyncState };

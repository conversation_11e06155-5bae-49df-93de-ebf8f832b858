/* CSS sample file for testing FileParser functionality.
   This file contains various CSS syntax structures that should be captured by the tree-sitter queries. */

/* CSS Variables (Custom Properties) */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
    --text-color: #333;
    --background-color: #fff;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Universal selector */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Element selectors */
body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    font-weight: 600;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

img {
    max-width: 100%;
    height: auto;
}

/* Class selectors */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
}

.btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-accent {
    background-color: var(--accent-color);
}

/* ID selectors */
#header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem 0;
}

#main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

#footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Attribute selectors */
input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 16px;
}

input[required] {
    border-left: 3px solid var(--accent-color);
}

a[href^="http"] {
    color: var(--accent-color);
}

a[href$=".pdf"]::after {
    content: " (PDF)";
    font-size: 0.8em;
    color: #666;
}

/* Pseudo-classes */
.nav-item:first-child {
    margin-left: 0;
}

.nav-item:last-child {
    margin-right: 0;
}

.nav-item:nth-child(odd) {
    background-color: #f9f9f9;
}

.nav-item:nth-child(even) {
    background-color: #fff;
}

.form-group:focus-within {
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Pseudo-elements */
.quote::before {
    content: """;
    font-size: 2em;
    color: var(--primary-color);
}

.quote::after {
    content: """;
    font-size: 2em;
    color: var(--primary-color);
}

.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* Combinators */
.sidebar > .widget {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
}

.nav-menu li + li {
    margin-left: 1rem;
}

.article ~ .article {
    border-top: 1px solid #eee;
    padding-top: 2rem;
}

.header .logo + .nav {
    margin-left: auto;
}

/* Flexbox */
.flex-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.flex-item {
    flex: 1;
    margin: 0 10px;
}

.flex-item:first-child {
    flex: 2;
}

/* Grid */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    grid-gap: 2rem;
    padding: 2rem 0;
}

.grid-item {
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Animations and Transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .flex-container {
        flex-direction: column;
    }
    
    .grid-container {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu {
        display: block;
    }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 960px;
    }
    
    .grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media print {
    .no-print {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    a {
        color: black;
        text-decoration: underline;
    }
}

/* CSS Functions */
.calc-width {
    width: calc(100% - 40px);
    margin: 0 20px;
}

.gradient-background {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.radial-gradient {
    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(52,152,219,1) 100%);
}

.transform-example {
    transform: rotate(45deg) scale(1.2) translateX(10px);
}

/* Advanced selectors */
.card:not(.disabled) {
    cursor: pointer;
}

.input-group:has(input:invalid) {
    border-color: var(--accent-color);
}

.container:where(.dark-theme, .high-contrast) {
    background-color: #222;
    color: white;
}

/* CSS Nesting (future syntax) */
.card {
    padding: 1rem;
    border-radius: var(--border-radius);
    
    &:hover {
        box-shadow: var(--box-shadow);
    }
    
    .card-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .card-content {
        color: #666;
    }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.hidden { display: none; }
.visible { display: block; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

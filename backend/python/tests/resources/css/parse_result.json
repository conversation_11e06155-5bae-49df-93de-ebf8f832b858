{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "filename": "sample.css", "content_length": 6499, "line_count": 378}, "parsing_results": {"key_structure": "    --primary-color: #3498db;\n...\n    max-width: 1200px;\n    margin: 0 auto;\n...\n    width: 100%;\n    padding: 12px;\n...\n    font-size: 2em;\n    color: var(--primary-color);\n...\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n    grid-gap: 2rem;\n...\n        grid-template-columns: 1fr;\n...\n        display: none;\n...\n    cursor: pointer;\n...\n    border-color: var(--accent-color);\n...\n    border: 0;\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 5, "end_line": 57, "content": "    --primary-color: #3498db;\n    --secondary-color: #2ecc71;\n    --accent-color: #e74c3c;\n    --text-color: #333;\n    --background-color: #fff;\n    --border-radius: 8px;\n    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    --transition: all 0.3s ease;\n}\n\n/* Universal selector */\n* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\n\n/* Element selectors */\nbody {\n    font-family: 'Arial', sans-serif;\n    line-height: 1.6;\n    color: var(--text-color);\n    background-color: var(--background-color);\n}\n\nh1, h2, h3, h4, h5, h6 {\n    margin-bottom: 1rem;\n    font-weight: 600;\n}\n\np {\n    margin-bottom: 1rem;\n}\n\na {\n    color: var(--primary-color);\n    text-decoration: none;\n    transition: var(--transition);\n}\n\na:hover {\n    color: var(--secondary-color);\n    text-decoration: underline;\n}\n\nimg {\n    max-width: 100%;\n    height: auto;\n}\n\n/* Class selectors */\n.container {\n    max-width: 1200px;", "line_count": 53}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 58, "end_line": 111, "content": "    margin: 0 auto;\n    padding: 0 20px;\n}\n\n.btn {\n    display: inline-block;\n    padding: 12px 24px;\n    background-color: var(--primary-color);\n    color: white;\n    border: none;\n    border-radius: var(--border-radius);\n    cursor: pointer;\n    transition: var(--transition);\n    font-size: 16px;\n}\n\n.btn:hover {\n    background-color: var(--secondary-color);\n    transform: translateY(-2px);\n    box-shadow: var(--box-shadow);\n}\n\n.btn-secondary {\n    background-color: var(--secondary-color);\n}\n\n.btn-accent {\n    background-color: var(--accent-color);\n}\n\n/* ID selectors */\n#header {\n    background-color: var(--primary-color);\n    color: white;\n    padding: 1rem 0;\n}\n\n#main-content {\n    min-height: calc(100vh - 200px);\n    padding: 2rem 0;\n}\n\n#footer {\n    background-color: #333;\n    color: white;\n    text-align: center;\n    padding: 2rem 0;\n}\n\n/* Attribute selectors */\ninput[type=\"text\"],\ninput[type=\"email\"],\ninput[type=\"password\"] {\n    width: 100%;", "line_count": 54}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 112, "end_line": 162, "content": "    padding: 12px;\n    border: 1px solid #ddd;\n    border-radius: var(--border-radius);\n    font-size: 16px;\n}\n\ninput[required] {\n    border-left: 3px solid var(--accent-color);\n}\n\na[href^=\"http\"] {\n    color: var(--accent-color);\n}\n\na[href$=\".pdf\"]::after {\n    content: \" (PDF)\";\n    font-size: 0.8em;\n    color: #666;\n}\n\n/* Pseudo-classes */\n.nav-item:first-child {\n    margin-left: 0;\n}\n\n.nav-item:last-child {\n    margin-right: 0;\n}\n\n.nav-item:nth-child(odd) {\n    background-color: #f9f9f9;\n}\n\n.nav-item:nth-child(even) {\n    background-color: #fff;\n}\n\n.form-group:focus-within {\n    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);\n}\n\n/* Pseudo-elements */\n.quote::before {\n    content: \"\"\";\n    font-size: 2em;\n    color: var(--primary-color);\n}\n\n.quote::after {\n    content: \"\"\";\n    font-size: 2em;", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 163, "end_line": 213, "content": "    color: var(--primary-color);\n}\n\n.clearfix::after {\n    content: \"\";\n    display: table;\n    clear: both;\n}\n\n/* Combinators */\n.sidebar > .widget {\n    margin-bottom: 2rem;\n    padding: 1rem;\n    background-color: #f9f9f9;\n    border-radius: var(--border-radius);\n}\n\n.nav-menu li + li {\n    margin-left: 1rem;\n}\n\n.article ~ .article {\n    border-top: 1px solid #eee;\n    padding-top: 2rem;\n}\n\n.header .logo + .nav {\n    margin-left: auto;\n}\n\n/* Flexbox */\n.flex-container {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n}\n\n.flex-item {\n    flex: 1;\n    margin: 0 10px;\n}\n\n.flex-item:first-child {\n    flex: 2;\n}\n\n/* Grid */\n.grid-container {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 214, "end_line": 265, "content": "    grid-gap: 2rem;\n    padding: 2rem 0;\n}\n\n.grid-item {\n    background-color: white;\n    padding: 1.5rem;\n    border-radius: var(--border-radius);\n    box-shadow: var(--box-shadow);\n}\n\n/* Animations and Transitions */\n@keyframes fadeIn {\n    from {\n        opacity: 0;\n        transform: translateY(20px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n@keyframes slideIn {\n    0% {\n        transform: translateX(-100%);\n    }\n    100% {\n        transform: translateX(0);\n    }\n}\n\n.fade-in {\n    animation: fadeIn 0.6s ease-out;\n}\n\n.slide-in {\n    animation: slideIn 0.5s ease-out;\n}\n\n/* Media Queries */\n@media screen and (max-width: 768px) {\n    .container {\n        padding: 0 15px;\n    }\n    \n    .flex-container {\n        flex-direction: column;\n    }\n    \n    .grid-container {\n        grid-template-columns: 1fr;", "line_count": 52}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 269, "end_line": 323, "content": "        display: none;\n    }\n    \n    .mobile-menu {\n        display: block;\n    }\n}\n\n@media screen and (min-width: 769px) and (max-width: 1024px) {\n    .container {\n        max-width: 960px;\n    }\n    \n    .grid-container {\n        grid-template-columns: repeat(2, 1fr);\n    }\n}\n\n@media print {\n    .no-print {\n        display: none;\n    }\n    \n    body {\n        font-size: 12pt;\n        line-height: 1.4;\n    }\n    \n    a {\n        color: black;\n        text-decoration: underline;\n    }\n}\n\n/* CSS Functions */\n.calc-width {\n    width: calc(100% - 40px);\n    margin: 0 20px;\n}\n\n.gradient-background {\n    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));\n}\n\n.radial-gradient {\n    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(52,152,219,1) 100%);\n}\n\n.transform-example {\n    transform: rotate(45deg) scale(1.2) translateX(10px);\n}\n\n/* Advanced selectors */\n.card:not(.disabled) {\n    cursor: pointer;", "line_count": 55}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/css/sample.css", "start_line": 327, "end_line": 376, "content": "    border-color: var(--accent-color);\n}\n\n.container:where(.dark-theme, .high-contrast) {\n    background-color: #222;\n    color: white;\n}\n\n/* CSS Nesting (future syntax) */\n.card {\n    padding: 1rem;\n    border-radius: var(--border-radius);\n    \n    &:hover {\n        box-shadow: var(--box-shadow);\n    }\n    \n    .card-title {\n        font-size: 1.5rem;\n        margin-bottom: 0.5rem;\n    }\n    \n    .card-content {\n        color: #666;\n    }\n}\n\n/* Utility classes */\n.text-center { text-align: center; }\n.text-left { text-align: left; }\n.text-right { text-align: right; }\n\n.mt-1 { margin-top: 0.25rem; }\n.mt-2 { margin-top: 0.5rem; }\n.mt-3 { margin-top: 1rem; }\n.mt-4 { margin-top: 1.5rem; }\n\n.hidden { display: none; }\n.visible { display: block; }\n\n.sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;", "line_count": 50}], "chunk_count": 7}, "analysis": {"total_lines_in_chunks": 366, "coverage_percentage": 96.82539682539682}}
// Go sample file for testing FileParser functionality.
// This file contains various Go syntax structures that should be captured by the tree-sitter queries.

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"
)

// Constants
const (
	MaxRetries = 3
	Timeout    = 30 * time.Second
)

// Type definitions
type UserID int64
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
	StatusPending  Status = "pending"
)

// Struct definitions
type User struct {
	ID       UserID    `json:"id"`
	Name     string    `json:"name"`
	Email    string    `json:"email"`
	Status   Status    `json:"status"`
	Created  time.Time `json:"created"`
	Settings *Settings `json:"settings,omitempty"`
}

type Settings struct {
	Theme       string `json:"theme"`
	Notifications bool   `json:"notifications"`
}

// Interface definitions
type UserRepository interface {
	GetUser(ctx context.Context, id UserID) (*User, error)
	CreateUser(ctx context.Context, user *User) error
	UpdateUser(ctx context.Context, user *User) error
	DeleteUser(ctx context.Context, id UserID) error
}

type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, err error, fields ...interface{})
}

// Struct with embedded interface
type UserService struct {
	repo   UserRepository
	logger Logger
	mu     sync.RWMutex
	cache  map[UserID]*User
}

// Constructor function
func NewUserService(repo UserRepository, logger Logger) *UserService {
	return &UserService{
		repo:   repo,
		logger: logger,
		cache:  make(map[UserID]*User),
	}
}

// Method with receiver
func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {
	s.mu.RLock()
	if user, exists := s.cache[id]; exists {
		s.mu.RUnlock()
		return user, nil
	}
	s.mu.RUnlock()

	user, err := s.repo.GetUser(ctx, id)
	if err != nil {
		s.logger.Error("failed to get user", err, "user_id", id)
		return nil, err
	}

	s.mu.Lock()
	s.cache[id] = user
	s.mu.Unlock()

	return user, nil
}

// Method with pointer receiver
func (s *UserService) CreateUser(ctx context.Context, user *User) error {
	if user == nil {
		return fmt.Errorf("user cannot be nil")
	}

	if err := s.validateUser(user); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	user.Created = time.Now()
	user.Status = StatusPending

	if err := s.repo.CreateUser(ctx, user); err != nil {
		s.logger.Error("failed to create user", err, "user", user)
		return err
	}

	s.mu.Lock()
	s.cache[user.ID] = user
	s.mu.Unlock()

	s.logger.Info("user created successfully", "user_id", user.ID)
	return nil
}

// Private method
func (s *UserService) validateUser(user *User) error {
	if user.Name == "" {
		return fmt.Errorf("name is required")
	}
	if user.Email == "" {
		return fmt.Errorf("email is required")
	}
	return nil
}

// Function with multiple return values
func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
	// Implementation would go here
	return 100, 75, nil
}

// Generic function (Go 1.18+)
func Map[T, U any](slice []T, fn func(T) U) []U {
	result := make([]U, len(slice))
	for i, v := range slice {
		result[i] = fn(v)
	}
	return result
}

// Generic type constraint
type Numeric interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 |
		~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |
		~float32 | ~float64
}

// Generic function with constraint
func Sum[T Numeric](values []T) T {
	var sum T
	for _, v := range values {
		sum += v
	}
	return sum
}

// Goroutine and channel usage
func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {
	userChan := make(chan *User, len(userIDs))
	
	go func() {
		defer close(userChan)
		
		var wg sync.WaitGroup
		semaphore := make(chan struct{}, 10) // Limit concurrent goroutines
		
		for _, id := range userIDs {
			wg.Add(1)
			go func(userID UserID) {
				defer wg.Done()
				
				semaphore <- struct{}{} // Acquire
				defer func() { <-semaphore }() // Release
				
				user, err := s.GetUser(ctx, userID)
				if err != nil {
					s.logger.Error("failed to process user", err, "user_id", userID)
					return
				}
				
				select {
				case userChan <- user:
				case <-ctx.Done():
					return
				}
			}(id)
		}
		
		wg.Wait()
	}()
	
	return userChan
}

// HTTP handler function
func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	// Extract user ID from URL or query params
	userIDStr := r.URL.Query().Get("id")
	if userIDStr == "" {
		http.Error(w, "user ID is required", http.StatusBadRequest)
		return
	}
	
	var userID UserID
	if _, err := fmt.Sscanf(userIDStr, "%d", &userID); err != nil {
		http.Error(w, "invalid user ID", http.StatusBadRequest)
		return
	}
	
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		http.Error(w, "user not found", http.StatusNotFound)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(user); err != nil {
		s.logger.Error("failed to encode user", err, "user_id", userID)
		http.Error(w, "internal server error", http.StatusInternalServerError)
		return
	}
}

// Variadic function
func LogFields(msg string, fields ...interface{}) {
	fmt.Printf("%s: %v\n", msg, fields)
}

// Function with defer, panic, and recover
func SafeOperation() (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic recovered: %v", r)
		}
	}()
	
	// Some operation that might panic
	riskyOperation()
	return nil
}

func riskyOperation() {
	// This might panic
	panic("something went wrong")
}

// Init function
func init() {
	log.Println("Package initialized")
}

// Main function
func main() {
	ctx := context.Background()
	
	// Create service
	var repo UserRepository // Would be implemented
	var logger Logger       // Would be implemented
	service := NewUserService(repo, logger)
	
	// Example usage
	user := &User{
		ID:    1,
		Name:  "John Doe",
		Email: "<EMAIL>",
	}
	
	if err := service.CreateUser(ctx, user); err != nil {
		log.Printf("Error creating user: %v", err)
	}
	
	// Generic function usage
	numbers := []int{1, 2, 3, 4, 5}
	doubled := Map(numbers, func(x int) int { return x * 2 })
	fmt.Printf("Doubled: %v\n", doubled)
	
	total := Sum(numbers)
	fmt.Printf("Sum: %d\n", total)
	
	// Channel usage
	userIDs := []UserID{1, 2, 3, 4, 5}
	userChan := service.ProcessUsersAsync(ctx, userIDs)
	
	for user := range userChan {
		fmt.Printf("Processed user: %+v\n", user)
	}
	
	// HTTP server setup
	http.HandleFunc("/user", service.HandleGetUser)
	
	server := &http.Server{
		Addr:         ":8080",
		ReadTimeout:  Timeout,
		WriteTimeout: Timeout,
	}
	
	log.Println("Server starting on :8080")
	if err := server.ListenAndServe(); err != nil {
		log.Printf("Server error: %v", err)
	}
}

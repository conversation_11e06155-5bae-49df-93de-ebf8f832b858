FileParser 解析结果报告 - GO
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go
  文件名: sample.go
  内容长度: 6803 字符
  行数: 313


代码块信息 (5 个):
  块 1: 第 3-54 行 (52 行)
      3: package main
      4: 
      5: import (
    ... (还有 49 行)

  块 2: 第 60-125 行 (66 行)
     60: type UserService struct {
     61: 	repo   UserRepository
     62: 	logger Logger
    ... (还有 63 行)

  块 3: 第 136-199 行 (64 行)
    136: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
    137: 	// Implementation would go here
    138: 	return 100, 75, nil
    ... (还有 61 行)

  块 4: 第 205-259 行 (55 行)
    205: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
    206: 	ctx := r.Context()
    207: 	
    ... (还有 52 行)

  块 5: 第 264-269 行 (6 行)
    264: func main() {
    265: 	ctx := context.Background()
    266: 	
    ... (还有 3 行)


统计信息:
  覆盖率: 77.6%
  块中总行数: 243

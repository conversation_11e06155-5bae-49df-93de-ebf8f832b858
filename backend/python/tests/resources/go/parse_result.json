{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "filename": "sample.go", "content_length": 6803, "line_count": 313}, "parsing_results": {"key_structure": "func NewUserService(repo UserRepository, logger Logger) *UserService {\n                             ...\nfunc (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {\n                                ...\nfunc (s *UserService) CreateUser(ctx context.Context, user *User) error {\n                                  ...\nfunc (s *UserService) validateUser(user *User) error {\n                                  ...\nfunc (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {\n        ...\nfunc Map[T, U any](slice []T, fn func(T) U) []U {\n        ...\nfunc Sum[T Numeric](values []T) T {\n                                       ...\nfunc (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {\n                                   ...\nfunc (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {\n              ...\nfunc LogFields(msg string, fields ...interface{}) {\n                  ...\nfunc SafeOperation() (err error) {\n                   ...\nfunc riskyOperation() {\n         ...\nfunc init() {\n         ...\nfunc main() {\n", "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 3, "end_line": 54, "content": "package main\n\nimport (\n\t\"context\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"log\"\n\t\"net/http\"\n\t\"sync\"\n\t\"time\"\n)\n\n// Constants\nconst (\n\tMaxRetries = 3\n\tTimeout    = 30 * time.Second\n)\n\n// Type definitions\ntype UserID int64\ntype Status string\n\nconst (\n\tStatusActive   Status = \"active\"\n\tStatusInactive Status = \"inactive\"\n\tStatusPending  Status = \"pending\"\n)\n\n// Struct definitions\ntype User struct {\n\tID       UserID    `json:\"id\"`\n\tName     string    `json:\"name\"`\n\tEmail    string    `json:\"email\"`\n\tStatus   Status    `json:\"status\"`\n\tCreated  time.Time `json:\"created\"`\n\tSettings *Settings `json:\"settings,omitempty\"`\n}\n\ntype Settings struct {\n\tTheme       string `json:\"theme\"`\n\tNotifications bool   `json:\"notifications\"`\n}\n\n// Interface definitions\ntype UserRepository interface {\n\tGetUser(ctx context.Context, id UserID) (*User, error)\n\tCreateUser(ctx context.Context, user *User) error\n\tUpdateUser(ctx context.Context, user *User) error\n\tDeleteUser(ctx context.Context, id UserID) error\n}\n\ntype Logger interface {", "line_count": 52}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 60, "end_line": 125, "content": "type UserService struct {\n\trepo   UserRepository\n\tlogger Logger\n\tmu     sync.RWMutex\n\tcache  map[UserID]*User\n}\n\n// Constructor function\nfunc NewUserService(repo UserRepository, logger Logger) *UserService {\n\treturn &UserService{\n\t\trepo:   repo,\n\t\tlogger: logger,\n\t\tcache:  make(map[UserID]*User),\n\t}\n}\n\n// Method with receiver\nfunc (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {\n\ts.mu.RLock()\n\tif user, exists := s.cache[id]; exists {\n\t\ts.mu.RUnlock()\n\t\treturn user, nil\n\t}\n\ts.mu.RUnlock()\n\n\tuser, err := s.repo.GetUser(ctx, id)\n\tif err != nil {\n\t\ts.logger.Error(\"failed to get user\", err, \"user_id\", id)\n\t\treturn nil, err\n\t}\n\n\ts.mu.Lock()\n\ts.cache[id] = user\n\ts.mu.Unlock()\n\n\treturn user, nil\n}\n\n// Method with pointer receiver\nfunc (s *UserService) CreateUser(ctx context.Context, user *User) error {\n\tif user == nil {\n\t\treturn fmt.Errorf(\"user cannot be nil\")\n\t}\n\n\tif err := s.validateUser(user); err != nil {\n\t\treturn fmt.Errorf(\"validation failed: %w\", err)\n\t}\n\n\tuser.Created = time.Now()\n\tuser.Status = StatusPending\n\n\tif err := s.repo.CreateUser(ctx, user); err != nil {\n\t\ts.logger.Error(\"failed to create user\", err, \"user\", user)\n\t\treturn err\n\t}\n\n\ts.mu.Lock()\n\ts.cache[user.ID] = user\n\ts.mu.Unlock()\n\n\ts.logger.Info(\"user created successfully\", \"user_id\", user.ID)\n\treturn nil\n}\n\n// Private method\nfunc (s *UserService) validateUser(user *User) error {", "line_count": 66}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 136, "end_line": 199, "content": "func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {\n\t// Implementation would go here\n\treturn 100, 75, nil\n}\n\n// Generic function (Go 1.18+)\nfunc Map[T, U any](slice []T, fn func(T) U) []U {\n\tresult := make([]U, len(slice))\n\tfor i, v := range slice {\n\t\tresult[i] = fn(v)\n\t}\n\treturn result\n}\n\n// Generic type constraint\ntype Numeric interface {\n\t~int | ~int8 | ~int16 | ~int32 | ~int64 |\n\t\t~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 |\n\t\t~float32 | ~float64\n}\n\n// Generic function with constraint\nfunc Sum[T Numeric](values []T) T {\n\tvar sum T\n\tfor _, v := range values {\n\t\tsum += v\n\t}\n\treturn sum\n}\n\n// Goroutine and channel usage\nfunc (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {\n\tuserChan := make(chan *User, len(userIDs))\n\t\n\tgo func() {\n\t\tdefer close(userChan)\n\t\t\n\t\tvar wg sync.WaitGroup\n\t\tsemaphore := make(chan struct{}, 10) // Limit concurrent goroutines\n\t\t\n\t\tfor _, id := range userIDs {\n\t\t\twg.Add(1)\n\t\t\tgo func(userID UserID) {\n\t\t\t\tdefer wg.Done()\n\t\t\t\t\n\t\t\t\tsemaphore <- struct{}{} // Acquire\n\t\t\t\tdefer func() { <-semaphore }() // Release\n\t\t\t\t\n\t\t\t\tuser, err := s.GetUser(ctx, userID)\n\t\t\t\tif err != nil {\n\t\t\t\t\ts.logger.Error(\"failed to process user\", err, \"user_id\", userID)\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tselect {\n\t\t\t\tcase userChan <- user:\n\t\t\t\tcase <-ctx.Done():\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t}(id)\n\t\t}\n\t\t\n\t\twg.Wait()\n\t}()", "line_count": 64}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 205, "end_line": 259, "content": "func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {\n\tctx := r.Context()\n\t\n\t// Extract user ID from URL or query params\n\tuserIDStr := r.URL.Query().Get(\"id\")\n\tif userIDStr == \"\" {\n\t\thttp.Error(w, \"user ID is required\", http.StatusBadRequest)\n\t\treturn\n\t}\n\t\n\tvar userID UserID\n\tif _, err := fmt.Sscanf(userIDStr, \"%d\", &userID); err != nil {\n\t\thttp.Error(w, \"invalid user ID\", http.StatusBadRequest)\n\t\treturn\n\t}\n\t\n\tuser, err := s.GetUser(ctx, userID)\n\tif err != nil {\n\t\thttp.Error(w, \"user not found\", http.StatusNotFound)\n\t\treturn\n\t}\n\t\n\tw.Header().Set(\"Content-Type\", \"application/json\")\n\tif err := json.NewEncoder(w).Encode(user); err != nil {\n\t\ts.logger.Error(\"failed to encode user\", err, \"user_id\", userID)\n\t\thttp.Error(w, \"internal server error\", http.StatusInternalServerError)\n\t\treturn\n\t}\n}\n\n// Variadic function\nfunc LogFields(msg string, fields ...interface{}) {\n\tfmt.Printf(\"%s: %v\\n\", msg, fields)\n}\n\n// Function with defer, panic, and recover\nfunc SafeOperation() (err error) {\n\tdefer func() {\n\t\tif r := recover(); r != nil {\n\t\t\terr = fmt.Errorf(\"panic recovered: %v\", r)\n\t\t}\n\t}()\n\t\n\t// Some operation that might panic\n\triskyOperation()\n\treturn nil\n}\n\nfunc riskyOperation() {\n\t// This might panic\n\tpanic(\"something went wrong\")\n}\n\n// Init function\nfunc init() {", "line_count": 55}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go", "start_line": 264, "end_line": 269, "content": "func main() {\n\tctx := context.Background()\n\t\n\t// Create service\n\tvar repo UserRepository // Would be implemented\n\tvar logger Logger       // Would be implemented", "line_count": 6}], "chunk_count": 5}, "analysis": {"total_lines_in_chunks": 243, "coverage_percentage": 77.63578274760383}}
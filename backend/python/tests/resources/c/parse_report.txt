FileParser 解析结果报告 - C
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c
  文件名: sample.c
  内容长度: 4685 字符
  行数: 247


代码块信息 (4 个):
  块 1: 第 5-56 行 (52 行)
      5: #include <stdio.h>
      6: #include <stdlib.h>
      7: #include <string.h>
    ... (还有 49 行)

  块 2: 第 58-114 行 (57 行)
     58: void print_array(int arr[], int size) {
     59:     printf("Array: ");
     60:     for (int i = 0; i < size; i++) {
    ... (还有 54 行)

  块 3: 第 117-167 行 (51 行)
    117: void swap(int* a, int* b) {
    118:     int temp = *a;
    119:     *a = *b;
    ... (还有 48 行)

  块 4: 第 169-246 行 (78 行)
    169: void print_list(Node* head) {
    170:     Node* current = head;
    171:     while (current != NULL) {
    ... (还有 75 行)


统计信息:
  覆盖率: 96.4%
  块中总行数: 238

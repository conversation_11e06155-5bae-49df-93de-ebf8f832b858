/**
 * C sample file for testing FileParser functionality.
 * This file contains various C syntax structures that should be captured by the tree-sitter queries.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

// Macro definitions
#define MAX_SIZE 100
#define PI 3.14159
#define SQUARE(x) ((x) * (x))

// Type definitions
typedef struct {
    int x;
    int y;
} Point;

typedef struct Node {
    int data;
    struct Node* next;
} Node;

typedef enum {
    RED,
    GREEN,
    BLUE
} Color;

typedef union {
    int i;
    float f;
    char c;
} Value;

// Global variables
int global_counter = 0;
static int static_global = 42;
extern int external_var;

// Function prototypes
int add(int a, int b);
void print_array(int arr[], int size);
Point* create_point(int x, int y);
void free_point(Point* p);

// Function definitions
int add(int a, int b) {
    return a + b;
}

int subtract(int a, int b) {
    return a - b;
}

void print_array(int arr[], int size) {
    printf("Array: ");
    for (int i = 0; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
}

Point* create_point(int x, int y) {
    Point* p = (Point*)malloc(sizeof(Point));
    if (p != NULL) {
        p->x = x;
        p->y = y;
    }
    return p;
}

void free_point(Point* p) {
    if (p != NULL) {
        free(p);
    }
}

// Function with static storage class
static int get_next_id(void) {
    static int id = 0;
    return ++id;
}

// Function with inline keyword
inline int max(int a, int b) {
    return (a > b) ? a : b;
}

// Recursive function
int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

// Function with variable arguments
#include <stdarg.h>

int sum_all(int count, ...) {
    va_list args;
    va_start(args, count);
    
    int sum = 0;
    for (int i = 0; i < count; i++) {
        sum += va_arg(args, int);
    }
    
    va_end(args);
    return sum;
}

// Pointer functions
void swap(int* a, int* b) {
    int temp = *a;
    *a = *b;
    *b = temp;
}

// Function pointer
int (*operation)(int, int) = add;

// Array processing function
void process_matrix(int matrix[][3], int rows) {
    for (int i = 0; i < rows; i++) {
        for (int j = 0; j < 3; j++) {
            matrix[i][j] *= 2;
        }
    }
}

// String manipulation function
char* string_duplicate(const char* src) {
    if (src == NULL) {
        return NULL;
    }
    
    size_t len = strlen(src);
    char* dest = (char*)malloc(len + 1);
    
    if (dest != NULL) {
        strcpy(dest, src);
    }
    
    return dest;
}

// Linked list operations
Node* create_node(int data) {
    Node* node = (Node*)malloc(sizeof(Node));
    if (node != NULL) {
        node->data = data;
        node->next = NULL;
    }
    return node;
}

void insert_node(Node** head, int data) {
    Node* new_node = create_node(data);
    if (new_node != NULL) {
        new_node->next = *head;
        *head = new_node;
    }
}

void print_list(Node* head) {
    Node* current = head;
    while (current != NULL) {
        printf("%d -> ", current->data);
        current = current->next;
    }
    printf("NULL\n");
}

void free_list(Node* head) {
    Node* current = head;
    while (current != NULL) {
        Node* next = current->next;
        free(current);
        current = next;
    }
}

// Main function
int main(int argc, char* argv[]) {
    printf("C Sample Program\n");
    
    // Variable declarations
    int numbers[] = {1, 2, 3, 4, 5};
    int size = sizeof(numbers) / sizeof(numbers[0]);
    
    // Function calls
    print_array(numbers, size);
    
    int result = add(10, 20);
    printf("10 + 20 = %d\n", result);
    
    // Pointer usage
    Point* p1 = create_point(3, 4);
    if (p1 != NULL) {
        printf("Point: (%d, %d)\n", p1->x, p1->y);
        free_point(p1);
    }
    
    // Linked list example
    Node* list = NULL;
    insert_node(&list, 1);
    insert_node(&list, 2);
    insert_node(&list, 3);
    
    printf("Linked list: ");
    print_list(list);
    
    free_list(list);
    
    // Control structures
    for (int i = 0; i < 5; i++) {
        if (i % 2 == 0) {
            printf("%d is even\n", i);
        } else {
            printf("%d is odd\n", i);
        }
    }
    
    // Switch statement
    Color color = RED;
    switch (color) {
        case RED:
            printf("Color is red\n");
            break;
        case GREEN:
            printf("Color is green\n");
            break;
        case BLUE:
            printf("Color is blue\n");
            break;
        default:
            printf("Unknown color\n");
            break;
    }
    
    return 0;
}

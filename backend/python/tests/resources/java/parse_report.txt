FileParser 解析结果报告 - JAVA
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java
  文件名: Sample.java
  内容长度: 4306 字符
  行数: 180


代码块信息 (3 个):
  块 1: 第 2-88 行 (87 行)
      2: import java.util.*;
      3: import java.util.function.Function;
      4: import java.util.stream.Collectors;
    ... (还有 84 行)

  块 2: 第 90-140 行 (51 行)
     90: // Interface declaration
     91: interface Drawable {
     92:     void draw();
    ... (还有 48 行)

  块 3: 第 141-179 行 (39 行)
    141: @interface MyAnnotation {
    142:     String value() default "";
    143:     int priority() default 0;
    ... (还有 36 行)


统计信息:
  覆盖率: 98.3%
  块中总行数: 177

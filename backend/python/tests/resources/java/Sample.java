package com.example.test;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Java sample file for testing FileParser functionality.
 * This file contains various Java syntax structures that should be captured by the tree-sitter queries.
 */

// Line comment example
public class Sample {
    
    // Field declarations
    private static final String CONSTANT = "Hello World";
    private int instanceField;
    protected List<String> protectedField;
    public Map<String, Integer> publicField;
    
    // Constructor
    public Sample() {
        this.instanceField = 0;
        this.protectedField = new ArrayList<>();
        this.publicField = new HashMap<>();
    }
    
    // Parameterized constructor
    public Sample(int value, List<String> items) {
        this.instanceField = value;
        this.protectedField = new ArrayList<>(items);
        this.publicField = new HashMap<>();
    }
    
    // Method declarations
    public void publicMethod() {
        System.out.println("Public method called");
    }
    
    private int privateMethod(int a, int b) {
        return a + b;
    }
    
    protected static String staticMethod(String input) {
        return input.toUpperCase();
    }
    
    // Method with generic parameters
    public <T> List<T> genericMethod(T item, int count) {
        List<T> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            result.add(item);
        }
        return result;
    }
    
    // Lambda expressions
    public void lambdaExamples() {
        Function<String, Integer> stringLength = s -> s.length();
        Function<Integer, Integer> square = x -> x * x;
        
        List<String> words = Arrays.asList("hello", "world", "java");
        List<Integer> lengths = words.stream()
            .map(stringLength)
            .collect(Collectors.toList());
    }
    
    // Inner class
    public class InnerClass {
        private String innerField;
        
        public InnerClass(String value) {
            this.innerField = value;
        }
        
        public String getInnerField() {
            return innerField;
        }
    }
    
    // Static nested class
    public static class StaticNestedClass {
        private static int staticNestedField = 100;
        
        public static void staticNestedMethod() {
            System.out.println("Static nested method");
        }
    }
}

// Interface declaration
interface Drawable {
    void draw();
    
    default void print() {
        System.out.println("Drawing something");
    }
    
    static void staticInterfaceMethod() {
        System.out.println("Static interface method");
    }
}

// Enum declaration
enum Color {
    RED("Red Color"),
    GREEN("Green Color"),
    BLUE("Blue Color");
    
    private final String description;
    
    Color(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

// Record declaration (Java 14+)
record Person(String name, int age, String email) {
    // Compact constructor
    public Person {
        if (age < 0) {
            throw new IllegalArgumentException("Age cannot be negative");
        }
    }
    
    // Additional constructor
    public Person(String name, int age) {
        this(name, age, null);
    }
    
    // Instance method
    public String getDisplayName() {
        return name + " (" + age + ")";
    }
}

// Annotation declaration
@interface MyAnnotation {
    String value() default "";
    int priority() default 0;
}

// Abstract class
abstract class Shape {
    protected String color;
    
    public Shape(String color) {
        this.color = color;
    }
    
    public abstract double getArea();
    
    public String getColor() {
        return color;
    }
}

// Concrete class extending abstract class
class Circle extends Shape implements Drawable {
    private double radius;
    
    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }
    
    @Override
    public double getArea() {
        return Math.PI * radius * radius;
    }
    
    @Override
    public void draw() {
        System.out.println("Drawing a " + color + " circle");
    }
}

/**
 * TypeScript sample file for testing FileParser functionality.
 * This file contains various TypeScript syntax structures that should be captured by the tree-sitter queries.
 */

// Type definitions
type StringOrNumber = string | number;
type UserRole = 'admin' | 'user' | 'guest';

// Interface declarations
interface User {
    id: number;
    name: string;
    email?: string;
    role: UserRole;
    readonly createdAt: Date;
}

interface ApiResponse<T> {
    data: T;
    status: number;
    message: string;
}

// Generic interface
interface Repository<T> {
    findById(id: number): Promise<T | null>;
    save(entity: T): Promise<T>;
    delete(id: number): Promise<void>;
}

// Class with TypeScript features
class UserService implements Repository<User> {
    private users: User[] = [];
    
    constructor(private apiUrl: string) {}
    
    async findById(id: number): Promise<User | null> {
        return this.users.find(user => user.id === id) || null;
    }
    
    async save(user: User): Promise<User> {
        this.users.push(user);
        return user;
    }
    
    async delete(id: number): Promise<void> {
        this.users = this.users.filter(user => user.id !== id);
    }
    
    // Method with generic type parameter
    async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {
        return this.users.filter(user => user[field] === value);
    }
}

// Abstract class
abstract class Shape {
    protected constructor(protected color: string) {}
    
    abstract getArea(): number;
    abstract getPerimeter(): number;
    
    getColor(): string {
        return this.color;
    }
}

// Concrete class extending abstract class
class Circle extends Shape {
    constructor(color: string, private radius: number) {
        super(color);
    }
    
    getArea(): number {
        return Math.PI * this.radius ** 2;
    }
    
    getPerimeter(): number {
        return 2 * Math.PI * this.radius;
    }
}

// Enum declarations
enum Status {
    PENDING = 'pending',
    APPROVED = 'approved',
    REJECTED = 'rejected'
}

enum Direction {
    UP,
    DOWN,
    LEFT,
    RIGHT
}

// Function with type annotations
function processData<T>(
    data: T[],
    predicate: (item: T) => boolean,
    transformer?: (item: T) => T
): T[] {
    let filtered = data.filter(predicate);
    
    if (transformer) {
        filtered = filtered.map(transformer);
    }
    
    return filtered;
}

// Arrow function with types
const calculateTotal = (items: { price: number; quantity: number }[]): number => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

// Generic function
const identity = <T>(arg: T): T => arg;

// Function overloads
function combine(a: string, b: string): string;
function combine(a: number, b: number): number;
function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {
    return (a as any) + (b as any);
}

// Namespace
namespace Utils {
    export function formatDate(date: Date): string {
        return date.toISOString().split('T')[0];
    }
    
    export function isValidEmail(email: string): boolean {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
    
    export class Logger {
        static log(message: string): void {
            console.log(`[${new Date().toISOString()}] ${message}`);
        }
    }
}

// Module augmentation
declare global {
    interface Window {
        customProperty: string;
    }
}

// Conditional types
type NonNullable<T> = T extends null | undefined ? never : T;
type ReturnType<T> = T extends (...args: any[]) => infer R ? R : any;

// Mapped types
type Partial<T> = {
    [P in keyof T]?: T[P];
};

type Required<T> = {
    [P in keyof T]-?: T[P];
};

// Utility types usage
type PartialUser = Partial<User>;
type RequiredUser = Required<User>;

// Decorators (experimental)
function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
        console.log(`Calling ${propertyName} with args:`, args);
        const result = method.apply(this, args);
        console.log(`Result:`, result);
        return result;
    };
}

class Calculator {
    @logged
    add(a: number, b: number): number {
        return a + b;
    }
    
    @logged
    multiply(a: number, b: number): number {
        return a * b;
    }
}

// Type guards
function isString(value: any): value is string {
    return typeof value === 'string';
}

function isUser(obj: any): obj is User {
    return obj && typeof obj.id === 'number' && typeof obj.name === 'string';
}

// Assertion functions
function assertIsNumber(value: any): asserts value is number {
    if (typeof value !== 'number') {
        throw new Error('Expected number');
    }
}

// Module exports
export { User, UserService, Status, Direction };
export type { ApiResponse, Repository, StringOrNumber };
export default Calculator;

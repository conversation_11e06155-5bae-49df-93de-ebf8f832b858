<?php
/**
 * PHP sample file for testing FileParser functionality.
 * This file contains various PHP syntax structures that should be captured by the tree-sitter queries.
 */

declare(strict_types=1);

namespace App\Models;

use DateTime;
use Exception;
use JsonSerializable;
use PDO;
use PDOException;

// Constants
const MAX_USERS = 1000;
const DEFAULT_ROLE = 'user';

// Global variables
$globalCounter = 0;

// Enums (PHP 8.1+)
enum UserStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case SUSPENDED = 'suspended';
    case PENDING = 'pending';
    
    public function getLabel(): string
    {
        return match($this) {
            self::ACTIVE => 'Active User',
            self::INACTIVE => 'Inactive User',
            self::SUSPENDED => 'Suspended User',
            self::PENDING => 'Pending Verification',
        };
    }
}

// Interface definitions
interface UserRepositoryInterface
{
    public function findById(int $id): ?User;
    public function save(User $user): bool;
    public function delete(int $id): bool;
    public function findAll(): array;
}

interface LoggerInterface
{
    public function info(string $message, array $context = []): void;
    public function error(string $message, array $context = []): void;
}

// Trait definitions
trait Timestampable
{
    protected DateTime $createdAt;
    protected DateTime $updatedAt;
    
    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }
    
    public function getUpdatedAt(): DateTime
    {
        return $this->updatedAt;
    }
    
    public function touch(): void
    {
        $this->updatedAt = new DateTime();
    }
}

trait Validatable
{
    abstract public function validate(): array;
    
    public function isValid(): bool
    {
        return empty($this->validate());
    }
}

// Abstract class
abstract class BaseModel implements JsonSerializable
{
    use Timestampable;
    
    protected int $id;
    
    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }
    
    public function getId(): int
    {
        return $this->id;
    }
    
    public function setId(int $id): void
    {
        $this->id = $id;
    }
    
    abstract public function toArray(): array;
    
    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}

// Class definitions
class User extends BaseModel
{
    use Validatable;
    
    private string $name;
    private string $email;
    private UserStatus $status;
    private array $roles = [];
    private ?string $avatar = null;
    
    // Constructor with property promotion (PHP 8.0+)
    public function __construct(
        string $name,
        string $email,
        UserStatus $status = UserStatus::PENDING
    ) {
        parent::__construct();
        $this->name = $name;
        $this->email = $email;
        $this->status = $status;
        $this->roles = [DEFAULT_ROLE];
    }
    
    // Getters
    public function getName(): string
    {
        return $this->name;
    }
    
    public function getEmail(): string
    {
        return $this->email;
    }
    
    public function getStatus(): UserStatus
    {
        return $this->status;
    }
    
    public function getRoles(): array
    {
        return $this->roles;
    }
    
    public function getAvatar(): ?string
    {
        return $this->avatar;
    }
    
    // Setters
    public function setName(string $name): self
    {
        $this->name = $name;
        $this->touch();
        return $this;
    }
    
    public function setEmail(string $email): self
    {
        $this->email = $email;
        $this->touch();
        return $this;
    }
    
    public function setStatus(UserStatus $status): self
    {
        $this->status = $status;
        $this->touch();
        return $this;
    }
    
    public function setAvatar(?string $avatar): self
    {
        $this->avatar = $avatar;
        $this->touch();
        return $this;
    }
    
    // Role management
    public function addRole(string $role): self
    {
        if (!in_array($role, $this->roles)) {
            $this->roles[] = $role;
            $this->touch();
        }
        return $this;
    }
    
    public function removeRole(string $role): self
    {
        $this->roles = array_filter($this->roles, fn($r) => $r !== $role);
        $this->touch();
        return $this;
    }
    
    public function hasRole(string $role): bool
    {
        return in_array($role, $this->roles);
    }
    
    // Status checks
    public function isActive(): bool
    {
        return $this->status === UserStatus::ACTIVE;
    }
    
    public function activate(): self
    {
        return $this->setStatus(UserStatus::ACTIVE);
    }
    
    public function suspend(): self
    {
        return $this->setStatus(UserStatus::SUSPENDED);
    }
    
    // Validation
    public function validate(): array
    {
        $errors = [];
        
        if (empty($this->name)) {
            $errors[] = 'Name is required';
        }
        
        if (empty($this->email)) {
            $errors[] = 'Email is required';
        } elseif (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }
        
        return $errors;
    }
    
    // Serialization
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'status' => $this->status->value,
            'roles' => $this->roles,
            'avatar' => $this->avatar,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),
        ];
    }
    
    // Magic methods
    public function __toString(): string
    {
        return sprintf('%s <%s>', $this->name, $this->email);
    }
    
    public function __clone()
    {
        $this->id = 0;
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }
}

// Repository implementation
class UserRepository implements UserRepositoryInterface
{
    private PDO $pdo;
    private LoggerInterface $logger;
    
    public function __construct(PDO $pdo, LoggerInterface $logger)
    {
        $this->pdo = $pdo;
        $this->logger = $logger;
    }
    
    public function findById(int $id): ?User
    {
        try {
            $stmt = $this->pdo->prepare('SELECT * FROM users WHERE id = :id');
            $stmt->execute(['id' => $id]);
            $data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$data) {
                return null;
            }
            
            return $this->hydrate($data);
        } catch (PDOException $e) {
            $this->logger->error('Failed to find user by ID', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    public function save(User $user): bool
    {
        try {
            if ($user->getId()) {
                return $this->update($user);
            } else {
                return $this->insert($user);
            }
        } catch (PDOException $e) {
            $this->logger->error('Failed to save user', [
                'user' => $user->toArray(),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    public function delete(int $id): bool
    {
        try {
            $stmt = $this->pdo->prepare('DELETE FROM users WHERE id = :id');
            $result = $stmt->execute(['id' => $id]);
            
            $this->logger->info('User deleted', ['id' => $id]);
            return $result;
        } catch (PDOException $e) {
            $this->logger->error('Failed to delete user', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    public function findAll(): array
    {
        try {
            $stmt = $this->pdo->query('SELECT * FROM users ORDER BY created_at DESC');
            $users = [];
            
            while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $users[] = $this->hydrate($data);
            }
            
            return $users;
        } catch (PDOException $e) {
            $this->logger->error('Failed to find all users', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    private function insert(User $user): bool
    {
        $sql = 'INSERT INTO users (name, email, status, roles, avatar, created_at, updated_at) 
                VALUES (:name, :email, :status, :roles, :avatar, :created_at, :updated_at)';
        
        $stmt = $this->pdo->prepare($sql);
        $result = $stmt->execute([
            'name' => $user->getName(),
            'email' => $user->getEmail(),
            'status' => $user->getStatus()->value,
            'roles' => json_encode($user->getRoles()),
            'avatar' => $user->getAvatar(),
            'created_at' => $user->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s'),
        ]);
        
        if ($result) {
            $user->setId((int) $this->pdo->lastInsertId());
        }
        
        return $result;
    }
    
    private function update(User $user): bool
    {
        $sql = 'UPDATE users SET name = :name, email = :email, status = :status, 
                roles = :roles, avatar = :avatar, updated_at = :updated_at 
                WHERE id = :id';
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            'id' => $user->getId(),
            'name' => $user->getName(),
            'email' => $user->getEmail(),
            'status' => $user->getStatus()->value,
            'roles' => json_encode($user->getRoles()),
            'avatar' => $user->getAvatar(),
            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s'),
        ]);
    }
    
    private function hydrate(array $data): User
    {
        $user = new User(
            $data['name'],
            $data['email'],
            UserStatus::from($data['status'])
        );
        
        $user->setId((int) $data['id']);
        $user->setAvatar($data['avatar']);
        
        // Set roles
        $roles = json_decode($data['roles'], true) ?: [];
        foreach ($roles as $role) {
            $user->addRole($role);
        }
        
        return $user;
    }
}

// Service class
class UserService
{
    private UserRepositoryInterface $repository;
    private LoggerInterface $logger;
    
    public function __construct(
        UserRepositoryInterface $repository,
        LoggerInterface $logger
    ) {
        $this->repository = $repository;
        $this->logger = $logger;
    }
    
    public function createUser(string $name, string $email): ?User
    {
        $user = new User($name, $email);
        
        if (!$user->isValid()) {
            $this->logger->error('Invalid user data', [
                'errors' => $user->validate()
            ]);
            return null;
        }
        
        if ($this->repository->save($user)) {
            $this->logger->info('User created', ['user_id' => $user->getId()]);
            return $user;
        }
        
        return null;
    }
    
    public function activateUser(int $id): bool
    {
        $user = $this->repository->findById($id);
        
        if (!$user) {
            $this->logger->error('User not found for activation', ['id' => $id]);
            return false;
        }
        
        $user->activate();
        return $this->repository->save($user);
    }
}

// Functions
function generateRandomString(int $length = 10): string
{
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

function validateEmail(string $email): bool
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Arrow functions (PHP 7.4+)
$multiply = fn($a, $b) => $a * $b;
$isEven = fn($n) => $n % 2 === 0;

// Anonymous functions
$greet = function(string $name): string {
    return "Hello, {$name}!";
};

// Closure with use
$prefix = 'Mr. ';
$formatName = function(string $name) use ($prefix): string {
    return $prefix . $name;
};

// Example usage
try {
    $pdo = new PDO('sqlite::memory:');
    $logger = new class implements LoggerInterface {
        public function info(string $message, array $context = []): void {
            echo "[INFO] {$message}\n";
        }
        
        public function error(string $message, array $context = []): void {
            echo "[ERROR] {$message}\n";
        }
    };
    
    $repository = new UserRepository($pdo, $logger);
    $service = new UserService($repository, $logger);
    
    // Create a user
    $user = $service->createUser('John Doe', '<EMAIL>');
    
    if ($user) {
        echo "Created user: {$user}\n";
        echo "User data: " . json_encode($user) . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>

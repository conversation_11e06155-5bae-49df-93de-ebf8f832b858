FileParser 解析结果报告 - PHP
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php
  文件名: sample.php
  内容长度: 13449 字符
  行数: 541


代码块信息 (10 个):
  块 1: 第 31-81 行 (51 行)
     31:     public function getLabel(): string
     32:     {
     33:         return match($this) {
    ... (还有 48 行)

  块 2: 第 83-118 行 (36 行)
     83:     public function isValid(): bool
     84:     {
     85:         return empty($this->validate());
    ... (还有 33 行)

  块 3: 第 121-282 行 (162 行)
    121: class User extends BaseModel
    122: {
    123:     use Validatable;
    ... (还有 159 行)

  块 4: 第 121-221 行 (101 行)
    121: class User extends BaseModel
    122: {
    123:     use Validatable;
    ... (还有 100 行)

  块 5: 第 221-282 行 (62 行)
    221: class User extends BaseModel
    222: ...
    223:     // Status checks
    ... (还有 61 行)

  块 6: 第 285-430 行 (146 行)
    285: class UserRepository implements UserRepositoryInterface
    286: {
    287:     private PDO $pdo;
    ... (还有 143 行)

  块 7: 第 285-385 行 (101 行)
    285: class UserRepository implements UserRepositoryInterface
    286: {
    287:     private PDO $pdo;
    ... (还有 100 行)

  块 8: 第 385-430 行 (46 行)
    385: class UserRepository implements UserRepositoryInterface
    386: ...
    387:         
    ... (还有 45 行)

  块 9: 第 433-491 行 (59 行)
    433: class UserService
    434: {
    435:     private UserRepositoryInterface $repository;
    ... (还有 56 行)

  块 10: 第 493-523 行 (31 行)
    493: function validateEmail(string $email): bool
    494: {
    495:     return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    ... (还有 28 行)


统计信息:
  覆盖率: 147.0%
  块中总行数: 795

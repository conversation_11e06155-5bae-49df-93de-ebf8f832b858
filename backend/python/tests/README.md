# FileParser Tests

This directory contains comprehensive tests for the FileParser functionality, covering all supported programming languages and their syntax structures.

## Directory Structure

```
tests/
├── README.md                 # This file
├── conftest.py              # Pytest configuration and fixtures
├── run_tests.py             # Test runner script
├── test_file_parser.py      # Main FileParser tests
├── test_utils.py            # Test utility functions
└── resources/               # Test sample files
    ├── python/
    │   └── sample.py        # Python test samples
    ├── java/
    │   └── Sample.java      # Java test samples
    ├── javascript/
    │   └── sample.js        # JavaScript test samples
    ├── typescript/
    │   └── sample.ts        # TypeScript test samples
    ├── c/
    │   └── sample.c         # C test samples
    ├── cpp/
    │   └── sample.cpp       # C++ test samples
    ├── go/
    │   └── sample.go        # Go test samples
    ├── rust/
    │   └── sample.rs        # Rust test samples
    ├── php/
    │   └── sample.php       # PHP test samples
    ├── ruby/
    │   └── sample.rb        # Ruby test samples
    ├── swift/
    │   └── sample.swift     # Swift test samples
    ├── kotlin/
    │   └── sample.kt        # Kotlin test samples
    ├── scala/
    │   └── sample.scala     # Scala test samples
    ├── html/
    │   └── sample.html      # HTML test samples
    ├── css/
    │   └── sample.css       # CSS test samples
    ├── jsx/
    │   └── sample.jsx       # JSX test samples
    └── tsx/
        └── sample.tsx       # TSX test samples
```

## Test Categories

### 1. Basic Functionality Tests (`TestFileParser`)
- Parser initialization
- Unsupported file extensions
- Empty file content handling
- Basic parsing for all supported languages
- Chunk size constraints
- Content integrity validation
- Parser caching
- Error handling
- Concurrent access

### 2. Language-Specific Tests (`TestLanguageSpecificParsing`)
- Python: Class and function detection
- Java: Class, interface, and method detection
- C++: Class detection
- Go: Function detection
- Rust: Struct and impl detection
- TypeScript: Interface detection
- And more for each supported language

### 3. Test Utilities (`test_utils.py`)
- `ParseResultValidator`: Validates parsing results
- `LanguageTestHelper`: Language-specific test helpers
- `TestDataGenerator`: Generates test data

## Sample Files

Each language directory contains comprehensive sample files that include:

### Programming Languages
- **Class definitions** (where applicable)
- **Function/method definitions**
- **Interface definitions** (where applicable)
- **Struct definitions** (where applicable)
- **Enum definitions** (where applicable)
- **Import/include statements**
- **Comments and documentation**
- **Complex syntax structures** specific to each language

### Markup Languages
- **HTML**: Various HTML elements, attributes, nested structures
- **CSS**: Selectors, properties, media queries, animations

### Component Languages
- **JSX/TSX**: React components, hooks, props, state management

## Running Tests

### Using the Test Runner Script

```bash
# Run all tests
python tests/run_tests.py

# Run only basic tests (no language-specific)
python tests/run_tests.py --test-type basic

# Run only language-specific tests
python tests/run_tests.py --test-type language

# Run tests for a specific language
python tests/run_tests.py --language python

# Run with coverage reporting
python tests/run_tests.py --coverage

# Run with verbose output
python tests/run_tests.py --verbose

# Run fast tests only (exclude slow tests)
python tests/run_tests.py --test-type fast

# Run with parallel execution
python tests/run_tests.py --parallel 4
```

### Using Pytest Directly

```bash
# Run all tests
pytest tests/

# Run with verbose output
pytest tests/ -v

# Run specific test file
pytest tests/test_file_parser.py

# Run specific test method
pytest tests/test_file_parser.py::TestFileParser::test_python_specific_structures

# Run tests with markers
pytest tests/ -m "language_specific"
pytest tests/ -m "not slow"

# Run with coverage
pytest tests/ --cov=modules.integrations.tools.treesitter --cov-report=html
```

## Test Markers

- `@pytest.mark.language_specific`: Tests specific to particular languages
- `@pytest.mark.slow`: Tests that take longer to run
- `@pytest.mark.integration`: Integration tests

## Expected Test Results

### Successful Parsing Indicators
1. **Key Structure Lines**: Each language should detect appropriate syntax structures
   - Python: Classes, functions, methods
   - Java: Classes, interfaces, methods, constructors
   - JavaScript/TypeScript: Functions, classes, interfaces
   - C/C++: Functions, classes, structs
   - Go: Functions, methods, structs, interfaces
   - Rust: Functions, structs, implementations
   - And so on...

2. **Chunk Generation**: Files should be properly divided into chunks
   - Chunks respect size constraints
   - Content integrity is maintained
   - No overlapping chunks
   - Good coverage of original content

3. **Error Handling**: Graceful handling of edge cases
   - Invalid file extensions
   - Empty files
   - Malformed syntax (where applicable)

### Performance Expectations
- Parsing should complete within reasonable time
- Memory usage should be reasonable for file sizes
- Concurrent access should work correctly
- Parser caching should improve performance on repeated calls

## Adding New Language Tests

To add tests for a new language:

1. **Create sample file**: Add `resources/{language}/sample.{ext}` with comprehensive syntax examples
2. **Update test utilities**: Add language to `LanguageTestHelper.LANGUAGE_STRUCTURES`
3. **Add specific tests**: Create language-specific test methods in `TestLanguageSpecificParsing`
4. **Update documentation**: Add the new language to this README

## Troubleshooting

### Common Issues
1. **Missing sample files**: Ensure all sample files exist in the resources directory
2. **Import errors**: Make sure the backend/python directory is in the Python path
3. **Tree-sitter parser errors**: Verify that tree-sitter language parsers are properly installed
4. **Query file issues**: Check that query files exist for all tested languages

### Debug Tips
1. Use `pytest -v` for verbose output
2. Use `pytest -s` to see print statements
3. Use `pytest --pdb` to drop into debugger on failures
4. Check the `logs/` directory for detailed parsing logs

## Contributing

When adding new tests:
1. Follow the existing test structure and naming conventions
2. Add comprehensive sample files that cover language features
3. Include both positive and negative test cases
4. Update documentation
5. Ensure tests are deterministic and don't depend on external resources

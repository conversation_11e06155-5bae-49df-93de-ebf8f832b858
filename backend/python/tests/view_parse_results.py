#!/usr/bin/env python3
"""
查看FileParser解析结果的便捷脚本。
"""

import sys
import json
from pathlib import Path
import argparse


def view_language_result(language: str, show_chunks: bool = False, show_json: bool = False):
    """查看特定语言的解析结果。"""
    resources_dir = Path(__file__).parent / "resources"
    language_dir = resources_dir / language
    
    if not language_dir.exists():
        print(f"❌ 语言目录不存在: {language}")
        return False
    
    report_file = language_dir / "parse_report.txt"
    json_file = language_dir / "parse_result.json"
    
    if show_json and json_file.exists():
        print(f"📄 {language.upper()} - JSON结果:")
        print("=" * 60)
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            print(json.dumps(data, indent=2, ensure_ascii=False))
    elif report_file.exists():
        print(f"📄 {language.upper()} - 解析报告:")
        print("=" * 60)
        with open(report_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            if not show_chunks:
                # 只显示到代码块信息之前的部分
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith('代码块信息'):
                        content = '\n'.join(lines[:i])
                        break
            
            print(content)
    else:
        print(f"❌ 找不到 {language} 的解析结果文件")
        return False
    
    return True


def list_available_languages():
    """列出所有可用的语言。"""
    resources_dir = Path(__file__).parent / "resources"
    languages = []
    
    for item in resources_dir.iterdir():
        if item.is_dir() and (item / "parse_report.txt").exists():
            languages.append(item.name)
    
    return sorted(languages)


def view_summary():
    """查看总结报告。"""
    resources_dir = Path(__file__).parent / "resources"
    summary_file = resources_dir / "parse_results_summary.txt"
    
    if summary_file.exists():
        print("📊 FileParser解析结果总结:")
        print("=" * 60)
        with open(summary_file, 'r', encoding='utf-8') as f:
            print(f.read())
    else:
        print("❌ 找不到总结报告文件")


def compare_languages(languages: list):
    """比较多个语言的解析结果。"""
    resources_dir = Path(__file__).parent / "resources"
    
    print("🔍 语言解析结果比较:")
    print("=" * 80)
    print(f"{'语言':<12} {'关键结构':<8} {'代码块':<8} {'覆盖率':<10} {'结构类型'}")
    print("-" * 80)
    
    for language in languages:
        json_file = resources_dir / language / "parse_result.json"
        if json_file.exists():
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                key_count = data['parsing_results']['key_structure_count']
                chunk_count = data['parsing_results']['chunk_count']
                coverage = data['analysis']['coverage_percentage']
                structure_types = len(data['analysis']['detected_structures'])
                
                print(f"{language:<12} {key_count:<8} {chunk_count:<8} {coverage:<9.1f}% {structure_types}")
        else:
            print(f"{language:<12} {'N/A':<8} {'N/A':<8} {'N/A':<10} {'N/A'}")


def main():
    parser = argparse.ArgumentParser(description="查看FileParser解析结果")
    parser.add_argument("language", nargs="?", help="要查看的语言名称")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有可用的语言")
    parser.add_argument("--summary", "-s", action="store_true", help="显示总结报告")
    parser.add_argument("--chunks", "-c", action="store_true", help="显示代码块详细信息")
    parser.add_argument("--json", "-j", action="store_true", help="显示JSON格式的结果")
    parser.add_argument("--compare", nargs="+", help="比较多个语言的结果")
    
    args = parser.parse_args()
    
    if args.list:
        languages = list_available_languages()
        print("📋 可用的语言:")
        for lang in languages:
            print(f"  - {lang}")
        print(f"\n总计: {len(languages)} 种语言")
        return
    
    if args.summary:
        view_summary()
        return
    
    if args.compare:
        compare_languages(args.compare)
        return
    
    if args.language:
        success = view_language_result(args.language, args.chunks, args.json)
        if not success:
            print(f"\n💡 提示: 使用 --list 查看所有可用的语言")
    else:
        print("📋 FileParser解析结果查看器")
        print("\n使用方法:")
        print("  python view_parse_results.py <语言名>     # 查看特定语言的结果")
        print("  python view_parse_results.py --list      # 列出所有语言")
        print("  python view_parse_results.py --summary   # 查看总结报告")
        print("  python view_parse_results.py --compare python java  # 比较语言")
        print("\n选项:")
        print("  --chunks, -c    显示代码块详细信息")
        print("  --json, -j      显示JSON格式结果")
        
        # 显示可用语言
        languages = list_available_languages()
        if languages:
            print(f"\n📋 可用语言 ({len(languages)}):")
            for i, lang in enumerate(languages):
                if i % 4 == 0:
                    print()
                print(f"  {lang:<12}", end="")
            print()


if __name__ == "__main__":
    main()

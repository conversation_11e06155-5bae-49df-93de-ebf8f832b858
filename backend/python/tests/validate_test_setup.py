#!/usr/bin/env python3
"""
Validation script to check if the test setup is correct.
Verifies that all required sample files exist and are properly structured.
"""

import sys
from pathlib import Path
from typing import Dict, List, Set

# Add the backend/python directory to the Python path
backend_python_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_python_dir))

from modules.common.constants import LanguageEnum


def check_sample_files() -> Dict[str, List[str]]:
    """Check if all required sample files exist."""
    resources_dir = Path(__file__).parent / "resources"
    
    # Expected sample files for each language
    expected_files = {
        "python": ["sample.py"],
        "java": ["Sample.java"],
        "javascript": ["sample.js"],
        "typescript": ["sample.ts"],
        "c": ["sample.c"],
        "cpp": ["sample.cpp"],
        "go": ["sample.go"],
        "rust": ["sample.rs"],
        "php": ["sample.php"],
        "ruby": ["sample.rb"],
        "swift": ["sample.swift"],
        "kotlin": ["sample.kt"],
        "scala": ["sample.scala"],
        "html": ["sample.html"],
        "css": ["sample.css"],
        "jsx": ["sample.jsx"],
        "tsx": ["sample.tsx"]
    }
    
    results = {
        "existing": [],
        "missing": [],
        "empty": []
    }
    
    for language, files in expected_files.items():
        lang_dir = resources_dir / language
        
        if not lang_dir.exists():
            results["missing"].append(f"Directory: {language}/")
            continue
        
        for filename in files:
            file_path = lang_dir / filename
            
            if not file_path.exists():
                results["missing"].append(f"{language}/{filename}")
            else:
                # Check if file is not empty
                try:
                    content = file_path.read_text(encoding='utf-8')
                    if not content.strip():
                        results["empty"].append(f"{language}/{filename}")
                    else:
                        results["existing"].append(f"{language}/{filename}")
                except Exception as e:
                    results["missing"].append(f"{language}/{filename} (read error: {e})")
    
    return results


def check_test_files() -> Dict[str, bool]:
    """Check if all required test files exist."""
    test_dir = Path(__file__).parent
    
    required_files = [
        "test_file_parser.py",
        "test_utils.py",
        "conftest.py",
        "run_tests.py",
        "README.md"
    ]
    
    results = {}
    
    for filename in required_files:
        file_path = test_dir / filename
        results[filename] = file_path.exists()
    
    return results


def check_imports() -> Dict[str, bool]:
    """Check if required modules can be imported."""
    import_results = {}
    
    try:
        from core.config import ChunkConfig
        import_results["ChunkConfig"] = True
    except ImportError as e:
        import_results["ChunkConfig"] = False
        print(f"Import error for ChunkConfig: {e}")
    
    try:
        from modules.integrations.tools.treesitter.parser import FileParser
        import_results["FileParser"] = True
    except ImportError as e:
        import_results["FileParser"] = False
        print(f"Import error for FileParser: {e}")
    
    try:
        from modules.common.constants import LanguageEnum
        import_results["LanguageEnum"] = True
    except ImportError as e:
        import_results["LanguageEnum"] = False
        print(f"Import error for LanguageEnum: {e}")
    
    try:
        from modules.common.schema import Chunk
        import_results["Chunk"] = True
    except ImportError as e:
        import_results["Chunk"] = False
        print(f"Import error for Chunk: {e}")
    
    return import_results


def validate_sample_content() -> Dict[str, List[str]]:
    """Validate that sample files contain expected content patterns."""
    resources_dir = Path(__file__).parent / "resources"
    
    # Expected patterns for different languages
    content_patterns = {
        "python": ["class ", "def ", "import "],
        "java": ["class ", "public ", "interface "],
        "javascript": ["function ", "class ", "const "],
        "typescript": ["interface ", "type ", "class "],
        "c": ["#include", "int ", "struct "],
        "cpp": ["class ", "#include", "namespace "],
        "go": ["func ", "package ", "type "],
        "rust": ["fn ", "struct ", "impl "],
        "php": ["<?php", "class ", "function "],
        "ruby": ["class ", "def ", "module "],
        "swift": ["class ", "func ", "struct "],
        "kotlin": ["class ", "fun ", "interface "],
        "scala": ["class ", "def ", "object "],
        "html": ["<html", "<head", "<body"],
        "css": ["{", "}", ":"],
        "jsx": ["import React", "class ", "function "],
        "tsx": ["interface ", "type ", "React"]
    }
    
    results = {
        "valid": [],
        "invalid": [],
        "warnings": []
    }
    
    for language, patterns in content_patterns.items():
        lang_dir = resources_dir / language
        
        if not lang_dir.exists():
            continue
        
        sample_files = list(lang_dir.glob("sample.*"))
        
        for sample_file in sample_files:
            try:
                content = sample_file.read_text(encoding='utf-8')
                
                found_patterns = []
                for pattern in patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= len(patterns) * 0.5:  # At least 50% of patterns
                    results["valid"].append(str(sample_file.relative_to(resources_dir)))
                else:
                    results["warnings"].append(
                        f"{sample_file.relative_to(resources_dir)}: "
                        f"Only {len(found_patterns)}/{len(patterns)} patterns found"
                    )
                
            except Exception as e:
                results["invalid"].append(f"{sample_file.relative_to(resources_dir)}: {e}")
    
    return results


def main():
    """Run all validation checks."""
    print("🔍 Validating FileParser test setup...")
    print("=" * 60)
    
    # Check sample files
    print("\n📁 Checking sample files...")
    sample_results = check_sample_files()
    
    print(f"✅ Existing files: {len(sample_results['existing'])}")
    for file in sample_results['existing'][:5]:  # Show first 5
        print(f"   - {file}")
    if len(sample_results['existing']) > 5:
        print(f"   ... and {len(sample_results['existing']) - 5} more")
    
    if sample_results['missing']:
        print(f"\n❌ Missing files: {len(sample_results['missing'])}")
        for file in sample_results['missing']:
            print(f"   - {file}")
    
    if sample_results['empty']:
        print(f"\n⚠️  Empty files: {len(sample_results['empty'])}")
        for file in sample_results['empty']:
            print(f"   - {file}")
    
    # Check test files
    print("\n🧪 Checking test files...")
    test_results = check_test_files()
    
    for filename, exists in test_results.items():
        status = "✅" if exists else "❌"
        print(f"   {status} {filename}")
    
    # Check imports
    print("\n📦 Checking imports...")
    import_results = check_imports()
    
    for module, success in import_results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {module}")
    
    # Validate sample content
    print("\n📝 Validating sample content...")
    content_results = validate_sample_content()
    
    print(f"✅ Valid content: {len(content_results['valid'])}")
    
    if content_results['warnings']:
        print(f"\n⚠️  Content warnings: {len(content_results['warnings'])}")
        for warning in content_results['warnings'][:3]:  # Show first 3
            print(f"   - {warning}")
        if len(content_results['warnings']) > 3:
            print(f"   ... and {len(content_results['warnings']) - 3} more")
    
    if content_results['invalid']:
        print(f"\n❌ Invalid content: {len(content_results['invalid'])}")
        for invalid in content_results['invalid']:
            print(f"   - {invalid}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    total_files = len(sample_results['existing']) + len(sample_results['missing'])
    missing_count = len(sample_results['missing'])
    test_files_ok = all(test_results.values())
    imports_ok = all(import_results.values())
    
    print(f"Sample files: {len(sample_results['existing'])}/{total_files} present")
    print(f"Test files: {'✅ All present' if test_files_ok else '❌ Some missing'}")
    print(f"Imports: {'✅ All working' if imports_ok else '❌ Some failing'}")
    
    if missing_count == 0 and test_files_ok and imports_ok:
        print("\n🎉 Test setup is complete and ready!")
        return 0
    else:
        print("\n⚠️  Test setup needs attention. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())

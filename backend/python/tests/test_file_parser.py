"""
Comprehensive tests for FileParser functionality.
Tests parsing capabilities for all supported languages with various syntax structures.
"""

import pytest
import os
from pathlib import Path
from typing import Dict, List, Tuple

from core.config import ChunkConfig
from modules.integrations.tools.treesitter.parser import FileParser
from modules.common.constants import LanguageEnum
from modules.common.schema import Chunk


class TestFileParser:
    """Test class for FileParser functionality."""
    
    @pytest.fixture
    def chunk_config(self):
        """Create a test chunk configuration."""
        return ChunkConfig(
            name="test",
            min_chunk_size=10,
            max_chunk_size=50,
            overflow_size=5
        )
    
    @pytest.fixture
    def file_parser(self, chunk_config):
        """Create a FileParser instance for testing."""
        return FileParser(chunk_config)
    
    @pytest.fixture
    def test_resources_dir(self):
        """Get the test resources directory path."""
        return Path(__file__).parent / "resources"
    
    def load_sample_file(self, language: str, filename: str = None) -> Tuple[str, str]:
        """Load a sample file for the given language."""
        resources_dir = Path(__file__).parent / "resources"
        language_dir = resources_dir / language
        
        if filename is None:
            # Use default sample file names
            sample_files = {
                "python": "sample.py",
                "java": "Sample.java",
                "javascript": "sample.js",
                "typescript": "sample.ts",
                "c": "sample.c",
                "cpp": "sample.cpp",
                "go": "sample.go",
                "rust": "sample.rs",
                "php": "sample.php",
                "ruby": "sample.rb",
                "swift": "sample.swift",
                "kotlin": "sample.kt",
                "scala": "sample.scala",
                "html": "sample.html",
                "css": "sample.css",
                "jsx": "sample.jsx",
                "tsx": "sample.tsx"
            }
            filename = sample_files.get(language, "sample.txt")
        
        file_path = language_dir / filename
        if not file_path.exists():
            pytest.skip(f"Sample file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return str(file_path), content
    
    def test_parser_initialization(self, chunk_config):
        """Test FileParser initialization."""
        parser = FileParser(chunk_config)
        assert parser.config == chunk_config
        assert isinstance(parser.language_parsers, dict)
        assert isinstance(parser.language_queries, dict)
    
    def test_unsupported_file_extension(self, file_parser):
        """Test parsing with unsupported file extension."""
        key_lines, chunks = file_parser.parse("test.unknown", "some content")
        assert key_lines == {}
        assert chunks == []
    
    def test_empty_file_content(self, file_parser):
        """Test parsing empty file content."""
        key_lines, chunks = file_parser.parse("test.py", "")
        assert isinstance(key_lines, dict)
        assert isinstance(chunks, list)
    
    @pytest.mark.parametrize("language", [
        "python", "java", "javascript", "typescript", "c", "cpp", 
        "go", "rust", "php", "ruby", "swift", "kotlin", "scala"
    ])
    def test_programming_language_parsing(self, file_parser, language):
        """Test parsing for programming languages."""
        file_path, content = self.load_sample_file(language)
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        # Basic assertions
        assert isinstance(key_lines, dict)
        assert isinstance(chunks, list)
        
        # Should have some key structure lines for programming languages
        if content.strip():  # Only if file has content
            assert len(key_lines) > 0, f"No key structure lines found for {language}"
        
        # Should have some chunks
        if len(content.splitlines()) >= file_parser.config.min_chunk_size:
            assert len(chunks) > 0, f"No chunks found for {language}"
        
        # Validate chunk structure
        for chunk in chunks:
            assert isinstance(chunk, Chunk)
            assert chunk.file_path == file_path
            assert chunk.start_line >= 0
            assert chunk.end_line >= chunk.start_line
            assert isinstance(chunk.content, str)
            assert len(chunk.content) > 0
    
    @pytest.mark.parametrize("language", ["html", "css"])
    def test_markup_language_parsing(self, file_parser, language):
        """Test parsing for markup languages."""
        file_path, content = self.load_sample_file(language)
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        # Basic assertions
        assert isinstance(key_lines, dict)
        assert isinstance(chunks, list)
        
        # Should have some chunks for non-empty files
        if len(content.splitlines()) >= file_parser.config.min_chunk_size:
            assert len(chunks) > 0, f"No chunks found for {language}"
    
    @pytest.mark.parametrize("language", ["tsx"])  # JSX parser not available in tree-sitter-language-pack
    def test_jsx_tsx_parsing(self, file_parser, language):
        """Test parsing for JSX/TSX files."""
        file_path, content = self.load_sample_file(language)

        key_lines, chunks = file_parser.parse(file_path, content)

        # Basic assertions
        assert isinstance(key_lines, dict)
        assert isinstance(chunks, list)

        # Should have key structure lines for component definitions
        if content.strip():
            assert len(key_lines) > 0, f"No key structure lines found for {language}"

        # Should have chunks
        if len(content.splitlines()) >= file_parser.config.min_chunk_size:
            assert len(chunks) > 0, f"No chunks found for {language}"
    
    def test_python_specific_structures(self, file_parser):
        """Test Python-specific syntax structure detection."""
        file_path, content = self.load_sample_file("python")
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        # Check for Python-specific structures in key_lines
        key_line_types = set(key_lines.values())
        
        # Should detect class and function definitions
        assert any("definition.class" in line_type for line_type in key_line_types), \
            "No class definitions detected"
        assert any("definition.function" in line_type for line_type in key_line_types), \
            "No function definitions detected"
    
    def test_java_specific_structures(self, file_parser):
        """Test Java-specific syntax structure detection."""
        file_path, content = self.load_sample_file("java")
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        # Check for Java-specific structures
        key_line_types = set(key_lines.values())
        
        # Should detect class, method, and interface definitions
        expected_structures = ["definition.class", "definition.method", "definition.interface"]
        for structure in expected_structures:
            assert any(structure in line_type for line_type in key_line_types), \
                f"No {structure} detected in Java file"
    
    def test_javascript_specific_structures(self, file_parser):
        """Test JavaScript-specific syntax structure detection."""
        file_path, content = self.load_sample_file("javascript")
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        # Should have some key structures for JavaScript
        assert len(key_lines) > 0, "No key structure lines found for JavaScript"
    
    def test_chunk_size_constraints(self, file_parser):
        """Test that chunks respect size constraints."""
        file_path, content = self.load_sample_file("python")
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        for chunk in chunks:
            chunk_line_count = chunk.end_line - chunk.start_line + 1
            
            # Chunks should not exceed max_chunk_size unless it's a single large structure
            if chunk_line_count > file_parser.config.max_chunk_size:
                # This might be acceptable for very large single structures
                pass
            
            # Chunks should meet minimum size when possible
            if chunk_line_count < file_parser.config.min_chunk_size:
                # This might be the last chunk or a small structure
                pass
    
    def test_chunk_content_integrity(self, file_parser):
        """Test that chunk content matches the original file content."""
        file_path, content = self.load_sample_file("python")
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        original_lines = content.splitlines()
        
        for chunk in chunks:
            # Extract the expected content from original file
            expected_content = "\n".join(
                original_lines[chunk.start_line:chunk.end_line + 1]
            )
            
            assert chunk.content == expected_content, \
                f"Chunk content mismatch at lines {chunk.start_line}-{chunk.end_line}"
    
    def test_key_structure_lines_validity(self, file_parser):
        """Test that key structure lines are valid."""
        file_path, content = self.load_sample_file("python")
        
        key_lines, chunks = file_parser.parse(file_path, content)
        
        total_lines = len(content.splitlines())
        
        for line_number, structure_type in key_lines.items():
            # Line numbers should be within file bounds
            assert 0 <= line_number < total_lines, \
                f"Key structure line {line_number} is out of bounds"
            
            # Structure type should be a valid definition type
            assert "definition" in structure_type, \
                f"Invalid structure type: {structure_type}"
    
    def test_parser_caching(self, file_parser):
        """Test that parsers and queries are cached properly."""
        file_path, content = self.load_sample_file("python")
        
        # First parse
        key_lines1, chunks1 = file_parser.parse(file_path, content)
        
        # Check that parser and query are cached
        python_enum = LanguageEnum.PYTHON
        assert python_enum in file_parser.language_parsers
        assert python_enum in file_parser.language_queries
        
        # Second parse should use cached parser and query
        key_lines2, chunks2 = file_parser.parse(file_path, content)
        
        # Results should be identical
        assert key_lines1 == key_lines2
        assert len(chunks1) == len(chunks2)
        
        for chunk1, chunk2 in zip(chunks1, chunks2):
            assert chunk1.file_path == chunk2.file_path
            assert chunk1.start_line == chunk2.start_line
            assert chunk1.end_line == chunk2.end_line
            assert chunk1.content == chunk2.content
    
    def test_error_handling_invalid_query(self, file_parser):
        """Test error handling when query file is invalid or missing."""
        # This test would require mocking or creating an invalid query file
        # For now, we'll test with a language that might not have a query file
        
        # Try parsing with a language that exists but might not have a query
        key_lines, chunks = file_parser.parse("test.unknown", "some content")
        
        # Should handle gracefully
        assert key_lines == {}
        assert chunks == []
    
    def test_concurrent_parsing(self, file_parser):
        """Test that parser works correctly with concurrent access."""
        import threading
        import time
        
        file_path, content = self.load_sample_file("python")
        results = []
        
        def parse_file():
            key_lines, chunks = file_parser.parse(file_path, content)
            results.append((key_lines, chunks))
        
        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=parse_file)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All results should be identical
        assert len(results) == 5
        first_result = results[0]

        for result in results[1:]:
            assert result[0] == first_result[0]  # key_lines
            assert len(result[1]) == len(first_result[1])  # chunks count


class TestLanguageSpecificParsing:
    """Test class for language-specific parsing functionality."""

    @pytest.fixture
    def chunk_config(self):
        """Create a test chunk configuration."""
        return ChunkConfig(
            name="test",
            min_chunk_size=5,
            max_chunk_size=30,
            overflow_size=3
        )

    @pytest.fixture
    def file_parser(self, chunk_config):
        """Create a FileParser instance for testing."""
        return FileParser(chunk_config)

    def load_sample_file(self, language: str) -> Tuple[str, str]:
        """Load a sample file for the given language."""
        resources_dir = Path(__file__).parent / "resources"
        language_dir = resources_dir / language

        sample_files = {
            "python": "sample.py",
            "java": "Sample.java",
            "javascript": "sample.js",
            "typescript": "sample.ts",
            "c": "sample.c",
            "cpp": "sample.cpp",
            "go": "sample.go",
            "rust": "sample.rs",
            "php": "sample.php",
            "ruby": "sample.rb",
            "swift": "sample.swift",
            "kotlin": "sample.kt",
            "scala": "sample.scala",
            "html": "sample.html",
            "css": "sample.css",
            "jsx": "sample.jsx",
            "tsx": "sample.tsx"
        }

        filename = sample_files.get(language, "sample.txt")
        file_path = language_dir / filename

        if not file_path.exists():
            pytest.skip(f"Sample file not found: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        return str(file_path), content

    def test_python_class_detection(self, file_parser):
        """Test detection of Python class definitions."""
        file_path, content = self.load_sample_file("python")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect class definitions
        class_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.class" in line_type
        ]
        assert len(class_definitions) > 0, "No Python class definitions detected"

    def test_python_function_detection(self, file_parser):
        """Test detection of Python function definitions."""
        file_path, content = self.load_sample_file("python")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect function definitions
        function_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.function" in line_type
        ]
        assert len(function_definitions) > 0, "No Python function definitions detected"

    def test_java_class_and_interface_detection(self, file_parser):
        """Test detection of Java class and interface definitions."""
        file_path, content = self.load_sample_file("java")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect class definitions
        class_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.class" in line_type
        ]
        assert len(class_definitions) > 0, "No Java class definitions detected"

        # Should detect interface definitions
        interface_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.interface" in line_type
        ]
        assert len(interface_definitions) > 0, "No Java interface definitions detected"

    def test_java_method_detection(self, file_parser):
        """Test detection of Java method definitions."""
        file_path, content = self.load_sample_file("java")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect method definitions
        method_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.method" in line_type
        ]
        assert len(method_definitions) > 0, "No Java method definitions detected"

    def test_cpp_class_detection(self, file_parser):
        """Test detection of C++ class definitions."""
        file_path, content = self.load_sample_file("cpp")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect class definitions
        class_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.class" in line_type
        ]
        assert len(class_definitions) > 0, "No C++ class definitions detected"

    def test_go_function_detection(self, file_parser):
        """Test detection of Go function definitions."""
        file_path, content = self.load_sample_file("go")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect function definitions
        function_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.function" in line_type
        ]
        assert len(function_definitions) > 0, "No Go function definitions detected"

    def test_rust_struct_and_impl_detection(self, file_parser):
        """Test detection of Rust struct and impl definitions."""
        file_path, content = self.load_sample_file("rust")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect struct definitions
        struct_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.struct" in line_type
        ]
        # Note: Rust structs might be detected as different definition types
        # depending on the query configuration

        # Should have some key definitions
        assert len(key_lines) > 0, "No Rust definitions detected"

    def test_typescript_interface_detection(self, file_parser):
        """Test detection of TypeScript interface definitions."""
        file_path, content = self.load_sample_file("typescript")
        key_lines, chunks = file_parser.parse(file_path, content)

        # Should detect interface definitions
        interface_definitions = [
            line_type for line_type in key_lines.values()
            if "definition.interface" in line_type
        ]
        # TypeScript interfaces might be detected differently
        # Just ensure we have some definitions
        assert len(key_lines) > 0, "No TypeScript definitions detected"

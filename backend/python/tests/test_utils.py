"""
Test utility functions for FileParser testing.
Provides helper functions to validate parsing results and test data integrity.
"""

from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
import re

from modules.common.schema import Chunk
from modules.common.constants import LanguageEnum


class ParseResultValidator:
    """Utility class for validating FileParser results."""
    
    @staticmethod
    def validate_chunks(chunks: List[Chunk], original_content: str, file_path: str) -> List[str]:
        """
        Validate that chunks are properly formed and contain correct content.
        
        Args:
            chunks: List of chunks to validate
            original_content: Original file content
            file_path: Path to the file being parsed
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        original_lines = original_content.splitlines()
        total_lines = len(original_lines)
        
        for i, chunk in enumerate(chunks):
            # Validate chunk structure
            if not isinstance(chunk, Chunk):
                errors.append(f"Chunk {i} is not a Chunk instance")
                continue
            
            # Validate file path
            if chunk.file_path != file_path:
                errors.append(f"Chunk {i} has incorrect file_path: {chunk.file_path}")
            
            # Validate line numbers
            if chunk.start_line < 0:
                errors.append(f"Chunk {i} has negative start_line: {chunk.start_line}")
            
            if chunk.end_line < chunk.start_line:
                errors.append(f"Chunk {i} has end_line < start_line: {chunk.end_line} < {chunk.start_line}")
            
            if chunk.end_line >= total_lines:
                errors.append(f"Chunk {i} end_line {chunk.end_line} exceeds file length {total_lines}")
            
            # Validate content
            if not chunk.content:
                errors.append(f"Chunk {i} has empty content")
                continue
            
            # Validate content matches original file
            try:
                expected_content = "\n".join(original_lines[chunk.start_line:chunk.end_line + 1])
                if chunk.content != expected_content:
                    errors.append(f"Chunk {i} content mismatch at lines {chunk.start_line}-{chunk.end_line}")
            except IndexError:
                errors.append(f"Chunk {i} line range {chunk.start_line}-{chunk.end_line} is invalid")
        
        return errors
    
    @staticmethod
    def validate_key_structure_lines(
        key_lines: Dict[int, str], 
        original_content: str,
        expected_structures: Optional[Set[str]] = None
    ) -> List[str]:
        """
        Validate key structure lines.
        
        Args:
            key_lines: Dictionary mapping line numbers to structure types
            original_content: Original file content
            expected_structures: Set of expected structure types (optional)
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        total_lines = len(original_content.splitlines())
        
        for line_number, structure_type in key_lines.items():
            # Validate line number
            if not isinstance(line_number, int):
                errors.append(f"Key line number is not an integer: {line_number}")
                continue
            
            if line_number < 0 or line_number >= total_lines:
                errors.append(f"Key line number {line_number} is out of bounds (0-{total_lines-1})")
            
            # Validate structure type
            if not isinstance(structure_type, str):
                errors.append(f"Structure type at line {line_number} is not a string: {structure_type}")
                continue
            
            if "definition" not in structure_type:
                errors.append(f"Structure type at line {line_number} is not a definition: {structure_type}")
            
            # Check expected structures if provided
            if expected_structures:
                if not any(expected in structure_type for expected in expected_structures):
                    errors.append(f"Unexpected structure type at line {line_number}: {structure_type}")
        
        return errors
    
    @staticmethod
    def validate_chunk_coverage(chunks: List[Chunk], original_content: str) -> Tuple[float, List[str]]:
        """
        Calculate and validate chunk coverage of the original content.
        
        Args:
            chunks: List of chunks
            original_content: Original file content
            
        Returns:
            Tuple of (coverage_percentage, validation_errors)
        """
        errors = []
        total_lines = len(original_content.splitlines())
        
        if total_lines == 0:
            return 100.0, []
        
        covered_lines = set()
        
        for chunk in chunks:
            if chunk.start_line < 0 or chunk.end_line >= total_lines:
                errors.append(f"Chunk line range {chunk.start_line}-{chunk.end_line} is invalid")
                continue
            
            for line_num in range(chunk.start_line, chunk.end_line + 1):
                covered_lines.add(line_num)
        
        coverage = (len(covered_lines) / total_lines) * 100
        
        # Check for gaps in coverage
        all_lines = set(range(total_lines))
        uncovered_lines = all_lines - covered_lines
        
        if uncovered_lines:
            # Small gaps might be acceptable
            if len(uncovered_lines) > total_lines * 0.1:  # More than 10% uncovered
                errors.append(f"Large coverage gap: {len(uncovered_lines)} uncovered lines")
        
        return coverage, errors
    
    @staticmethod
    def validate_chunk_overlap(chunks: List[Chunk]) -> List[str]:
        """
        Check for overlapping chunks.
        
        Args:
            chunks: List of chunks to check
            
        Returns:
            List of validation error messages (empty if no overlaps)
        """
        errors = []
        
        for i, chunk1 in enumerate(chunks):
            for j, chunk2 in enumerate(chunks[i+1:], i+1):
                # Check if chunks overlap
                if (chunk1.start_line <= chunk2.end_line and 
                    chunk2.start_line <= chunk1.end_line):
                    errors.append(
                        f"Chunks {i} and {j} overlap: "
                        f"[{chunk1.start_line}-{chunk1.end_line}] and "
                        f"[{chunk2.start_line}-{chunk2.end_line}]"
                    )
        
        return errors


class LanguageTestHelper:
    """Helper class for language-specific testing."""
    
    # Expected structure types for different languages
    LANGUAGE_STRUCTURES = {
        LanguageEnum.PYTHON: {
            "definition.class", "definition.function", "definition.method"
        },
        LanguageEnum.JAVA: {
            "definition.class", "definition.interface", "definition.method", 
            "definition.constructor", "definition.enum"
        },
        LanguageEnum.JAVASCRIPT: {
            "definition.function", "definition.class", "definition.method"
        },
        LanguageEnum.TYPESCRIPT: {
            "definition.function", "definition.class", "definition.interface", 
            "definition.method", "definition.type"
        },
        LanguageEnum.CPP: {
            "definition.class", "definition.function", "definition.method"
        },
        LanguageEnum.C: {
            "definition.function", "definition.struct"
        },
        LanguageEnum.GO: {
            "definition.function", "definition.method", "definition.struct", 
            "definition.interface"
        },
        LanguageEnum.RUST: {
            "definition.function", "definition.struct", "definition.impl", 
            "definition.trait"
        },
        LanguageEnum.PHP: {
            "definition.class", "definition.function", "definition.method", 
            "definition.interface"
        },
        LanguageEnum.RUBY: {
            "definition.class", "definition.method", "definition.module"
        },
        LanguageEnum.SWIFT: {
            "definition.class", "definition.function", "definition.method", 
            "definition.struct", "definition.protocol"
        },
        LanguageEnum.KOTLIN: {
            "definition.class", "definition.function", "definition.method", 
            "definition.interface"
        },
        LanguageEnum.SCALA: {
            "definition.class", "definition.function", "definition.method", 
            "definition.object", "definition.trait"
        }
    }
    
    @classmethod
    def get_expected_structures(cls, language: LanguageEnum) -> Set[str]:
        """Get expected structure types for a language."""
        return cls.LANGUAGE_STRUCTURES.get(language, set())
    
    @classmethod
    def validate_language_structures(
        cls, 
        language: LanguageEnum, 
        key_lines: Dict[int, str]
    ) -> List[str]:
        """
        Validate that expected language structures are detected.
        
        Args:
            language: Programming language
            key_lines: Detected key structure lines
            
        Returns:
            List of validation error messages
        """
        errors = []
        expected_structures = cls.get_expected_structures(language)
        
        if not expected_structures:
            # No specific expectations for this language
            return errors
        
        detected_structures = set()
        for structure_type in key_lines.values():
            for expected in expected_structures:
                if expected in structure_type:
                    detected_structures.add(expected)
        
        missing_structures = expected_structures - detected_structures
        
        # For some languages, not all structures might be present in sample files
        # So we'll only warn about completely missing core structures
        core_structures = {"definition.function", "definition.class", "definition.method"}
        missing_core = missing_structures & core_structures
        
        if missing_core and language in [
            LanguageEnum.PYTHON, LanguageEnum.JAVA, LanguageEnum.JAVASCRIPT,
            LanguageEnum.TYPESCRIPT, LanguageEnum.CPP
        ]:
            errors.append(f"Missing core structures for {language.name}: {missing_core}")
        
        return errors


class TestDataGenerator:
    """Utility for generating test data and scenarios."""
    
    @staticmethod
    def create_minimal_test_content(language: LanguageEnum) -> str:
        """Create minimal test content for a language."""
        templates = {
            LanguageEnum.PYTHON: '''
class TestClass:
    def test_method(self):
        pass

def test_function():
    return True
''',
            LanguageEnum.JAVA: '''
public class TestClass {
    public void testMethod() {
        // test
    }
}
''',
            LanguageEnum.JAVASCRIPT: '''
class TestClass {
    testMethod() {
        return true;
    }
}

function testFunction() {
    return true;
}
''',
            LanguageEnum.C: '''
#include <stdio.h>

struct TestStruct {
    int value;
};

int test_function() {
    return 0;
}
''',
            LanguageEnum.CPP: '''
class TestClass {
public:
    void testMethod() {
        // test
    }
};

int testFunction() {
    return 0;
}
'''
        }
        
        return templates.get(language, "// Test content")
    
    @staticmethod
    def create_large_test_content(language: LanguageEnum, line_count: int = 100) -> str:
        """Create large test content for testing chunk boundaries."""
        base_content = TestDataGenerator.create_minimal_test_content(language)
        
        # Repeat and modify the base content to reach desired line count
        lines = base_content.strip().split('\n')
        result_lines = []
        
        while len(result_lines) < line_count:
            for line in lines:
                if len(result_lines) >= line_count:
                    break
                result_lines.append(line)
        
        return '\n'.join(result_lines)

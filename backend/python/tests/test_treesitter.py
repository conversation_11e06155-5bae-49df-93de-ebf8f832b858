#!/usr/bin/env python3
"""
Test tree-sitter usage to understand the correct API.
"""

import sys
from pathlib import Path

# Add the backend/python directory to the Python path
backend_python_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_python_dir))

from tree_sitter import Query, QueryCursor
from tree_sitter_language_pack import get_parser


def test_treesitter_api():
    """Test the correct tree-sitter API usage."""
    print("🌳 Testing tree-sitter API...")
    
    # Load Python parser
    parser = get_parser("python")
    print(f"Parser: {parser}")
    
    # Simple Python code
    code = '''
class TestClass:
    def __init__(self):
        self.value = 42
    
    def test_method(self):
        return self.value

def test_function():
    return "hello"
'''
    
    # Parse the code
    tree = parser.parse(code.encode("utf-8"))
    print(f"Tree: {tree}")
    print(f"Root node: {tree.root_node}")
    
    # Load query
    query_file = Path(__file__).parent.parent / "modules" / "integrations" / "tools" / "treesitter" / "queries" / "python.scm"
    query_content = query_file.read_text(encoding="utf-8")
    print(f"Query content length: {len(query_content)}")
    
    # Create query
    query = Query(parser.language, query_content)
    print(f"Query: {query}")
    
    # Execute query
    query_cursor = QueryCursor(query)
    captures = query_cursor.captures(tree.root_node)
    
    print(f"\nCaptures type: {type(captures)}")
    
    # Iterate through captures
    print("\nCaptures:")
    for i, capture in enumerate(captures):
        print(f"  Capture {i}: {capture}")
        print(f"    Type: {type(capture)}")
        print(f"    Length: {len(capture) if hasattr(capture, '__len__') else 'N/A'}")
        
        if hasattr(capture, '__iter__'):
            for j, item in enumerate(capture):
                print(f"      Item {j}: {item} (type: {type(item)})")
                if hasattr(item, 'start_point'):
                    print(f"        Start: {item.start_point}")
                if hasattr(item, 'end_point'):
                    print(f"        End: {item.end_point}")
                if hasattr(item, 'type'):
                    print(f"        Node type: {item.type}")
        
        if i >= 5:  # Limit output
            print("    ... (truncated)")
            break


if __name__ == "__main__":
    test_treesitter_api()

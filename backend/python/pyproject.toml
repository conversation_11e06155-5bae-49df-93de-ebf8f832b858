[project]
name = "codebase-dev"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.12.15",
    "click>=8.2.1",
    "deprecated>=1.2.18",
    "fastapi==0.104.1",
    "jieba>=0.42.1",
    "openai>=1.0.0",
    "pydantic==2.5.0",
    "python-dotenv==1.0.0",
    "python-multipart==0.0.6",
    "pyyaml>=6.0.2",
    "rich>=14.1.0",
    "ripgrep>=14.1.0",
    "tree-sitter>=0.25.1",
    "tree-sitter-language-pack>=0.9.0",
    "uvicorn[standard]==0.24.0",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
]

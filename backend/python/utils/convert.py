"""
XML和数据转换相关的工具函数

这个模块提供了一套完整的XML处理工具，专门解决XML查询中包含特殊字符（如<、>、&）的问题。

主要功能：
1. 智能XML预处理：自动检测并处理特殊字符
2. 安全XML解析：结合预处理的XML解析
3. 文本提取：正确处理CDATA和HTML实体
4. 多标签提取：支持包含多个标签的复杂XML结构

使用场景：
- term_sparse_search: 处理包含代码片段的搜索查询
- deep_search: 处理LLM回复中的多个搜索工具标签
- 任何需要在XML中包含特殊字符的场景

示例：
    # 单标签处理
    xml = '<term_sparse><query>if x < y: return True</query></term_sparse>'
    processed = preprocess_xml_query(xml)
    # 结果: '<term_sparse><query><![CDATA[if x < y: return True]]></query></term_sparse>'

    # 多标签处理
    xml = '<output><term_sparse><query>x < y</query></term_sparse></output>'
    results = safe_extract_xml_tags_with_preprocessing(xml, ['term_sparse'])
    # 结果: [('term_sparse', '<term_sparse><query><![CDATA[x < y]]></query></term_sparse>')]
"""

import re
import xml.etree.ElementTree as ET
import html
from typing import Optional, List, Tuple


def preprocess_xml_query(xml_query: str, query_tag: str = 'query') -> str:
    """
    预处理XML查询，智能处理特殊字符

    这是一个通用的XML预处理函数，可以处理任何包含指定标签的XML内容中的特殊字符问题。

    策略：
    1. 如果指定标签内容已经是CDATA格式，保持不变
    2. 如果指定标签内容已经是HTML实体转义，保持不变
    3. 如果指定标签内容包含未转义的<>字符，自动用CDATA包装

    Args:
        xml_query: 原始XML查询字符串
        query_tag: 需要处理的标签名，默认为'query'

    Returns:
        str: 处理后的XML查询字符串

    Examples:
        >>> xml = '<term_sparse><query>if x < y: return True</query></term_sparse>'
        >>> result = preprocess_xml_query(xml)
        >>> print(result)
        <term_sparse><query><![CDATA[if x < y: return True]]></query></term_sparse>

        >>> xml = '<search><content>already &lt; escaped</content></search>'
        >>> result = preprocess_xml_query(xml, 'content')
        >>> print(result)
        <search><content>already &lt; escaped</content></search>
    """
    # 查找指定标签的内容
    pattern = f'<{query_tag}>(.*?)</{query_tag}>'
    match = re.search(pattern, xml_query, re.DOTALL)

    if not match:
        return xml_query

    tag_content = match.group(1)

    # 如果已经是CDATA格式，不处理
    if tag_content.strip().startswith('<![CDATA[') and tag_content.strip().endswith(']]>'):
        return xml_query

    # 如果包含HTML实体转义，不处理
    if '&lt;' in tag_content or '&gt;' in tag_content or '&amp;' in tag_content:
        return xml_query

    # 如果包含未转义的<>字符，用CDATA包装
    if '<' in tag_content or '>' in tag_content:
        cdata_content = f'<![CDATA[{tag_content}]]>'
        return xml_query.replace(f'<{query_tag}>{tag_content}</{query_tag}>',
                               f'<{query_tag}>{cdata_content}</{query_tag}>')

    return xml_query


def safe_parse_xml_with_preprocessing(xml_string: str, query_tag: str = 'query') -> Optional[ET.Element]:
    """
    安全解析XML字符串，自动预处理特殊字符

    Args:
        xml_string: 要解析的XML字符串
        query_tag: 需要预处理的标签名，默认为'query'

    Returns:
        Optional[ET.Element]: 解析成功返回根元素，失败返回None

    Examples:
        >>> xml = '<term_sparse><query>if x < y: return True</query></term_sparse>'
        >>> root = safe_parse_xml_with_preprocessing(xml)
        >>> if root is not None:
        ...     query_elem = root.find('query')
        ...     print(''.join(query_elem.itertext()))
        if x < y: return True
    """
    try:
        # 预处理XML
        processed_xml = preprocess_xml_query(xml_string, query_tag)

        # 解析XML
        return ET.fromstring(processed_xml)

    except ET.ParseError:
        return None


def extract_text_from_xml_element(element: ET.Element) -> str:
    """
    从XML元素中提取文本内容，正确处理CDATA和HTML实体

    Args:
        element: XML元素

    Returns:
        str: 提取的文本内容

    Examples:
        >>> xml = '<query><![CDATA[if x < y: return True]]></query>'
        >>> root = ET.fromstring(xml)
        >>> text = extract_text_from_xml_element(root)
        >>> print(text)
        if x < y: return True
    """
    if element.text:
        # 处理普通文本和HTML实体转义
        return html.unescape(element.text.strip())
    else:
        # 处理可能的CDATA或混合内容
        content = ''.join(element.itertext()).strip()
        return content if content else ""


def safe_extract_xml_tags_with_preprocessing(xml_string: str, tag_names: List[str]) -> List[Tuple[str, str]]:
    """
    安全地从XML字符串中提取多个指定标签的内容，支持预处理

    Args:
        xml_string: 要解析的XML字符串
        tag_names: 要提取的标签名列表

    Returns:
        List[Tuple[str, str]]: 包含(标签名, 标签完整XML内容)的元组列表

    Examples:
        >>> xml = '<output><sub_query><text>query1</text></sub_query><sub_query><text>query2</text></sub_query></output>'
        >>> results = safe_extract_xml_tags_with_preprocessing(xml, ['sub_query'])
        >>> print(results)
        [('sub_query', '<sub_query><text>query1</text></sub_query>'), ('sub_query', '<sub_query><text>query2</text></sub_query>')]
    """
    results = []

    try:
        # 首先尝试解析整个XML
        root = ET.fromstring(xml_string)

        # 对每个标签名进行搜索
        for tag_name in tag_names:
            # 查找所有匹配的标签
            elements = root.findall(f'.//{tag_name}')

            for element in elements:
                # 将元素转换回XML字符串
                element_xml = ET.tostring(element, encoding='unicode')
                results.append((tag_name, element_xml))

    except ET.ParseError:
        # 如果XML解析失败，使用正则表达式作为备选方案
        for tag_name in tag_names:
            pattern = f'<{tag_name}>(.*?)</{tag_name}>'
            matches = re.findall(pattern, xml_string, re.DOTALL)

            for match in matches:
                full_tag = f'<{tag_name}>{match}</{tag_name}>'
                results.append((tag_name, full_tag))

    return results
"""
SQLite数据库集成模块

提供了基于SQLite的数据库客户端，用于存储和检索代码仓库相关数据。

主要组件:
- SQLiteClient: 主要的数据库客户端类
- SQLiteConfig: 独立的SQLite数据库配置类
- ExtendedConfig: 扩展的配置类（如果可以导入主配置系统）
- default_sqlite_config: 默认配置实例

使用示例:
    from integrations.database.sqlite import SQLiteClient, SQLiteConfig
    from integrations.database.schemas import Repo, RepoFile

    # 使用默认配置
    client = SQLiteClient()

    # 使用自定义配置
    config = SQLiteConfig(db_path="custom/path/database.db")
    client = SQLiteClient(config)

    # 插入仓库数据
    repo = Repo(
        repo_id=1,  # 可选，如果不提供会自动生成
        repo_path="/path/to/repo",
        updated_at=1234567890,
        term_sparse="{}",
        inverted_index="{}"
    )
    repo_id = client.insert_repo(repo)

    # 查询数据
    repo = client.get_repo("/path/to/repo")
    stats = client.get_database_stats()

    # 与主配置系统集成（如果可用）
    try:
        from integrations.database.sqlite.config import ExtendedConfig
        # 创建包含SQLite配置的完整配置
        full_config = ExtendedConfig(
            # 主配置字段...
            sqlite=SQLiteConfig(db_path="custom.db")
        )
    except ImportError:
        # 主配置系统不可用，使用独立配置
        pass
"""

from .client import SQLiteClient
from core.config import SQLiteConfig

__all__ = [
    "SQLiteClient",
    "SQLiteConfig"
]

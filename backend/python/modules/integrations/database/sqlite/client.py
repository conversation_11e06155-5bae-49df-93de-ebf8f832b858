import os
import sqlite3
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
import logging
from pathlib import Path

from modules.integrations.database.schemas import Repo, RepoFile
from core.config import Config, SQLiteConfig, get_config

logger = logging.getLogger(__name__)

class SQLiteClient:
    """SQLite数据库客户端"""
    
    def __init__(self, config: Config = get_config()):
        """初始化SQLite客户端
        
        Args:
            config: SQLite配置，如果为None则使用默认配置
        """
        self.config = config.database.sqlite
        self._ensure_database_setup()
    
    def _ensure_database_setup(self) -> None:
        """确保数据库和表结构存在"""
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.config.db_path), exist_ok=True)

        # 无论数据库文件是否存在，都检查并创建表结构
        with self.get_connection() as conn:
            self._create_tables(conn)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(
            self.config.db_path,
            timeout=self.config.timeout,
            check_same_thread=self.config.check_same_thread,
            isolation_level=self.config.isolation_level
        )
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            conn.close()
    
    def _create_tables(self, conn: sqlite3.Connection) -> None:
        """创建数据库表"""
        cursor = conn.cursor()

        # 创建仓库表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repos (
                repo_id INTEGER PRIMARY KEY AUTOINCREMENT,
                repo_path TEXT UNIQUE NOT NULL,
                updated_at INTEGER NOT NULL,
                term_sparse TEXT,
                inverted_index TEXT
            )
        """)

        # 创建文件表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repo_files (
                file_id INTEGER PRIMARY KEY AUTOINCREMENT,
                repo_id INTEGER NOT NULL,
                file_path TEXT NOT NULL,
                file_structure TEXT NOT NULL,
                file_hash TEXT NOT NULL,
                updated_at INTEGER NOT NULL,
                FOREIGN KEY (repo_id) REFERENCES repos (repo_id) ON DELETE CASCADE,
                UNIQUE(repo_id, file_path)
            )
        """)



        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_repo_id ON repo_files(repo_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_updated_at ON repo_files(updated_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_repos_updated_at ON repos(updated_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_repos_path ON repos(repo_path)")

        conn.commit()

    def create_repo(self, repo_path: str, updated_at: int, term_sparse: str = None, inverted_index: str = None) -> int:
        """创建新仓库，自动生成repo_id"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否已存在相同路径的仓库
            cursor.execute("SELECT repo_id FROM repos WHERE repo_path = ?", (repo_path,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有仓库
                cursor.execute("""
                    UPDATE repos SET updated_at = ?, term_sparse = ?, inverted_index = ?
                    WHERE repo_id = ?
                """, (updated_at, term_sparse, inverted_index, existing['repo_id']))
                conn.commit()
                return existing['repo_id']
            else:
                # 插入新仓库，让数据库自动生成 repo_id
                cursor.execute("""
                    INSERT INTO repos (repo_path, updated_at, term_sparse, inverted_index)
                    VALUES (?, ?, ?, ?)
                """, (repo_path, updated_at, term_sparse, inverted_index))
                conn.commit()
                return cursor.lastrowid

    def update_repo(self, repo: Repo, fields: List[str]) -> None:
        """更新仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 构建更新字段和值
            update_fields = []
            values = []
            for field in fields:
                if hasattr(repo, field):
                    update_fields.append(f"{field} = ?")
                    values.append(getattr(repo, field))

            if update_fields:
                update_clause = ", ".join(update_fields)
                values.append(repo.repo_id)
                cursor.execute(f"""
                    UPDATE repos SET {update_clause}
                    WHERE repo_id = ?
                """, values)
                conn.commit()

    def get_repo(self, repo_path: str) -> Optional[Repo]:
        """根据路径获取仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repos WHERE repo_path = ?", (repo_path,))
            row = cursor.fetchone()

            if row:
                return Repo(
                    repo_id=row['repo_id'],
                    repo_path=row['repo_path'],
                    updated_at=row['updated_at'],
                    term_sparse=row['term_sparse'],
                    inverted_index=row['inverted_index']
                )
            return None

    def get_repo_by_id(self, repo_id: int) -> Optional[Repo]:
        """根据ID获取仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repos WHERE repo_id = ?", (repo_id,))
            row = cursor.fetchone()

            if row:
                return Repo(
                    repo_id=row['repo_id'],
                    repo_path=row['repo_path'],
                    updated_at=row['updated_at'],
                    term_sparse=row['term_sparse'],
                    inverted_index=row['inverted_index']
                )
            return None
    
    def get_all_repos(self) -> List[Repo]:
        """获取所有仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repos ORDER BY updated_at DESC")
            rows = cursor.fetchall()

            return [
                Repo(
                    repo_id=row['repo_id'],
                    repo_path=row['repo_path'],
                    updated_at=row['updated_at'],
                    term_sparse=row['term_sparse'],
                    inverted_index=row['inverted_index']
                )
                for row in rows
            ]

    def delete_repo(self, repo_path: str) -> bool:
        """删除仓库信息（会级联删除相关的文件）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repos WHERE repo_path = ?", (repo_path,))
            conn.commit()
            return cursor.rowcount > 0

    def delete_repo_by_id(self, repo_id: int) -> bool:
        """根据ID删除仓库信息（会级联删除相关的文件）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repos WHERE repo_id = ?", (repo_id,))
            conn.commit()
            return cursor.rowcount > 0

    # 文件相关操作
    def upsert_file(self, file: RepoFile) -> int:
        """插入或更新文件信息，返回file_id"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 如果有file_id，尝试更新
            if hasattr(file, 'file_id') and file.file_id:
                cursor.execute("""
                    INSERT OR REPLACE INTO repo_files (file_id, repo_id, file_path, file_structure, file_hash, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (file.file_id, file.repo_id, file.file_path, file.file_structure, file.file_hash, file.updated_at))
                conn.commit()
                return file.file_id
            else:
                # 检查是否已存在相同路径的文件
                cursor.execute("SELECT file_id FROM repo_files WHERE repo_id = ? AND file_path = ?", (file.repo_id, file.file_path))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有文件
                    cursor.execute("""
                        UPDATE repo_files SET file_structure = ?, file_hash = ?, updated_at = ?
                        WHERE file_id = ?
                    """, (file.file_structure, file.file_hash, file.updated_at, existing['file_id']))
                    conn.commit()
                    return existing['file_id']
                else:
                    # 插入新文件
                    cursor.execute("""
                        INSERT INTO repo_files (repo_id, file_path, file_structure, file_hash, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (file.repo_id, file.file_path, file.file_structure, file.file_hash, file.updated_at))
                    conn.commit()
                    return cursor.lastrowid

    def update_file_structure(self, file_id: int, file_structure: str) -> bool:
        """更新文件的关键结构

        Args:
            file_id: 文件ID
            file_structure: 新的文件结构

        Returns:
            bool: 如果文件存在并成功更新返回True，否则返回False

        Note:
            这个方法只更新现有文件，不会创建新文件
            如果需要 upsert 功能，请使用 upsert_repository_key_structure
        """
        import time

        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE repo_files
                SET file_structure = ?, updated_at = ?
                WHERE file_id = ?
            """, (file_structure, int(time.time()), file_id))
            conn.commit()

            # 返回是否有记录被更新
            return cursor.rowcount > 0

    def upsert_repository_key_structure(self, repo_id: int, key_structures: Dict[str, str]) -> None:
        """插入或更新仓库中的所有文件的关键结构

        Args:
            repo_id: 仓库ID
            key_structures: 文件路径到关键结构的映射字典

        Note:
            如果文件记录不存在，会创建新的文件记录
            如果文件记录已存在，会更新其关键结构
        """
        import time

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 批量 upsert 文件结构
            for file_path, file_structure in key_structures.items():
                # 首先尝试更新现有记录
                cursor.execute("""
                    UPDATE repo_files
                    SET file_structure = ?, updated_at = ?
                    WHERE repo_id = ? AND file_path = ?
                """, (file_structure, int(time.time()), repo_id, file_path))

                # 如果没有记录被更新，说明文件不存在，需要插入新记录
                if cursor.rowcount == 0:
                    # 生成默认的文件哈希（基于文件路径）
                    import hashlib
                    file_hash = hashlib.sha256(file_path.encode('utf-8')).hexdigest()[:32]

                    cursor.execute("""
                        INSERT INTO repo_files (repo_id, file_path, file_structure, file_hash, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (repo_id, file_path, file_structure, file_hash, int(time.time())))

            conn.commit()


    def get_file(self, file_path: str, repo_id: int = None) -> Optional[RepoFile]:
        """根据路径获取文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if repo_id:
                cursor.execute("SELECT * FROM repo_files WHERE file_path = ? AND repo_id = ?", (file_path, repo_id))
            else:
                cursor.execute("SELECT * FROM repo_files WHERE file_path = ?", (file_path,))

            row = cursor.fetchone()

            if row:
                return RepoFile(
                    repo_id=row['repo_id'],
                    file_id=row['file_id'],
                    file_path=row['file_path'],
                    file_structure=row['file_structure'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
            return None

    def get_file_by_id(self, file_id: int) -> Optional[RepoFile]:
        """根据ID获取文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repo_files WHERE file_id = ?", (file_id,))
            row = cursor.fetchone()

            if row:
                return RepoFile(
                    repo_id=row['repo_id'],
                    file_id=row['file_id'],
                    file_path=row['file_path'],
                    file_structure=row['file_structure'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
            return None

    def get_file_structure(self, file_path: str, repo_id: int = None, repo_path: str = None) -> Optional[str]:
        """获取文件的结构信息

        Args:
            file_path: 文件路径
            repo_id: 仓库ID（可选）
            repo_path: 仓库路径（可选，如果提供则会先查找repo_id）

        Returns:
            Optional[str]: 文件结构信息，如果文件不存在或仓库不存在则返回None

        Note:
            如果同时提供repo_id和repo_path，优先使用repo_id
            如果都不提供，则在所有仓库中查找第一个匹配的文件
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 如果提供了repo_path但没有repo_id，先查找repo_id
            if repo_path and not repo_id:
                cursor.execute("SELECT repo_id FROM repos WHERE repo_path = ?", (repo_path,))
                repo_row = cursor.fetchone()
                if not repo_row:
                    return None  # 仓库不存在
                repo_id = repo_row['repo_id']

            # 根据条件查询文件结构
            if repo_id:
                cursor.execute("""
                    SELECT file_structure FROM repo_files
                    WHERE file_path = ? AND repo_id = ?
                """, (file_path, repo_id))
            else:
                cursor.execute("""
                    SELECT file_structure FROM repo_files
                    WHERE file_path = ?
                    LIMIT 1
                """, (file_path,))

            row = cursor.fetchone()
            return row['file_structure'] if row else None

    def get_files_by_pattern(self, pattern: str, repo_id: int = None) -> List[RepoFile]:
        """根据路径模式获取文件列表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if repo_id:
                cursor.execute("SELECT * FROM repo_files WHERE file_path LIKE ? AND repo_id = ? ORDER BY file_path", (pattern, repo_id))
            else:
                cursor.execute("SELECT * FROM repo_files WHERE file_path LIKE ? ORDER BY file_path", (pattern,))

            rows = cursor.fetchall()

            return [
                RepoFile(
                    repo_id=row['repo_id'],
                    file_id=row['file_id'],
                    file_path=row['file_path'],
                    file_structure=row['file_structure'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
                for row in rows
            ]

    def get_all_files(self, repo_id: int = None) -> List[RepoFile]:
        """获取所有文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if repo_id:
                cursor.execute("SELECT * FROM repo_files WHERE repo_id = ? ORDER BY file_path", (repo_id,))
            else:
                cursor.execute("SELECT * FROM repo_files ORDER BY file_path")

            rows = cursor.fetchall()

            return [
                RepoFile(
                    repo_id=row['repo_id'],
                    file_id=row['file_id'],
                    file_path=row['file_path'],
                    file_structure=row['file_structure'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
                for row in rows
            ]

    def delete_file(self, file_path: str, repo_id: int = None) -> bool:
        """删除文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if repo_id:
                cursor.execute("DELETE FROM repo_files WHERE file_path = ? AND repo_id = ?", (file_path, repo_id))
            else:
                cursor.execute("DELETE FROM repo_files WHERE file_path = ?", (file_path,))

            conn.commit()
            return cursor.rowcount > 0

    def delete_file_by_id(self, file_id: int) -> bool:
        """根据ID删除文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repo_files WHERE file_id = ?", (file_id,))
            conn.commit()
            return cursor.rowcount > 0

    # 统计和查询方法
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 统计各表的记录数
            cursor.execute("SELECT COUNT(*) as count FROM repos")
            repo_count = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM repo_files")
            file_count = cursor.fetchone()['count']

            # 获取数据库文件大小
            db_size = Path(self.config.db_path).stat().st_size if Path(self.config.db_path).exists() else 0

            return {
                "repo_count": repo_count,
                "file_count": file_count,
                "database_size_bytes": db_size,
                "database_path": self.config.db_path
            }



    def clear_all_data(self) -> None:
        """清空所有数据（保留表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repo_files")
            cursor.execute("DELETE FROM repos")
            conn.commit()

    def clear_repo_data(self, repo_id: int) -> None:
        """清空指定仓库的所有数据"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 由于外键约束，删除仓库会级联删除相关文件
            cursor.execute("DELETE FROM repos WHERE repo_id = ?", (repo_id,))
            conn.commit()

    def close(self) -> None:
        """关闭客户端（当前实现中无需特殊处理）"""
        pass

import threading

# 创建线程安全的全局客户端
_client_lock = threading.Lock()
_sqlite_client = None

def get_sqlite_client():
    """获取线程安全的SQLite客户端单例"""
    global _sqlite_client
    if _sqlite_client is None:
        with _client_lock:
            if _sqlite_client is None:  # 双重检查
                _sqlite_client = SQLiteClient(get_config())
    return _sqlite_client


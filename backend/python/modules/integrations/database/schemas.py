from pydantic import BaseModel, Field, ConfigDict
from typing import Optional

class RepoFile(BaseModel):
    """仓库文件结构"""
    model_config = ConfigDict(arbitrary_types_allowed=True)

    repo_id: int = Field(..., description="仓库ID")
    file_id: Optional[int] = Field(default=None, description="文件ID")

    file_path: str = Field(..., description="文件路径")
    file_structure: str = Field(..., description="文件关键行")
    file_hash: str = Field(..., description="文件哈希")

    updated_at: int = Field(..., description="更新时间戳")

class Repo(BaseModel):
    """仓库结构"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    repo_id: int = Field(..., description="仓库ID")
    repo_path: str = Field(..., description="仓库路径")
    updated_at: int = Field(..., description="更新时间戳")
    term_sparse: Optional[str] = Field(default=None, description="BM25json序列化字符串")
    inverted_index: Optional[str] = Field(default=None, description="关键结构json序列化字符串")


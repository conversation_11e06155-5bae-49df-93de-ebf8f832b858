; Rust Tree-sitter 查询模式
; 捕获测试所需的所有构造

; 函数定义（所有类型）
; 匹配样例: fn main() { } 或 fn add(a: i32, b: i32) -> i32 { a + b }
(function_item
    name: (identifier) @name.definition.function) @definition.function

; 结构体定义（所有类型 - 标准、元组、单元）
; 匹配样例: struct Person { name: String } 或 struct Point(i32, i32) 或 struct Unit;
(struct_item
    name: (type_identifier) @name.definition.struct) @definition.struct

; 枚举定义及其变体
; 匹配样例: enum Color { Red, Green, Blue }
(enum_item
    name: (type_identifier) @name.definition.enum) @definition.enum

; Trait 定义
; 匹配样例: trait Display { fn fmt(&self) -> String; }
(trait_item
    name: (type_identifier) @name.definition.trait) @definition.trait

; Impl 块（固有实现）
; 匹配样例: impl Person { fn new() -> Self { } }
(impl_item
    type: (type_identifier) @name.definition.impl) @definition.impl

; Trait 实现
; 匹配样例: impl Display for Person { fn fmt(&self) -> String { } }
(impl_item
    trait: (type_identifier) @name.definition.impl_trait
    type: (type_identifier) @name.definition.impl_for) @definition.impl_trait

; 模块定义
; 匹配样例: mod utils { } 或 mod tests;
(mod_item
    name: (identifier) @name.definition.module) @definition.module

; 宏定义
; 匹配样例: macro_rules! say_hello { () => { println!("Hello!"); }; }
(macro_definition
    name: (identifier) @name.definition.macro) @definition.macro

; 属性宏（用于 #[derive(...)] 等）
; 匹配样例: #[derive(Debug, Clone)]
(attribute_item
    (attribute) @name.definition.attribute) @definition.attribute

; 类型别名
; 匹配样例: type Result<T> = std::result::Result<T, Error>;
(type_item
    name: (type_identifier) @name.definition.type_alias) @definition.type_alias

; 常量
; 匹配样例: const PI: f64 = 3.14159;
(const_item
    name: (identifier) @name.definition.constant) @definition.constant

; 静态项
; 匹配样例: static HELLO_WORLD: &str = "Hello, world!";
(static_item
    name: (identifier) @name.definition.static) @definition.static

; impl 块内的方法
; 匹配样例: impl Person { fn new() -> Self { } }
(impl_item
    body: (declaration_list
        (function_item
            name: (identifier) @name.definition.method))) @definition.method_container

; Use 声明
; 匹配样例: use std::collections::HashMap;
(use_declaration) @definition.use_declaration

; 生命周期定义
; 匹配样例: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str
(lifetime
    "'" @punctuation.lifetime
    (identifier) @name.definition.lifetime) @definition.lifetime

; Where 子句
; 匹配样例: fn generic<T>() where T: Clone + Debug { }
(where_clause
    (where_predicate)*) @definition.where_clause

; Match 表达式
; 匹配样例: match value { Some(x) => x, None => 0 }
(match_expression
    value: (_) @match.value
    body: (match_block)) @definition.match

; Unsafe 块
; 匹配样例: unsafe { *raw_ptr }
(unsafe_block) @definition.unsafe_block

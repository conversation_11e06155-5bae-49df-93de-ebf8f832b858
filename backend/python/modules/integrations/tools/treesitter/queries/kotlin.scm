; Ko<PERSON>in Tree-sitter 查询模式
; 支持的结构：
; - 类声明（普通、数据、抽象、密封、枚举、注解）
; - 接口声明
; - 函数声明（普通、挂起、扩展）
; - 对象声明（包括伴生对象）
; - 属性声明和访问器
; - 类型别名和构造函数

; 类型别名声明
; 匹配样例: typealias StringList = List<String>
(type_alias
  (type_identifier) @name.definition.type_alias
) @definition.type_alias

; 普通类声明
; 匹配样例: class MyClass { }
(class_declaration
  (type_identifier) @name.definition.class
) @definition.class

; 数据类声明
; 匹配样例: data class Person(val name: String, val age: Int)
(class_declaration
  (modifiers
    (class_modifier) @_modifier (#eq? @_modifier "data"))
  (type_identifier) @name.definition.data_class
) @definition.data_class

; 抽象类声明
; 匹配样例: abstract class Shape { abstract fun area(): Double }
(class_declaration
  (modifiers
    (inheritance_modifier) @_modifier (#eq? @_modifier "abstract"))
  (type_identifier) @name.definition.abstract_class
) @definition.abstract_class

; 密封类声明
; 匹配样例: sealed class Result<out T>
(class_declaration
  (modifiers
    (class_modifier) @_modifier (#eq? @_modifier "sealed"))
  (type_identifier) @name.definition.sealed_class
) @definition.sealed_class

; 枚举类声明
; 匹配样例: enum class Color { RED, GREEN, BLUE }
(class_declaration
  (type_identifier)
  (enum_class_body)
) @definition.enum_class

; 接口声明
; 匹配样例: interface MyInterface { fun method() }
(class_declaration
  (type_identifier) @name.definition.interface
) @definition.interface

; 普通函数声明
; 匹配样例: fun myFunction(): String { return "hello" }
(function_declaration
  (simple_identifier) @name.definition.function
) @definition.function

; 挂起函数声明
; 匹配样例: suspend fun fetchData(): String { }
(function_declaration
  (modifiers
    (function_modifier) @_modifier (#eq? @_modifier "suspend"))
  (simple_identifier) @name.definition.suspend_function
) @definition.suspend_function

; 对象声明
; 匹配样例: object MySingleton { }
(object_declaration
  (type_identifier) @name.definition.object
) @definition.object

; 伴生对象声明
; 匹配样例: companion object { }
(companion_object) @definition.companion_object

; 注解类声明
; 匹配样例: annotation class MyAnnotation
(class_declaration
  (modifiers
    (class_modifier) @_modifier (#eq? @_modifier "annotation"))
  (type_identifier) @name.definition.annotation_class
) @definition.annotation_class

; 扩展函数声明
; 匹配样例: fun String.isEmail(): Boolean { }
(function_declaration
  (modifiers
    (function_modifier) @_modifier (#eq? @_modifier "extension"))
  (simple_identifier) @name.definition.extension_function
) @definition.extension_function

; 主构造函数声明
; 匹配样例: class Person(val name: String)
(class_declaration
  (primary_constructor) @definition.primary_constructor
)

; 次构造函数声明
; 匹配样例: constructor(name: String) : this(name, 0)
(secondary_constructor) @definition.secondary_constructor

; 属性声明
; 匹配样例: val name: String, var age: Int
(property_declaration
  (variable_declaration
    (simple_identifier) @name.definition.property)
) @definition.property

; 带访问器的属性声明
; 匹配样例: var name: String get() = field set(value) { field = value }
(property_declaration
  (variable_declaration
    (simple_identifier) @name.definition.property)
  (getter)? @definition.getter
  (setter)? @definition.setter
) @definition.property_with_accessors

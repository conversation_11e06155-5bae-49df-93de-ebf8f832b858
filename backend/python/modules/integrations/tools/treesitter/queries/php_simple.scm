; Simple PHP Tree-sitter query for testing

; Class definition
(class_declaration
  name: (name) @name.definition.class) @definition.class

; Interface definition
(interface_declaration
  name: (name) @name.definition.interface) @definition.interface

; Method definition
(method_declaration
  name: (name) @name.definition.method) @definition.method

; Function definition
(function_definition
  name: (name) @name.definition.function) @definition.function

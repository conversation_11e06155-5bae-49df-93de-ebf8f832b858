; <PERSON><PERSON> Tree-sitter 查询模式
; 支持的 Lua 结构：
; - 函数定义（全局、局部和方法）
; - 表构造器
; - 变量声明
; - 类似类的结构

; 函数定义
; 匹配样例: function myFunction() end
(function_definition_statement
  name: (identifier) @name.definition.function) @definition.function

; 方法定义
; 匹配样例: function MyClass:method() end 或 function MyClass.method() end
(function_definition_statement
  name: (variable
    table: (identifier)
    field: (identifier) @name.definition.method)) @definition.method

; 局部函数定义
; 匹配样例: local function myLocalFunction() end
(local_function_definition_statement
  name: (identifier) @name.definition.function) @definition.function

; 表构造器（类似类的结构）
; 匹配样例: local MyClass = { field1 = "value", method = function() end }
(local_variable_declaration
  (variable_list
    (variable name: (identifier) @name.definition.table))
  (expression_list
    value: (table))) @definition.table

; 变量赋值
; 匹配样例: myVariable = "value"
(variable_assignment
  (variable_list
    (variable name: (identifier) @name.definition.variable))) @definition.variable

; 局部变量声明
; 匹配样例: local myLocalVariable = "value"
(local_variable_declaration
  (variable_list
    (variable name: (identifier) @name.definition.variable))) @definition.variable

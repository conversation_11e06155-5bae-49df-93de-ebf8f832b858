; Swift Tree-sitter 查询模式
;
; 此文件包含 Swift 语言构造的查询模式：
; - 类声明 - 捕获标准、final 和 open 类定义
; - 结构体声明 - 捕获标准和泛型结构体定义
; - 协议声明 - 捕获带要求的协议定义
; - 扩展声明 - 捕获类、结构体和协议的扩展
; - 方法声明 - 捕获实例和类型方法
; - 属性声明 - 捕获存储和计算属性
; - 初始化器声明 - 捕获指定和便利初始化器
; - 析构器声明 - 捕获 deinit 方法
; - 下标声明 - 捕获下标方法
; - 类型别名声明 - 捕获类型别名定义
;
; 每个查询模式都映射到 parseSourceCodeDefinitions.swift.test.ts 中的特定测试

; 类声明 - 捕获标准、final 和 open 类
; 匹配样例: class MyClass { }, final class FinalClass { }, open class OpenClass { }
(class_declaration
  name: (type_identifier) @name) @definition.class

; 协议声明 - 捕获带要求的协议
; 匹配样例: protocol MyProtocol { func method() }
(protocol_declaration
  name: (type_identifier) @name) @definition.interface

; 类/结构体/枚举/扩展中的方法声明
; 匹配样例: func myMethod() { }
(function_declaration
  name: (simple_identifier) @name) @definition.method

; 静态/类方法声明
; 匹配样例: static func staticMethod() { }, class func classMethod() { }
(function_declaration
  (modifiers
    (property_modifier))
  name: (simple_identifier) @name) @definition.static_method

; 初始化器 - 捕获指定初始化器
; 匹配样例: init() { }, init(name: String) { }
(init_declaration
  "init" @name) @definition.initializer

; 便利初始化器
; 匹配样例: convenience init() { }
(init_declaration
  (modifiers (member_modifier))
  "init" @name) @definition.convenience_initializer

; 析构器
; 匹配样例: deinit { }
(deinit_declaration
  "deinit" @name) @definition.deinitializer

; 下标声明
; 匹配样例: subscript(index: Int) -> String { get set }
(subscript_declaration
  (parameter) @name) @definition.subscript

; 属性声明 - 捕获存储属性
; 匹配样例: var name: String, let count: Int
(property_declaration
  (pattern) @name) @definition.property

; 计算属性声明（带访问器）
; 匹配样例: var computed: String { get { return "value" } }
(property_declaration
  (pattern)
  (computed_property)) @definition.computed_property

; 类型别名
; 匹配样例: typealias StringDictionary = [String: String]
(typealias_declaration
  name: (type_identifier) @name) @definition.type_alias

; 协议属性要求
; 匹配样例: protocol MyProtocol { var name: String { get } }
(protocol_property_declaration
  name: (pattern) @name) @definition.protocol_property

; 协议方法要求
; 匹配样例: protocol MyProtocol { func method() }
(protocol_function_declaration
  name: (simple_identifier) @name) @definition.protocol_method

; HTML Tree-sitter 查询模式

; 文档结构
; 匹配样例: 整个 HTML 文档
(document) @definition.document

; 带内容的元素
; 匹配样例: <div>content</div>, <p>text</p>（排除 script 和 style）
(element
  (start_tag
    (tag_name) @name.definition)
  (#not-eq? @name.definition "script")
  (#not-eq? @name.definition "style")) @definition.element

; Script 元素
; 匹配样例: <script>console.log('hello');</script>
(script_element
  (start_tag
    (tag_name) @name.definition)) @definition.script

; Style 元素
; 匹配样例: <style>body { margin: 0; }</style>
(style_element
  (start_tag
    (tag_name) @name.definition)) @definition.style

; 属性
; 匹配样例: class="container", id="main", src="image.jpg"
(attribute
  (attribute_name) @name.definition) @definition.attribute

; 注释
; 匹配样例: <!-- This is a comment -->
(comment) @definition.comment

; 文本内容
; 匹配样例: 元素内的纯文本内容
(text) @definition.text

; 原始文本内容
; 匹配样例: script 或 style 标签内的原始文本
(raw_text) @definition.raw_text

; 空元素（自闭合）
; 匹配样例: <img>, <br>, <hr>, <input> 等
(element
  (start_tag
    (tag_name) @name.definition)
  (#match? @name.definition "^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$")) @definition.void_element

; 自闭合标签
; 匹配样例: <img src="image.jpg" />
(self_closing_tag
  (tag_name) @name.definition) @definition.self_closing_tag

; Doctype 声明
; 匹配样例: <!DOCTYPE html>
(doctype) @definition.doctype

; 多重元素（父元素包含子元素）
; 匹配样例: <div><p>text</p><span>more</span></div>
(element
  (element)+) @definition.nested_elements

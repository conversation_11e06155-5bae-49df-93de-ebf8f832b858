; C 语言 Tree-sitter 查询模式
; Tree-sitter 解析器支持的 C 语言构造：
;
; 1. 类似类的构造：
; - 结构体定义（带字段）
; - 联合体定义（带变体）
; - 枚举定义（带值）
; - 匿名联合体/结构体
; - 对齐结构体
;
; 2. 函数相关构造：
; - 函数定义（带参数）
; - 函数声明（原型）
; - 静态函数
; - 函数指针
;
; 3. 类型定义：
; - typedef 声明（所有类型）
; - 函数指针 typedef
; - 结构体/联合体 typedef
;
; 4. 变量声明：
; - 全局变量
; - 静态变量
; - 数组声明
; - 指针声明
;
; 5. 预处理器构造：
; - 函数式宏
; - 对象式宏
; - 条件编译
; - #include 语句（依赖分析）

; 函数定义 - 有返回类型的函数
; 匹配样例: int main() { return 0; }
(function_definition
  type: (_)
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function)) @definition.function

; 函数定义 - 指针返回类型的函数
; 匹配样例: Point* create_point(int x, int y) { ... }
(function_definition
  type: (_)
  declarator: (pointer_declarator
    declarator: (function_declarator
      declarator: (identifier) @name.definition.function))) @definition.function

; 函数定义 - 没有明确返回类型的函数（如某些特殊情况）
; 匹配样例: main() { return 0; }
(function_definition
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function)) @definition.function

; 函数声明（原型）- 只匹配有参数列表的函数声明
; 匹配样例: int add(int a, int b);
(declaration
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function
    parameters: (parameter_list))) @definition.function

; 函数指针声明 - 带初始化
; 匹配样例: int (*operation)(int, int) = add;
(declaration
  type: (_)
  declarator: (init_declarator
    declarator: (function_declarator
      declarator: (parenthesized_declarator
        (pointer_declarator
          declarator: (identifier) @name.definition.function_pointer))
      parameters: (parameter_list)))) @definition.function_pointer

; 函数指针声明 - 不带初始化
; 匹配样例: int (*func_ptr)(int, int);
(declaration
  type: (_)
  declarator: (function_declarator
    declarator: (parenthesized_declarator
      (pointer_declarator
        declarator: (identifier) @name.definition.function_pointer))
    parameters: (parameter_list))) @definition.function_pointer

; Typedef 结构体定义 - 只匹配第一行
; 匹配样例: typedef struct Point { int x, y; } Point;
; 匹配样例: typedef struct { int x, y; } Point;
(type_definition
  type: (struct_specifier) @definition.struct)

; Typedef 联合体定义 - 只匹配第一行
; 匹配样例: typedef union Data { int i; float f; } Data;
; 匹配样例: typedef union { int i; float f; } Data;
(type_definition
  type: (union_specifier) @definition.union)

; Typedef 枚举定义 - 只匹配第一行
; 匹配样例: typedef enum Color { RED, GREEN, BLUE } Color;
; 匹配样例: typedef enum { RED, GREEN, BLUE } Color;
(type_definition
  type: (enum_specifier) @definition.enum)

; 独立的结构体定义（非typedef）
; 匹配样例: struct Point { int x, y; };
(declaration
  type: (struct_specifier
    name: (type_identifier) @name.definition.struct)) @definition.struct

; 独立的联合体定义（非typedef）
; 匹配样例: union Data { int i; float f; };
(declaration
  type: (union_specifier
    name: (type_identifier) @name.definition.union)) @definition.union

; 独立的枚举定义（非typedef）
; 匹配样例: enum Color { RED, GREEN, BLUE };
(declaration
  type: (enum_specifier
    name: (type_identifier) @name.definition.enum)) @definition.enum

; 简单 Typedef 声明（非结构体/联合体/枚举）
; 匹配样例: typedef int MyInt;
(type_definition
  type: (primitive_type)
  declarator: (type_identifier) @name.definition.type) @definition.type

; 指针类型 Typedef
; 匹配样例: typedef int* IntPtr;
(type_definition
  type: (primitive_type)
  declarator: (pointer_declarator
    declarator: (type_identifier) @name.definition.type)) @definition.type

; 简单变量声明（非函数）
; 匹配样例: int global_var;
(declaration
  (storage_class_specifier)?
  type: (_)
  declarator: (identifier) @name.definition.variable) @definition.variable

; 带初始化的变量声明
; 匹配样例: static int count = 0;
(declaration
  (storage_class_specifier)?
  type: (_)
  declarator: (init_declarator
    declarator: (identifier) @name.definition.variable)) @definition.variable

; 指针变量声明
; 匹配样例: int* ptr;
(declaration
  type: (_)
  declarator: (pointer_declarator
    declarator: (identifier) @name.definition.variable)) @definition.variable

; 数组声明
; 匹配样例: int arr[10];
(declaration
  type: (_)
  declarator: (array_declarator
    declarator: (identifier) @name.definition.variable)) @definition.variable

; 对象式宏
; 匹配样例: #define PI 3.14159
(preproc_def
  name: (identifier) @name.definition.macro) @definition.macro

; 函数式宏
; 匹配样例: #define MAX(a, b) ((a) > (b) ? (a) : (b))
(preproc_function_def
  name: (identifier) @name.definition.macro) @definition.macro

; #include 语句（系统头文件）
; 匹配样例: #include <stdio.h>
(preproc_include
  path: (system_lib_string) @name.definition.include) @definition.include

; #include 语句（用户头文件）
; 匹配样例: #include "myheader.h"
(preproc_include
  path: (string_literal) @name.definition.include) @definition.include

; #include 语句（标识符形式）
; 匹配样例: #include MACRO_HEADER
(preproc_include
  path: (identifier) @name.definition.include) @definition.include

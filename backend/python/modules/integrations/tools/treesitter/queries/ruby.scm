; Ruby Tree-sitter 查询模式
; 支持的结构：
; - 方法定义（包括单例方法和别名，带相关注释）
; - 类定义（包括单例类，带相关注释）
; - 模块定义
; - 常量
; - 全局变量
; - 实例变量
; - 类变量
; - 符号
; - 块、过程和 lambda
; - 混入（include, extend, prepend）
; - 元编程构造（define_method, method_missing）
; - 属性访问器（attr_reader, attr_writer, attr_accessor）
; - 类宏（has_many, belongs_to 等 Rails 风格代码）
; - 异常处理（begin/rescue/ensure）
; - 关键字参数
; - splat 操作符
; - hash rocket 和 JSON 风格哈希
; - 字符串插值
; - 正则表达式
; - Ruby 2.7+ 模式匹配
; - Ruby 3.0+ 无尽方法
; - Ruby 3.1+ pin 操作符和简写哈希语法

; 方法定义
; 匹配样例: def my_method; end
(method
  name: (identifier) @name.definition.method) @definition.method

; 单例方法
; 匹配样例: def self.class_method; end 或 def obj.singleton_method; end
(singleton_method
  object: (_)
  name: (identifier) @name.definition.method) @definition.method

; 方法别名
; 匹配样例: alias new_name old_name
(alias
  name: (_) @name.definition.method) @definition.method

; 类定义
; 匹配样例: class MyClass; end 或 class Namespace::MyClass; end
(class
  name: [
    (constant) @name.definition.class
    (scope_resolution
      name: (_) @name.definition.class)
  ]) @definition.class

; 单例类
; 匹配样例: class << self; end 或 class << obj; end
(singleton_class
  value: [
    (constant) @name.definition.class
    (scope_resolution
      name: (_) @name.definition.class)
  ]) @definition.class

; 模块定义
; 匹配样例: module MyModule; end 或 module Namespace::MyModule; end
(module
  name: [
    (constant) @name.definition.module
    (scope_resolution
      name: (_) @name.definition.module)
  ]) @definition.module

; 常量
; 匹配样例: MY_CONSTANT = "value"
(assignment
  left: (constant) @name.definition.constant) @definition.constant

; 全局变量
; 匹配样例: $global_var
(global_variable) @definition.global_variable

; 实例变量
; 匹配样例: @instance_var
(instance_variable) @definition.instance_variable

; 类变量
; 匹配样例: @@class_var
(class_variable) @definition.class_variable

; 符号
; 匹配样例: :symbol 或 symbol: (作为哈希键)
(simple_symbol) @definition.symbol
(hash_key_symbol) @definition.symbol

; 块
; 匹配样例: { |x| x * 2 } 或 do |x| x * 2 end
(block) @definition.block
(do_block) @definition.block

; 基本混入语句 - 捕获所有 include/extend/prepend 调用
; 匹配样例: include MyModule, extend OtherModule, prepend AnotherModule
(call
  method: (identifier) @_mixin_method
  arguments: (argument_list
    (constant) @name.definition.mixin)
  (#match? @_mixin_method "^(include|extend|prepend)$")) @definition.mixin

; 混入模块定义
; 匹配样例: module MyModule; end（以 Module 结尾的模块）
(module
  name: (constant) @name.definition.mixin_module
  (#match? @name.definition.mixin_module ".*Module$")) @definition.mixin_module

; 混入相关方法
; 匹配样例: def included_method, def extended_method, def prepended_method
(method
  name: (identifier) @name.definition.mixin_method
  (#match? @name.definition.mixin_method "(included|extended|prepended)_method")) @definition.mixin_method

; 单例类块
; 匹配样例: class << self; end
(singleton_class) @definition.singleton_class

; 单例上下文中的类方法
; 匹配样例: def self.class_method; end
(singleton_method
  object: (self)
  name: (identifier) @name.definition.singleton_method) @definition.singleton_method

; 属性访问器
; 匹配样例: attr_accessor :name, :age
(call
  method: (identifier) @_attr_accessor
  arguments: (argument_list
    (_) @name.definition.attr_accessor)
  (#eq? @_attr_accessor "attr_accessor")) @definition.attr_accessor

; 属性读取器
; 匹配样例: attr_reader :name
(call
  method: (identifier) @_attr_reader
  arguments: (argument_list
    (_) @name.definition.attr_reader)
  (#eq? @_attr_reader "attr_reader")) @definition.attr_reader

; 属性写入器
; 匹配样例: attr_writer :name
(call
  method: (identifier) @_attr_writer
  arguments: (argument_list
    (_) @name.definition.attr_writer)
  (#eq? @_attr_writer "attr_writer")) @definition.attr_writer

; 类宏（Rails 风格）
; 匹配样例: has_many :posts, belongs_to :user, validates :name
(call
  method: (identifier) @_macro_name
  arguments: (argument_list
    (_) @name.definition.class_macro)
  (#match? @_macro_name "^(has_many|belongs_to|has_one|validates|scope|before_action|after_action)$")) @definition.class_macro

; 异常处理
; 匹配样例: begin; rescue; ensure; end
(begin) @definition.begin
(rescue) @definition.rescue
(ensure) @definition.ensure

; 关键字参数
; 匹配样例: def method(name:, age: 18); end
(keyword_parameter
  name: (identifier) @name.definition.keyword_parameter) @definition.keyword_parameter

; Splat 操作符
; 匹配样例: def method(*args, **kwargs); end
(splat_parameter) @definition.splat_parameter
(splat_argument) @definition.splat_argument

; 哈希语法变体
; 匹配样例: { :key => "value" } 或 { key: "value" }
(pair
  key: (_) @name.definition.hash_key) @definition.hash_pair

; String interpolation - capture the string with interpolation and surrounding context
(assignment
  left: (identifier) @name.definition.string_var
  right: (string
    (interpolation))) @definition.string_interpolation

; Regular expressions - capture the regex pattern and assignment
(assignment
  left: (identifier) @name.definition.regex_var
  right: (regex)) @definition.regex_assignment

; Pattern matching - capture the entire case_match structure
(case_match) @definition.case_match

; Pattern matching - capture in_clause with hash pattern
(in_clause
  pattern: (hash_pattern)) @definition.hash_pattern_clause

; Endless methods - capture the method definition with name and surrounding context
(comment) @_endless_method_comment
(#match? @_endless_method_comment "Ruby 3.0\\+ endless method")
(method
  name: (identifier) @name.definition.endless_method
  body: (binary
    operator: "=")) @definition.endless_method

; Pin operator - capture the entire in_clause with variable_reference_pattern
(in_clause
  pattern: (variable_reference_pattern)) @definition.pin_pattern_clause

; Shorthand hash syntax - capture the method containing shorthand hash
(comment) @_shorthand_hash_comment
(#match? @_shorthand_hash_comment "Ruby 3.1\\+ shorthand hash syntax")
(method
  name: (identifier) @name.definition.shorthand_method) @definition.shorthand_method

; Shorthand hash syntax - capture the hash with shorthand syntax
(hash
  (pair
    (hash_key_symbol)
    ":")) @definition.shorthand_hash

; Capture larger contexts for features that need at least 4 lines

; Capture the entire program to include all comments and code
(program) @definition.program

; Capture all comments
(comment) @definition.comment

; Capture all method definitions
(method) @definition.method_all

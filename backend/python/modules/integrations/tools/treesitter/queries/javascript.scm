; JavaScript Tree-sitter 查询模式
; 支持的结构：
; - 类定义
; - 方法定义（包括装饰器方法）
; - 命名函数声明
; - 箭头函数和赋值给变量的函数表达式
; - JSON 对象和数组定义（用于 JSON 文件）
; - 装饰器和装饰元素
; - Import/Export 语句（依赖分析）

; 方法定义（带注释文档）
; 匹配样例: class MyClass { myMethod() { } }
(
  (comment)* @doc
  .
  (method_definition
    name: (property_identifier) @name) @definition.method
  (#not-eq? @name "constructor")
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.method)
)

; 类定义（带注释文档）
; 匹配样例: class MyClass { } 或 const MyClass = class { }
(
  (comment)* @doc
  .
  [
    (class
      name: (_) @name)
    (class_declaration
      name: (_) @name)
  ] @definition.class
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.class)
)

; 函数声明（带注释文档）
; 匹配样例: function myFunction() { } 或 function* myGenerator() { }
(
  (comment)* @doc
  .
  [
    (function_declaration
      name: (identifier) @name)
    (generator_function_declaration
      name: (identifier) @name)
  ] @definition.function
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

; 词法声明中的函数（let/const）
; 匹配样例: const myFunc = () => { } 或 let myFunc = function() { }
(
  (comment)* @doc
  .
  (lexical_declaration
    (variable_declarator
      name: (identifier) @name
      value: [(arrow_function) (function_expression)]) @definition.function)
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

; 变量声明中的函数（var）
; 匹配样例: var myFunc = () => { } 或 var myFunc = function() { }
(
  (comment)* @doc
  .
  (variable_declaration
    (variable_declarator
      name: (identifier) @name
      value: [(arrow_function) (function_expression)]) @definition.function)
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

; JSON 对象定义
; 匹配样例: { "key": "value" }
(object) @object.definition

; JSON 对象键值对
; 匹配样例: "name": "John", "age": 30, "active": true
(pair
  key: (string) @property.name.definition
  value: [
    (object) @object.value
    (array) @array.value
    (string) @string.value
    (number) @number.value
    (true) @boolean.value
    (false) @boolean.value
    (null) @null.value
  ]
) @property.definition

; JSON 数组定义
; 匹配样例: [1, 2, 3] 或 ["a", "b", "c"]
(array) @array.definition

; 装饰器方法定义
; 匹配样例: class MyClass { @decorator myMethod() { } }
(
  [
    (method_definition
      decorator: (decorator)
      name: (property_identifier) @name) @definition.method
    (method_definition
      decorator: (decorator
        (call_expression
          function: (identifier) @decorator_name))
      name: (property_identifier) @name) @definition.method
  ]
  (#not-eq? @name "constructor")
)

; 装饰器类定义
; 匹配样例: @decorator class MyClass { }
(
  [
    (class
      decorator: (decorator)
      name: (_) @name) @definition.class
    (class_declaration
      decorator: (decorator)
      name: (_) @name) @definition.class
  ]
)

; 捕获装饰器类中的方法名
; 匹配样例: @decorator class MyClass { myMethod() { } }
(
  (class_declaration
    decorator: (decorator)
    body: (class_body
      (method_definition
        name: (property_identifier) @name) @definition.method))
  (#not-eq? @name "constructor")
)

; Import 语句
; 匹配样例: import { Component } from 'react';
(import_statement
  source: (string) @name.definition.import) @definition.import

; 命名导入
; 匹配样例: import { useState, useEffect } from 'react';
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @name.definition.import_specifier)))) @definition.import

; 默认导入
; 匹配样例: import React from 'react';
(import_statement
  (import_clause
    (identifier) @name.definition.import_default)) @definition.import

; 命名空间导入
; 匹配样例: import * as React from 'react';
(import_statement
  (import_clause
    (namespace_import
      (identifier) @name.definition.import_namespace))) @definition.import

; Export 语句
; 匹配样例: export { Component };
(export_statement
  (export_clause
    (export_specifier
      name: (identifier) @name.definition.export))) @definition.export

; 默认导出
; 匹配样例: export default MyComponent;
(export_statement
  value: (identifier) @name.definition.export_default) @definition.export

; 导出声明
; 匹配样例: export const myVar = 10;
(export_statement
  declaration: (lexical_declaration
    (variable_declarator
      name: (identifier) @name.definition.export_declaration))) @definition.export

; 重新导出
; 匹配样例: export { Component } from 'react';
(export_statement
  source: (string) @name.definition.reexport) @definition.export

; require 语句（CommonJS）
; 匹配样例: const fs = require('fs');
(variable_declaration
  (variable_declarator
    name: (identifier) @name.definition.require_var
    value: (call_expression
      function: (identifier) @func_name
      arguments: (arguments
        (string) @name.definition.require_source)))
  (#eq? @func_name "require")) @definition.require

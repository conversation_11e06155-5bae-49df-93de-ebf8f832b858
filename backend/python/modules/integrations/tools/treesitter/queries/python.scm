; Python Tree-sitter 查询模式

; 类定义（包括装饰器类）
; 匹配样例: class MyClass: pass
(class_definition
  name: (identifier) @name.definition.class) @definition.class

; 带装饰器的类定义
; 匹配样例: @dataclass class MyClass: pass
(decorated_definition
  definition: (class_definition
    name: (identifier) @name.definition.class)) @definition.class

; 函数和方法定义（包括异步和装饰器函数）
; 匹配样例: def my_function(): pass
(function_definition
  name: (identifier) @name.definition.function) @definition.function

; 带装饰器的函数定义
; 匹配样例: @property def my_method(self): pass
(decorated_definition
  definition: (function_definition
    name: (identifier) @name.definition.function)) @definition.function

; Lambda 表达式
; 匹配样例: my_lambda = (lambda x: x * 2)
(expression_statement
  (assignment
    left: (identifier) @name.definition.lambda
    right: (parenthesized_expression
      (lambda)))) @definition.lambda

; 生成器函数（包含 yield 的函数）
; 匹配样例: def my_generator(): yield 1
(function_definition
  name: (identifier) @name.definition.generator
  body: (block
    (expression_statement
      (yield)))) @definition.generator

; 推导式
; 匹配样例: my_list = [x for x in range(10)]
(expression_statement
  (assignment
    left: (identifier) @name.definition.comprehension
    right: [
      (list_comprehension)
      (dictionary_comprehension)
      (set_comprehension)
    ])) @definition.comprehension

; with 语句
; 匹配样例: with open('file.txt') as f: pass
(with_statement) @definition.with_statement

; try 语句
; 匹配样例: try: pass except: pass
(try_statement) @definition.try_statement

; 导入语句
; 匹配样例: from module import function 或 import module
(import_from_statement) @definition.import
(import_statement) @definition.import

; Global/Nonlocal 语句
; 匹配样例: def func(): global x
(function_definition
  body: (block
    [(global_statement) (nonlocal_statement)])) @definition.scope

; Match case 语句（Python 3.10+）
; 匹配样例: def func(): match x: case 1: pass
(function_definition
  body: (block
    (match_statement))) @definition.match_case

; 类型注解
; 匹配样例: def func(x: int) -> str: pass
(typed_parameter
  type: (type)) @definition.type_annotation

; 变量类型注解
; 匹配样例: x: int = 10
(expression_statement
  (assignment
    left: (identifier) @name.definition.type
    type: (type))) @definition.type_annotation

; Java Tree-sitter 查询模式

; 模块声明（Java 9+）
; 匹配样例: module com.example.myapp { }
(module_declaration
  name: (scoped_identifier) @name.definition.module) @definition.module

; 包声明
; 匹配样例: package com.example.myapp;
((package_declaration
  (scoped_identifier)) @name.definition.package) @definition.package

; 行注释
; 匹配样例: // This is a comment
(line_comment) @definition.comment

; 类声明
; 匹配样例: public class MyClass { }
(class_declaration
  name: (identifier) @name.definition.class) @definition.class

; 接口声明
; 匹配样例: public interface MyInterface { }
(interface_declaration
  name: (identifier) @name.definition.interface) @definition.interface

; 枚举声明
; 匹配样例: enum Color { RED, GREEN, BLUE }
(enum_declaration
  name: (identifier) @name.definition.enum) @definition.enum

; 记录声明（Java 14+）
; 匹配样例: record Person(String name, int age) { }
(record_declaration
  name: (identifier) @name.definition.record) @definition.record

; 注解声明
; 匹配样例: @interface MyAnnotation { }
(annotation_type_declaration
  name: (identifier) @name.definition.annotation) @definition.annotation

; 构造函数声明
; 匹配样例: public MyClass() { }
(constructor_declaration
  name: (identifier) @name.definition.constructor) @definition.constructor

; 方法声明
; 匹配样例: public void myMethod() { }
(method_declaration
  name: (identifier) @name.definition.method) @definition.method

; 内部类声明
; 匹配样例: class Outer { class Inner { } }
(class_declaration
  (class_body
    (class_declaration
      name: (identifier) @name.definition.inner_class))) @definition.inner_class

; 静态嵌套类声明
; 匹配样例: class Outer { static class Nested { } }
(class_declaration
  (class_body
    (class_declaration
      name: (identifier) @name.definition.static_nested_class))) @definition.static_nested_class

; Lambda 表达式
; 匹配样例: (x, y) -> x + y
(lambda_expression) @definition.lambda

; 字段声明
; 匹配样例: private int count = 0;
(field_declaration
  (modifiers)?
  type: (_)
  declarator: (variable_declarator
    name: (identifier) @name.definition.field)) @definition.field

; 导入声明
; 匹配样例: import java.util.List;
(import_declaration
  (scoped_identifier) @name.definition.import) @definition.import

; 类型参数
; 匹配样例: class MyClass<T, U> { }
(type_parameters
  (type_parameter) @name.definition.type_parameter) @definition.type_parameter

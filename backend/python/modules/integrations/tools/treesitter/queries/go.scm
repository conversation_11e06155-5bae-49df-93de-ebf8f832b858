; Go Tree-sitter 查询模式

; 包声明
; 匹配样例: package main
(package_clause
  (package_identifier) @name.definition.package)

; 导入声明
; 匹配样例: import "fmt" 或 import ( "fmt" "os" )
(import_declaration
  (import_spec_list
    (import_spec path: (_) @name.definition.import)))

; 常量声明
; 匹配样例: const PI = 3.14
(const_declaration
  (const_spec name: (identifier) @name.definition.const))

; 变量声明
; 匹配样例: var x int = 10
(var_declaration
  (var_spec name: (identifier) @name.definition.var))

; 接口声明
; 匹配样例: type Writer interface { Write([]byte) (int, error) }
(type_declaration
  (type_spec
    name: (type_identifier) @name.definition.interface
    type: (interface_type)))

; 结构体声明
; 匹配样例: type Person struct { Name string; Age int }
(type_declaration
  (type_spec
    name: (type_identifier) @name.definition.struct
    type: (struct_type)))

; 类型声明
; 匹配样例: type MyInt int
(type_declaration
  (type_spec
    name: (type_identifier) @name.definition.type))

; 函数声明
; 匹配样例: func main() { }
(function_declaration
  name: (identifier) @name.definition.function)

; 方法声明
; 匹配样例: func (p Person) String() string { }
(method_declaration
  name: (field_identifier) @name.definition.method)

; 通道操作
; 匹配样例: chan int 或 <-chan string
(channel_type) @name.definition.channel

; Goroutine 声明
; 匹配样例: go func() { }()
(go_statement) @name.definition.goroutine

; Defer 语句
; 匹配样例: defer file.Close()
(defer_statement) @name.definition.defer

; Select 语句
; 匹配样例: select { case <-ch: }
(select_statement) @name.definition.select

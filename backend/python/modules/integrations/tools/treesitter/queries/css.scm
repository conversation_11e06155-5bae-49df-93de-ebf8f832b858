; CSS Tree-sitter 查询模式

; CSS 规则集和选择器
; 匹配样例: .my-class { color: red; }
(rule_set
  (selectors
    (class_selector
      (class_name) @name.definition.ruleset)) @_rule
  (#match? @name.definition.ruleset "test-ruleset-definition"))

; 伪类选择器
; 匹配样例: .my-class:hover { color: blue; }
(rule_set
  (selectors
    (pseudo_class_selector
      (class_selector
        (class_name) @name.definition.selector))) @_selector
  (#match? @name.definition.selector "test-selector-definition"))

; 媒体查询
; 匹配样例: @media (max-width: 768px) { .container { width: 100%; } }
(media_statement
  (block
    (rule_set
      (selectors
        (class_selector
          (class_name) @name.definition.media_query)))) @_media
  (#match? @name.definition.media_query "test-media-query-definition-container"))

; 关键帧动画
; 匹配样例: @keyframes fadeIn { 0% { opacity: 0; } 100% { opacity: 1; } }
(keyframes_statement
  (keyframes_name) @name.definition.keyframe) @_keyframe
  (#match? @name.definition.keyframe "test-keyframe-definition-fade")

; 动画相关类
; 匹配样例: .animated { animation: fadeIn 1s ease-in-out; }
(rule_set
  (selectors
    (class_selector
      (class_name) @name.definition.animation)) @_animation
  (#match? @name.definition.animation "test-animation-definition"))

; 函数
; 匹配样例: .gradient { background: linear-gradient(to right, red, blue); }
(rule_set
  (selectors
    (class_selector
      (class_name) @name.definition.function)) @_function
  (#match? @name.definition.function "test-function-definition"))

; 变量（CSS 自定义属性）
; 匹配样例: :root { --primary-color: #007bff; }
(declaration
  (property_name) @name.definition.variable) @_variable
  (#match? @name.definition.variable "^--test-variable-definition")

; 导入语句
; 匹配样例: @import "styles.css";
(import_statement
  (string_value) @name.definition.import) @_import
  (#match? @name.definition.import "test-import-definition")

; 嵌套规则集
; 匹配样例: .parent { .child { color: red; } }（在 SCSS/SASS 中）
(rule_set
  (selectors
    (class_selector
      (class_name) @name.definition.nested_ruleset)) @_nested
  (#match? @name.definition.nested_ruleset "test-nested-ruleset-definition"))

; 混入（使用 CSS 自定义属性作为代理）
; 匹配样例: .mixin { --mixin-property: value; }
(rule_set
  (selectors
    (class_selector
      (class_name) @name.definition.mixin)) @_mixin
  (#match? @name.definition.mixin "test-mixin-definition"))

; PHP Tree-sitter 查询模式 - 标准化版本
;
; 此查询文件捕获 PHP 语言构造，用于代码导航和分析。
; 每个查询模式按构造类型组织，并包含清晰的注释。
;
; 支持的语言构造：
; ------------------------------
; 1. 类定义
;    - 普通类
;    - 抽象类
;    - 最终类
;    - 只读类（PHP 8.2+）
;
; 2. 接口和 Trait 定义
;    - 接口
;    - Trait
;    - 枚举（PHP 8.1+）
;
; 3. 函数和方法定义
;    - 全局函数
;    - 类方法
;    - 静态方法
;    - 抽象方法
;    - 最终方法
;    - 箭头函数（PHP 7.4+）
;
; 4. 属性定义
;    - 普通属性
;    - 静态属性
;    - 只读属性（PHP 8.1+）
;    - 构造函数属性提升（PHP 8.0+）
;
; 5. 其他语言构造
;    - 常量
;    - 命名空间
;    - Use 语句（导入）
;    - 匿名类
;    - 属性（PHP 8.0+）
;    - Match 表达式（PHP 8.0+）
;    - Heredoc 和 nowdoc 语法

;--------------------------
; 1. 类定义
;--------------------------
; 普通类
; 匹配样例: class MyClass { }
(class_declaration
  name: (name) @name.definition.class) @definition.class

; 抽象类
; 匹配样例: abstract class AbstractClass { }
(class_declaration
  (abstract_modifier)
  name: (name) @name.definition.abstract_class) @definition.abstract_class

; 最终类
; 匹配样例: final class FinalClass { }
(class_declaration
  (final_modifier)
  name: (name) @name.definition.final_class) @definition.final_class

; 只读类（PHP 8.2+）
; 匹配样例: readonly class ReadonlyClass { }
(class_declaration
  (readonly_modifier)
  name: (name) @name.definition.readonly_class) @definition.readonly_class

;--------------------------
; 2. 接口和 Trait 定义
;--------------------------
; 接口
; 匹配样例: interface MyInterface { }
(interface_declaration
  name: (name) @name.definition.interface) @definition.interface

; Trait
; 匹配样例: trait MyTrait { }
(trait_declaration
  name: (name) @name.definition.trait) @definition.trait

; 枚举（PHP 8.1+）
; 匹配样例: enum Status { case PENDING; case APPROVED; }
(enum_declaration
  name: (name) @name.definition.enum) @definition.enum

;--------------------------
; 3. 函数和方法定义
;--------------------------
; 全局函数
; 匹配样例: function myFunction() { }
(function_definition
  name: (name) @name.definition.function) @definition.function

; 普通方法
; 匹配样例: public function myMethod() { }
(method_declaration
  name: (name) @name.definition.method) @definition.method

; 静态方法
; 匹配样例: public static function staticMethod() { }
(method_declaration
  (static_modifier)
  name: (name) @name.definition.static_method) @definition.static_method

; 抽象方法
; 匹配样例: abstract public function abstractMethod();
(method_declaration
  (abstract_modifier)
  name: (name) @name.definition.abstract_method) @definition.abstract_method

; 最终方法
; 匹配样例: final public function finalMethod() { }
(method_declaration
  (final_modifier)
  name: (name) @name.definition.final_method) @definition.final_method

; 箭头函数（PHP 7.4+）
; 匹配样例: $fn = fn($x) => $x * 2;
(arrow_function) @definition.arrow_function

;--------------------------
; 4. 属性定义
;--------------------------
; 普通属性
; 匹配样例: public $property;
(property_declaration
  (property_element
    (variable_name
      (name) @name.definition.property))) @definition.property

; 静态属性
; 匹配样例: public static $staticProperty;
(property_declaration
  (static_modifier)
  (property_element
    (variable_name
      (name) @name.definition.static_property))) @definition.static_property

; 只读属性（PHP 8.1+）
; 匹配样例: public readonly string $readonlyProperty;
(property_declaration
  (readonly_modifier)
  (property_element
    (variable_name
      (name) @name.definition.readonly_property))) @definition.readonly_property

; 构造函数属性提升（PHP 8.0+）
; 匹配样例: public function __construct(public string $name) { }
; (property_promotion_parameter
;   name: (variable_name
;     (name) @name.definition.promoted_property)) @definition.promoted_property

; 其他语言构造
; 常量
; 匹配样例: const MY_CONSTANT = 'value';
(const_declaration
  (const_element
    (name) @name.definition.constant)) @definition.constant

; 命名空间
; 匹配样例: namespace App\Controllers;
(namespace_definition
  name: (namespace_name) @name.definition.namespace) @definition.namespace

; Use 语句（导入）
; 匹配样例: use App\Models\User;
(namespace_use_declaration
  (namespace_use_clause
    (qualified_name) @name.definition.use)) @definition.use

; 匿名类
; 匹配样例: new class { };
(object_creation_expression
  (declaration_list)) @definition.anonymous_class

; 属性（PHP 8.0+）
; 匹配样例: #[Route('/api/users')]
(attribute_group
  (attribute
    (name) @name.definition.attribute)) @definition.attribute

; Match 表达式（PHP 8.0+）
; 匹配样例: match($value) { 1 => 'one', 2 => 'two' }
(match_expression) @definition.match_expression

; Heredoc 语法
; 匹配样例: <<<EOT ... EOT;
(heredoc) @definition.heredoc

; Nowdoc 语法
; 匹配样例: <<<'EOT' ... EOT;
(nowdoc) @definition.nowdoc

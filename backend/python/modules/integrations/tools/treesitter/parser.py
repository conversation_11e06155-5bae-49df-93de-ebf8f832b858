from pathlib import Path
from typing import Dict, List, Optional, Tuple
import threading
from tree_sitter import Node, Parser, Query, QueryCursor

from modules.chunks.base_line_chunk import BaseLineChunk
from core.config import ChunkConfig
from modules.common.constants import LanguageEnum
from modules.common.schema import Chunk
from utils.trace_logger import get_trace_logger


trace_logger = get_trace_logger(__name__)

class FileParser:
    """Manages tree-sitter parsers for different languages with thread safety."""
    def __init__(self, chunk_config: ChunkConfig):
        self.language_parsers: Dict[LanguageEnum, Parser] = {}
        self.language_queries: Dict[LanguageEnum, Query] = {}
        self.config = chunk_config
        # 线程安全锁
        self._parser_lock = threading.RLock()  # 使用可重入锁
        self._query_lock = threading.RLock()   # 使用可重入锁

    def parse(self, file_path: str, file_content: str) -> Tuple[str, List[Chunk]]:
        # 根据文件后缀名选择对应的解析器
        file_extension = file_path.split('.')[-1]
        language_enum = LanguageEnum.from_suffix(file_extension)
        
        if language_enum is None:
            trace_logger.warning(f"Unsupported file extension: {file_extension}")
            return "", []

        # 线程安全地加载parser
        parser = self._get_or_create_parser(language_enum)
        if parser is None:
            trace_logger.warning(f"Failed to get parser for {language_enum}")
            

        # 线程安全地加载query
        query = self._get_or_create_query(language_enum, parser)
        if query is None:
            trace_logger.warning(f"Failed to get query for {language_enum}")
            
        if not parser or not query: # 当不支持的语言时，使用默认的行级切分
            return BaseLineChunk().chunk_file(file_path, file_content)
        
        # 解析文件
        tree = parser.parse(file_content.encode("utf-8"))
        query_cursor = QueryCursor(query)
        captures = query_cursor.captures(tree.root_node)

        # 解析文件结构
        definitions: List[Tuple[Node, str]] = []
        processed_nodes = set()
        for capture_name, nodes in captures.items():
            if "definition" in capture_name:
                for node in nodes:
                    node_key = (node.start_point, node.end_point)
                    if node_key not in processed_nodes:
                        definitions.append((node, capture_name))
                        processed_nodes.add(node_key)

        definitions.sort(key=lambda x: (x[0].start_point[0], -x[0].end_point[0])) # 如果一个结构匹配到多个definition，取最长的那一个，较短的会被忽略

        lines = file_content.splitlines()
        file_chunks = []
        key_structure_lines: Dict[int, str] = {}
        
        seen_start_lines = set()
        seen_min_start_line, seen_max_end_line = 0, 0

        prev_nodes: List[Node] = []

        for node, capture_name in definitions:
            if node.start_point[0] in seen_start_lines:
                continue
            seen_start_lines.add(node.start_point[0])

            # 记录关键结构行
            if self._is_keynode(capture_name):
                key_structure_lines[node.start_point[0]] = (node.start_point[1], lines[node.start_point[0]])
                key_structure_lines[node.end_point[0]] = (node.end_point[1], lines[node.end_point[0]])

            if node.start_point[0] >= seen_min_start_line and node.end_point[0] <= seen_max_end_line:
                continue
            seen_min_start_line = min(seen_min_start_line, node.start_point[0])
            seen_max_end_line = max(seen_max_end_line, node.end_point[0])

            node_line_cnt = node.end_point[0] - node.start_point[0] + 1
            
            # 如果当前节点超大
            if node_line_cnt > self.config.max_chunk_size:
                # 清理prev_nodes
                if prev_nodes:
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=prev_nodes[0].start_point[0],
                            end_line=prev_nodes[-1].end_point[0],
                            content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                        )
                    )
                    prev_nodes = []
                    
                # 将当前节点加入到chunk中
                file_chunks.append(
                    Chunk(
                        file_path=file_path,
                        start_line=node.start_point[0],
                        end_line=node.end_point[0],
                        content="\n".join(lines[node.start_point[0]: node.end_point[0] + 1]),
                    )
                )
                for seg_idx, seg_line in enumerate(range(node.start_point[0], node.end_point[0], self.config.max_chunk_size)):
                    start_line = seg_line
                    end_line = min(seg_line + self.config.max_chunk_size, node.end_point[0])
                    content = "\n".join(lines[start_line: end_line + 1])
                    if seg_idx != 0:
                        content = lines[node.start_point[0]] + "\n...\n" + content
                    if seg_idx != (node.end_point[0] - node.start_point[0]) // self.config.max_chunk_size:
                        content += "\n...\n" + lines[node.end_point[0]]
                    
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=start_line,
                            end_line=end_line,
                            content=content,
                        )
                    )

                continue
            
            # 如果没有前置节点
            if not prev_nodes:
                if node_line_cnt >= self.config.min_chunk_size and node_line_cnt <= self.config.max_chunk_size:
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=node.start_point[0],
                            end_line=node.end_point[0],
                            content="\n".join(lines[node.start_point[0]: node.end_point[0] + 1]),
                        )
                    )
                else:
                    prev_nodes.append(node)
                continue
            
            # 如果有前置节点
            if prev_nodes:
                if node.end_point[0] - prev_nodes[0].start_point[0] >= self.config.min_chunk_size:
                    if node.end_point[0] - prev_nodes[0].start_point[0] <= self.config.max_chunk_size:
                        file_chunks.append(
                            Chunk(
                                file_path=file_path,
                                start_line=prev_nodes[0].start_point[0],
                                end_line=node.end_point[0],
                                content="\n".join(lines[prev_nodes[0].start_point[0]: node.end_point[0] + 1]),
                            )
                        )
                        prev_nodes = []
                    else:
                        file_chunks.append(
                            Chunk(
                                file_path=file_path,
                                start_line=prev_nodes[0].start_point[0],
                                end_line=prev_nodes[-1].end_point[0],
                                content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                            )
                        )
                        prev_nodes = [node]
                else:
                    prev_nodes.append(node)
                
        # 处理最后一个节点
        if prev_nodes:
            file_chunks.append(
                Chunk(
                    file_path=file_path,
                    start_line=prev_nodes[0].start_point[0],
                    end_line=prev_nodes[-1].end_point[0],
                    content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                )
            )
        
        if not key_structure_lines:
            # 使用file_chunks中的每个chunk的第一行作为key_structure
            for chunk in file_chunks:
                key_structure_lines[chunk.start_line] = (0, lines[chunk.start_line]) # 不记录缩进
                key_structure_lines[chunk.end_line] = (0, lines[chunk.end_line]) # 不记录缩进

       
        key_structure_lines_tuple = sorted(key_structure_lines.items(), key=lambda x: (x[0]))
        key_structure = ""
        for node_idx, (line_idx, (col_idx, line_content)) in enumerate(key_structure_lines_tuple):
            content = f"L{line_idx}: " + line_content
            if node_idx and line_idx - key_structure_lines_tuple[node_idx - 1][0] > 1:
                content = (" " * col_idx) + "...\n" + content
            key_structure += content + "\n"

        return key_structure, file_chunks
    
    def _load_query(self, language_enum: LanguageEnum) -> Optional[str]:
        """Load query string for a language."""
        # Try to load from queries directory
        query_file = Path(__file__).parent / "queries" / f"{language_enum.value[0]}.scm"
        if query_file.exists():
            try:
                return query_file.read_text(encoding="utf-8")
            except Exception as e:
                trace_logger.warning(f"读取查询文件 {query_file} 失败: {e}")

        return None
    
    def _is_keynode(self, capture_name: str) -> bool:
        """判断当前节点是否为关键节点"""
        # Determine entry type based on capture name
        key_patterns = [
            "definition.class",
            "definition.interface",
            "definition.struct",
            "definition.union",
            "definition.enum",
            "definition.function",
            "definition.method",
            "definition.constructor",
            "definition.destructor",
            "definition.operator",
            "definition.namespace",
            "definition.template",
            "definition.function_pointer"
        ]

        return any(pattern in capture_name for pattern in key_patterns)

    def _get_or_create_parser(self, language_enum: LanguageEnum) -> Optional[Parser]:
        """线程安全地获取或创建parser"""
        # 首先尝试无锁读取（双重检查锁定模式）
        if language_enum in self.language_parsers:
            return self.language_parsers[language_enum]

        # 如果不存在，使用锁进行创建
        with self._parser_lock:
            # 再次检查，防止在等待锁的过程中其他线程已经创建了
            if language_enum in self.language_parsers:
                return self.language_parsers[language_enum]

            try:
                from tree_sitter_language_pack import get_parser
                parser = get_parser(language_enum.value[0])
                self.language_parsers[language_enum] = parser
                trace_logger.debug(f"Successfully loaded parser for {language_enum}")
                return parser
            except Exception as e:
                self.language_parsers[language_enum] = None
                trace_logger.info(f"Failed to load parser for {language_enum}: {e}")
                return None

    def _get_or_create_query(self, language_enum: LanguageEnum, parser: Parser) -> Optional[Query]:
        """线程安全地获取或创建query"""
        # 首先尝试无锁读取（双重检查锁定模式）
        if language_enum in self.language_queries:
            return self.language_queries[language_enum]

        # 如果不存在，使用锁进行创建
        with self._query_lock:
            # 再次检查，防止在等待锁的过程中其他线程已经创建了
            if language_enum in self.language_queries:
                return self.language_queries[language_enum]

            query_content = self._load_query(language_enum)
            if not query_content:
                self.language_queries[language_enum] = None
                trace_logger.info(f"Failed to load query content for {language_enum}")
                return None

            try:
                query = Query(parser.language, query_content)
                self.language_queries[language_enum] = query
                trace_logger.debug(f"Successfully loaded query for {language_enum}")
                return query
            except Exception as e:
                self.language_queries[language_enum] = None
                trace_logger.info(f"Failed to create query for {language_enum}: {e}")
                return None

    def _merge_adjacent_chunks(self, chunks: List[Chunk], lines: List[str]) -> List[Chunk]:
        """合并相邻的小 chunks"""
        if len(chunks) <= 1:
            return chunks

        merged_chunks = []
        i = 0

        while i < len(chunks):
            current_chunk = chunks[i]

            # 检查是否可以与下一个 chunk 合并
            if i + 1 < len(chunks):
                next_chunk = chunks[i + 1]
                gap = next_chunk.start_line - current_chunk.end_line - 1
                combined_size = next_chunk.end_line - current_chunk.start_line + 1

                # 合并条件：
                # 1. 间隔小于 min_chunk_size
                # 2. 合并后的大小不超过 max_chunk_size
                # 3. 当前 chunk 不是超大 chunk（大小 > max_chunk_size）
                current_size = current_chunk.end_line - current_chunk.start_line + 1
                if (gap >= 0 and gap < self.config.min_chunk_size and
                    combined_size <= self.config.max_chunk_size and
                    current_size <= self.config.max_chunk_size):

                    # 合并两个 chunks
                    merged_chunk = Chunk(
                        file_path=current_chunk.file_path,
                        start_line=current_chunk.start_line,
                        end_line=next_chunk.end_line,
                        content="\n".join(lines[current_chunk.start_line: next_chunk.end_line + 1]),
                    )
                    merged_chunks.append(merged_chunk)
                    i += 2  # 跳过下一个 chunk，因为已经合并了
                    continue

            # 如果不能合并，直接添加当前 chunk
            merged_chunks.append(current_chunk)
            i += 1

        return merged_chunks

# 线程安全的单例模式实现
_parser_instance = None
_parser_lock = threading.Lock()

def get_file_parser(chunk_config: ChunkConfig) -> FileParser:
    """
    获取FileParser的单例实例，线程安全

    Args:
        chunk_config: 分块配置

    Returns:
        FileParser: 单例实例

    Note:
        使用双重检查锁定模式确保线程安全和性能
    """
    global _parser_instance

    # 首次检查（无锁）
    if _parser_instance is not None:
        return _parser_instance

    # 使用锁进行创建
    with _parser_lock:
        # 二次检查（有锁）
        if _parser_instance is not None:
            return _parser_instance

        # 创建新实例
        trace_logger.info(f"Creating new FileParser instance with config: "
                         f"min_chunk_size={chunk_config.min_chunk_size}, "
                         f"max_chunk_size={chunk_config.max_chunk_size}, "
                         f"overflow_size={chunk_config.overflow_size}")
        _parser_instance = FileParser(chunk_config)
        return _parser_instance

def reset_file_parser() -> None:
    """
    重置FileParser单例实例，主要用于测试或配置更改时清理缓存

    Note:
        这个函数是线程安全的
    """
    global _parser_instance

    with _parser_lock:
        if _parser_instance is not None:
            trace_logger.info("Resetting FileParser singleton instance")
            _parser_instance = None

def get_parser_stats() -> Dict[str, int]:
    """
    获取当前parser的统计信息

    Returns:
        Dict[str, int]: 包含已加载的parser和query数量的统计信息
    """
    global _parser_instance

    if _parser_instance is None:
        return {"loaded_parsers": 0, "loaded_queries": 0}

    with _parser_instance._parser_lock, _parser_instance._query_lock:
        # 统计非None的parser和query数量
        loaded_parsers = sum(1 for parser in _parser_instance.language_parsers.values() if parser is not None)
        loaded_queries = sum(1 for query in _parser_instance.language_queries.values() if query is not None)

        return {
            "loaded_parsers": loaded_parsers,
            "loaded_queries": loaded_queries,
            "total_languages_attempted": len(_parser_instance.language_parsers)
        }
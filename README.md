# Codebase-Dev 智能代码搜索系统

## 📖 项目概述

Codebase-Dev 是一个基于 AI 的智能代码搜索和分析系统，旨在帮助开发者快速理解和搜索大型代码库。系统采用前后端分离架构，结合了传统的 grep 搜索和基于 LLM 的深度搜索技术，为用户提供精准、智能的代码搜索体验。

### 🎯 核心特性

- **🔍 智能搜索**: 支持传统 grep 搜索和基于 LLM 的深度搜索
- **📁 工作区管理**: 支持多个代码仓库的工作区切换
- **🌳 文件树浏览**: 直观的文件目录结构展示
- **💻 代码查看**: 支持语法高亮的代码查看器
- **⚡ 实时搜索**: 流式搜索结果展示，实时反馈搜索进度
- **🎨 现代化UI**: 基于 React + TypeScript + Tailwind CSS 的现代化界面

## 🏗️ 系统架构

### 技术栈

#### 前端
- **框架**: React 19 + TypeScript
- **状态管理**: Zustand
- **样式**: Tailwind CSS
- **构建工具**: Vite
- **代码高亮**: react-syntax-highlighter
- **Markdown渲染**: react-markdown

#### 后端
- **框架**: FastAPI + Python 3.12
- **配置管理**: Pydantic + YAML
- **LLM集成**: OpenAI API 兼容接口
- **搜索引擎**: 多种搜索工具 (Grep, 倒排索引, BM25稀疏检索)
- **数据存储**: SQLite + 文件系统
- **异步处理**: asyncio + 流式响应
- **代码分块**: AST解析 + 行级分块

### 目录结构

```
Codebase-Dev/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   │   ├── layout/       # 布局组件
│   │   │   └── ui/           # UI基础组件
│   │   ├── features/         # 功能模块
│   │   │   ├── file-viewer/  # 文件查看器
│   │   │   ├── search/       # 搜索功能
│   │   │   └── workspace/    # 工作区管理
│   │   ├── services/         # API服务
│   │   ├── store/            # 状态管理
│   │   ├── types/            # 类型定义
│   │   └── utils/            # 工具函数
│   └── package.json
├── backend/                  # 后端项目
│   └── python/
│       ├── core/             # 核心配置系统
│       ├── server/           # FastAPI服务器
│       │   └── api/v1/       # RESTful API路由
│       ├── modules/          # 核心业务模块
│       │   ├── searchrouter/ # 智能搜索路由器 (核心)
│       │   ├── deepsearch/   # 传统深度搜索
│       │   ├── integrations/ # 工具集成层
│       │   │   ├── tools/    # 搜索和IO工具
│       │   │   │   ├── search/  # 搜索工具实现
│       │   │   │   └── io/      # 文件IO工具
│       │   │   └── database/ # 数据库集成
│       │   ├── chunks/       # 代码分块服务
│       │   ├── term/         # 分词和索引服务
│       │   ├── llm/          # LLM客户端
│       │   └── common/       # 通用数据结构
│       ├── utils/            # 工具函数
│       ├── config/           # 配置文件
│       └── data/             # 数据存储
│           ├── repos/        # 代码仓库
│           └── database.db   # SQLite数据库
├── tests/                    # 测试文件
└── deploy/                   # 部署配置
```

## 🔧 核心模块详解

### 前端模块 (简要说明)

#### 1. 工作区管理 (Workspace)
- **WorkspaceSelector**: 工作区选择器，支持多仓库切换
- **FileTree**: 文件树组件，展示目录结构
- **useWorkspace**: 工作区状态管理 Hook

#### 2. 文件查看器 (File Viewer)
- **FileViewer**: 主文件查看组件
- **CodeViewer**: 代码语法高亮显示
- **useFileContent**: 文件内容加载 Hook

#### 3. 搜索功能 (Search)
- **SearchPanel**: 搜索面板，支持查询输入和结果展示
- **useSearch**: 搜索逻辑处理 Hook
- 支持实时流式搜索结果展示

### 后端核心架构 (重点)

#### 1. API 服务层 (server/api/v1/)
- **FastAPI 应用**: 提供 RESTful API 接口
- **核心路由**: `/api/v1/searchrouter` - 智能搜索路由
- **CORS 配置**: 跨域请求支持
- **流式响应**: 支持 Server-Sent Events

#### 2. 智能搜索路由器 (SearchRouter) - 核心组件
**SearchRouter** 是系统的核心搜索引擎，采用多阶段智能搜索策略：

**核心特性**:
- **查询分解**: 将复杂查询分解为多个子查询
- **上下文感知**: 基于仓库结构和文件内容进行智能搜索
- **多工具集成**: 统一调度不同类型的搜索工具
- **结果优化**: 智能合并、去重和重排序

**主要方法**:
- `search()`: 主搜索入口，协调整个搜索流程
- `_split_queris()`: 基于LLM的查询分解
- `_generate_new_queries()`: 生成新的搜索查询
- `_search_and_filter()`: 并发搜索和过滤
- `_merge_snippets()`: 代码片段合并和去重
- `_reorder_snippets()`: 基于LLM的结果重排序

#### 3. 搜索工具层 (integrations/tools/search/)
**工具架构设计**:
- **SearchToolABC**: 搜索工具抽象基类，定义统一接口
- **AnySearchTool**: 搜索工具路由器，统一调度各种搜索工具
- **工厂模式**: `search_factory.py` 负责工具实例创建和缓存

**具体搜索工具**:
- **GrepSearchTool**: 基于系统grep命令的文本搜索
- **InvertedIndexSearchTool**: 基于倒排索引的关键词搜索
- **TermSparseSearchTool**: 基于BM25算法的稀疏检索
- **EmbeddingSearchTool**: 基于向量嵌入的语义搜索 (待实现)

#### 4. IO工具层 (integrations/tools/io/)
**文件操作抽象**:
- **AnyIOTool**: IO工具统一入口
- **FileIOTool**: 文件读写操作，支持行号范围读取
- **DirectoryIOTool**: 目录结构读取和遍历

#### 5. 代码分块服务 (chunks/)
**分块策略**:
- **IChunk**: 分块接口抽象
- **BaseLineChunk**: 基于行的简单分块策略
- **ASTChunk**: 基于AST语法树的智能分块
- **ChunkFactory**: 分块服务工厂，支持动态切换分块策略

**Chunk数据结构**:
```python
class Chunk(BaseModel):
    file_path: str      # 文件路径
    start_line: int     # 起始行号 (0-based)
    end_line: int       # 结束行号 (0-based)
    content: str        # 代码内容

class CodeSnippet(Chunk):
    context_before: str # 前置上下文
    context_after: str  # 后置上下文
    score: float        # 相关性分数
```

#### 6. 分词和索引服务 (term/)
**核心功能**:
- **分词处理**: 支持中英文分词，代码标识符提取
- **BM25索引**: 计算词频-逆文档频率，构建稀疏检索索引
- **倒排索引**: 构建关键词到文档的映射关系
- **缓存机制**: 索引数据持久化到SQLite数据库

#### 7. 数据存储层 (integrations/database/)
**SQLite数据库设计**:
```sql
-- 仓库表
CREATE TABLE repos (
    repo_id INTEGER PRIMARY KEY,
    repo_path TEXT UNIQUE NOT NULL,
    updated_at INTEGER NOT NULL,
    term_sparse TEXT,           -- BM25索引数据(JSON)
    inverted_index TEXT         -- 倒排索引数据(JSON)
);

-- 文件表
CREATE TABLE repo_files (
    file_id INTEGER PRIMARY KEY,
    repo_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    file_structure TEXT NOT NULL,  -- 文件关键结构
    file_hash TEXT NOT NULL,       -- 文件哈希值
    updated_at INTEGER NOT NULL
);
```

#### 8. LLM集成层 (llm/)
- **LLMClient**: 统一的LLM调用接口
- **异步支持**: 支持并发LLM请求
- **提示词管理**: 结构化的提示词模板系统
- **错误处理**: 完善的超时和重试机制

## 📊 核心业务流程和数据流

### 1. 系统初始化和仓库预处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant API as FastAPI服务
    participant SR as SearchRouter
    participant DB as SQLite数据库
    participant FS as 文件系统

    U->>F: 访问应用
    F->>F: 初始化 Zustand Store
    F->>API: GET /api/v1/workspaces
    API->>FS: 扫描 repos 目录
    FS->>API: 返回工作区列表
    API->>F: 返回工作区数据
    F->>F: 自动选择第一个工作区

    Note over SR,DB: 仓库预处理阶段
    API->>SR: 初始化SearchRouter
    SR->>DB: 检查仓库是否已索引
    alt 仓库未索引或需更新
        SR->>FS: 扫描仓库文件
        SR->>SR: 代码分块 (Chunk)
        SR->>SR: 构建BM25索引
        SR->>SR: 构建倒排索引
        SR->>DB: 存储索引数据
    else 仓库已索引
        SR->>DB: 加载缓存索引
    end

    F->>U: 显示工作区界面
```

### 2. SearchRouter核心搜索流程

```mermaid
flowchart TD
    A[用户查询] --> B[SearchRouter.search]
    B --> C[构建仓库文件树]
    C --> D[LLM查询分解]
    D --> E[_split_queris]
    E --> F[解析上下文操作]
    F --> G[执行IO操作]
    G --> H[生成新查询]
    H --> I[_generate_new_queries]
    I --> J[并发搜索]
    J --> K[_search_and_filter]

    K --> L{选择搜索工具}
    L -->|grep| M[GrepSearchTool]
    L -->|倒排索引| N[InvertedIndexTool]
    L -->|BM25稀疏| O[TermSparseTool]

    M --> P[收集搜索结果]
    N --> P
    O --> P

    P --> Q[代码片段合并]
    Q --> R[_merge_snippets]
    R --> S[文件级合并]
    S --> T[_merge_file_snippets]
    T --> U[LLM重排序]
    U --> V[_reorder_snippets]
    V --> W[返回最终结果]

    style B fill:#e1f5fe
    style K fill:#f3e5f5
    style U fill:#e8f5e8
```

### 3. 搜索工具架构和数据流

```mermaid
classDiagram
    class SearchToolABC {
        <<abstract>>
        +search(query: str) List~CodeSnippet~
        +search_async(query: str) List~CodeSnippet~
        +description: str
        +examples: str
    }

    class AnySearchTool {
        +enabled_search_tools: List~SearchToolEnum~
        +search(query: str, search_tool: SearchToolEnum)
    }

    class GrepSearchTool {
        +search(query: str) List~CodeSnippet~
        +_execute_grep_command()
    }

    class InvertedIndexSearchTool {
        +inverted_index: Dict
        +search(query: str) List~CodeSnippet~
        +_load_inverted_index()
    }

    class TermSparseSearchTool {
        +term_idf: Dict
        +chunks_term_freqs: Dict
        +search(query: str) List~CodeSnippet~
        +_calculate_bm25_score()
    }

    class SearchFactory {
        +get_search_tool_instance()
        +_create_search_tool_instance()
        +clear_instance_pool()
    }

    SearchToolABC <|-- AnySearchTool
    SearchToolABC <|-- GrepSearchTool
    SearchToolABC <|-- InvertedIndexSearchTool
    SearchToolABC <|-- TermSparseSearchTool

    AnySearchTool --> SearchFactory
    SearchFactory --> GrepSearchTool
    SearchFactory --> InvertedIndexSearchTool
    SearchFactory --> TermSparseSearchTool
```

### 4. 代码分块和索引构建流程

```mermaid
sequenceDiagram
    participant SR as SearchRouter
    participant CF as ChunkFactory
    participant CS as ChunkService
    participant TS as TermService
    participant DB as SQLite数据库
    participant FS as 文件系统

    Note over SR,DB: 仓库预处理阶段
    SR->>CF: getChunkService("line"|"ast")
    CF->>CS: 返回分块服务实例

    SR->>FS: 扫描仓库文件
    FS->>SR: 返回文件列表

    loop 处理每个文件
        SR->>CS: chunk_file(file_path, content)
        CS->>CS: 执行分块策略
        CS->>SR: 返回 (key_structure, chunks)

        SR->>TS: 提取文件关键词
        TS->>TS: 分词处理
        TS->>TS: 计算词频
    end

    SR->>TS: calculate_bm25_chunk_terms()
    TS->>TS: 计算BM25索引
    TS->>TS: 计算倒排索引

    SR->>DB: 存储索引数据
    DB->>DB: 更新repo表
    DB->>DB: 更新repo_files表

    Note over SR,DB: 索引构建完成，可开始搜索
```

### 5. 完整的搜索请求处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant API as FastAPI
    participant SR as SearchRouter
    participant LLM as LLM客户端
    participant ST as 搜索工具
    participant DB as 数据库

    U->>F: 输入搜索查询
    F->>API: POST /api/v1/searchrouter
    API->>SR: SearchRouter.search(query)

    Note over SR,LLM: 第一阶段：查询理解和分解
    SR->>SR: 构建仓库文件树
    SR->>LLM: 查询分解请求
    LLM->>SR: 返回子查询和上下文操作

    Note over SR,ST: 第二阶段：上下文信息收集
    SR->>SR: 执行IO操作读取文件
    SR->>LLM: 基于上下文生成新查询
    LLM->>SR: 返回优化查询列表

    Note over SR,DB: 第三阶段：并发搜索执行
    par 并行搜索
        SR->>ST: Grep搜索
        SR->>ST: 倒排索引搜索
        SR->>ST: BM25稀疏搜索
    end

    ST->>SR: 返回代码片段

    Note over SR,LLM: 第四阶段：结果优化
    SR->>SR: 代码片段合并去重
    SR->>SR: 文件级别合并
    SR->>LLM: 重排序请求
    LLM->>SR: 返回排序分数

    SR->>API: 返回最终结果
    API->>F: JSON响应
    F->>U: 显示搜索结果
```

## 🧠 核心算法和数据结构

### 1. 代码分块算法

#### 行级分块 (BaseLineChunk)
```python
def chunk_file(self, file_path: str, file_content: str,
               window_size: int = 50, overflow_size: int = 10):
    """
    基于滑动窗口的行级分块
    - window_size: 每个chunk的行数
    - overflow_size: 重叠行数，保证上下文连续性
    """
    lines = file_content.splitlines()
    step = max(1, window_size - overflow_size)

    for i in range(0, len(lines), step):
        start_line = i
        end_line = min(i + window_size - 1, len(lines) - 1)
        chunk_content = "\n".join(lines[start_line:end_line + 1])

        yield Chunk(
            file_path=file_path,
            start_line=start_line,
            end_line=end_line,
            content=chunk_content
        )
```

#### AST分块 (ASTChunk)
- 基于TreeSitter解析器进行语法感知分块
- 按函数、类、模块等语法单元进行分块
- 保持代码语义完整性

### 2. BM25稀疏检索算法

```python
def calculate_bm25_score(query_terms: List[str], chunk_id: str,
                        k1: float = 1.2, b: float = 0.75):
    """
    BM25评分算法实现
    - k1: 词频饱和参数
    - b: 长度归一化参数
    """
    score = 0.0
    chunk_length = len(chunk_term_freqs[chunk_id])

    for term in query_terms:
        if term in chunk_term_freqs[chunk_id]:
            tf = chunk_term_freqs[chunk_id][term]
            idf = term_idf.get(term, 0)

            # BM25公式
            score += idf * (tf * (k1 + 1)) / (
                tf + k1 * (1 - b + b * chunk_length / avg_chunk_length)
            )

    return score
```

### 3. 智能查询分解策略

SearchRouter采用基于LLM的查询分解策略：

1. **上下文感知分解**: 结合仓库结构信息进行查询分解
2. **工具选择**: 为每个子查询选择最适合的搜索工具
3. **迭代优化**: 基于搜索结果动态调整查询策略

### 4. 代码片段合并算法

```python
def _merge_snippets(self, snippets: List[CodeSnippet]):
    """
    智能合并重叠的代码片段
    - 按文件路径和行号排序
    - 合并重叠区域
    - 累加相关性分数
    """
    sorted_snippets = sorted(snippets,
                           key=lambda x: (x.file_path, x.start_line))

    merged = []
    for snippet in sorted_snippets:
        if (merged and
            merged[-1].file_path == snippet.file_path and
            merged[-1].end_line >= snippet.start_line):
            # 合并重叠片段
            merged[-1].end_line = max(merged[-1].end_line, snippet.end_line)
            merged[-1].score += snippet.score
        else:
            merged.append(snippet)

    return merged
```

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **Python**: >= 3.12
- **系统**: macOS/Linux (支持 grep 命令)

### 安装步骤

#### 1. 克隆项目

```bash
git clone <repository-url>
cd Codebase-Dev
```

#### 2. 后端设置

```bash
cd backend/python

# 安装依赖 (推荐使用 uv)
uv sync

# 或使用 pip
pip install -e .

# 配置环境
cp config/config.yaml config/config.dev.yaml
# 编辑 config.dev.yaml 配置 LLM API 信息
```

#### 3. 前端设置

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 4. 启动后端服务

```bash
cd backend/python

# 启动开发服务器
python main.py --env dev
```

### 配置说明

#### 后端配置 (config/config.yaml)

```yaml
# 日志配置
log:
  dir: ./logs
  name: app.log
  level: debug
  max_size_m: 5

# API 服务配置
api:
  host: "0.0.0.0"
  port: 3451
  reload: true
  version: 1
  # 跨域配置
  cors:
    origins:
      - "http://localhost:3000"
      - "http://localhost:5173"
    allow_credentials: true
    allow_methods: ["*"]
    allow_headers: ["*"]

# 数据配置
data:
  repos_path: ./data/repos
  ignored_terms_path: ./data/ignored_terms.txt

# LLM 配置
llm:
  base_url: "your-llm-api-url"
  api_key: "your-api-key"
  model: "your-model-name"
  temperature: 0.7
  max_tokens: 8192
  stream: false
  timeout: 60
  max_concurrent_requests: 10

# SearchRouter配置
deepsearch:
  max_iterations: 1                    # 最大搜索轮数
  max_sub_queries: 3                   # 每次拆分的最大子查询数量
  max_new_queries: 2                   # 每次生成的新查询最大数量
  max_workers: 16                      # 最大并发线程数
  enabled_search_tools:                # 启用的搜索工具
    - "grep"
    - "inverted_index"
    - "term_sparse"
  parallel_filter:
    enabled: true                      # 是否启用并行过滤
    min_snippets_for_parallel: 5       # 启用并行过滤的最小代码片段数量
    max_workers: 10                    # 最大并行线程数

# 代码分块配置
chunk:
  name: "line"                         # 分块策略: "line" | "ast"
  min_chunk_size: 50                   # 最小分块大小
  max_chunk_size: 100                  # 最大分块大小
  overflow_size: 10                    # 分块重叠大小

# 文件过滤配置
file_filter:
  local_include: [".py", ".js", ".ts", ".java", ".cpp", ".go", ".rs"]
  local_exclude: [".git", "node_modules", "__pycache__", ".venv"]
  embedding_include: [".py", ".js", ".ts", ".java"]
  embedding_exclude: [".git", "node_modules", "__pycache__"]
  max_file_size: 1048576               # 1MB

# 数据库配置
database:
  sqlite:
    db_path: "./data/database.db"
    timeout: 30.0
    check_same_thread: false
    isolation_level: null
```

## 📁 数据准备

### 添加代码仓库

1. 将要搜索的代码仓库放入 `backend/python/data/repos/` 目录
2. 每个子目录代表一个工作区
3. 系统会自动扫描并在前端显示可用的工作区

示例目录结构：
```
backend/python/data/repos/
├── my-project-1/
│   ├── src/
│   └── README.md
├── my-project-2/
│   ├── app/
│   └── package.json
└── open-source-lib/
    ├── lib/
    └── docs/
```

## 📚 API 文档

### 核心 API 接口

#### 1. 获取工作区列表

```http
GET /api/v1/workspaces
```

**响应示例:**
```json
[
  {
    "id": "my-project-1",
    "name": "My Project 1",
    "path": "/path/to/repos/my-project-1",
    "description": "项目描述"
  }
]
```

#### 2. 获取工作区文件树

```http
GET /api/v1/workspaces/{workspace_name}/files
```

**响应示例:**
```json
[
  {
    "name": "src",
    "path": "my-project-1/src",
    "type": "directory",
    "children": [
      {
        "name": "main.py",
        "path": "my-project-1/src/main.py",
        "type": "file",
        "size": 1024
      }
    ]
  }
]
```

#### 3. 获取文件内容

```http
GET /api/v1/files?file_path={file_path}
```

**响应示例:**
```json
{
  "content": "文件内容",
  "language": "python",
  "size": 1024
}
```

#### 4. 执行智能搜索 (SearchRouter)

```http
POST /api/v1/searchrouter
Content-Type: application/json

{
  "query": "如何实现用户认证功能",
  "workspaceName": "my-project-1",
  "search_tool": "any",
  "max_results": 20,
  "is_stream": false
}
```

**请求参数说明:**
- `query`: 自然语言查询，支持复杂的功能描述
- `workspaceName`: 目标工作区名称
- `search_tool`: 搜索工具类型 ("grep" | "inverted_index" | "term_sparse" | "any")
- `max_results`: 最大返回结果数量
- `is_stream`: 是否使用流式响应

**响应示例:**
```json
{
  "query": "如何实现用户认证功能",
  "code_snippets": "[{\"file_path\":\"auth/login.py\",\"start_line\":10,\"end_line\":25,\"content\":\"def authenticate_user(username, password):\\n    # 用户认证逻辑\\n    ...\",\"score\":0.95}]"
}
```

#### 5. 传统搜索 (兼容接口)

```http
POST /api/v1/search
Content-Type: application/json

{
  "query": "function authenticate",
  "workspaceName": "my-project-1",
  "search_tool": "grep",
  "max_results": 50
}
```

## 🔍 搜索功能详解

### 搜索架构概览

系统采用**多层次搜索架构**，结合传统文本搜索和现代AI技术：

```
用户查询 → SearchRouter → 多种搜索工具 → 智能结果合并 → 最终结果
```

### 核心搜索组件

#### 1. SearchRouter (智能搜索路由器)
**核心特性**:
- **查询理解**: 基于LLM的自然语言查询分解
- **上下文感知**: 结合仓库结构进行智能搜索
- **多工具协调**: 统一调度不同类型的搜索工具
- **结果优化**: 智能合并、去重和重排序

**适用场景**:
- 复杂功能查找: "如何实现用户认证"
- 概念性搜索: "错误处理机制"
- 架构理解: "数据库连接管理"

#### 2. 多种搜索工具

##### GrepSearchTool
- **特点**: 基于系统grep命令的高速文本搜索
- **适用场景**: 精确的函数名、变量名、关键词查找
- **优势**: 速度快、资源消耗低

##### InvertedIndexSearchTool
- **特点**: 基于倒排索引的关键词搜索
- **适用场景**: 多关键词组合搜索
- **优势**: 支持复杂查询，搜索精度高

##### TermSparseSearchTool
- **特点**: 基于BM25算法的稀疏检索
- **适用场景**: 相关性排序、语义相似搜索
- **优势**: 考虑词频和文档长度，结果更相关

##### EmbeddingSearchTool (规划中)
- **特点**: 基于向量嵌入的语义搜索
- **适用场景**: 语义相似代码查找
- **优势**: 理解代码语义，跨语言搜索

### 搜索流程详解

#### 阶段1: 查询预处理
1. **仓库结构分析**: 构建文件树，了解项目结构
2. **查询分解**: LLM将复杂查询分解为多个子查询
3. **工具选择**: 为每个子查询选择最适合的搜索工具

#### 阶段2: 并发搜索执行
1. **多工具并行**: 同时使用多种搜索工具
2. **结果收集**: 汇总所有搜索工具的结果
3. **初步过滤**: 移除明显不相关的结果

#### 阶段3: 智能结果优化
1. **代码片段合并**: 合并重叠的代码片段
2. **文件级聚合**: 将同一文件的片段合并
3. **LLM重排序**: 基于查询相关性重新排序
4. **结果截断**: 返回最相关的前N个结果

### 性能优化策略

#### 1. 索引预构建
- **BM25索引**: 预计算词频-逆文档频率
- **倒排索引**: 预构建关键词到文档的映射
- **增量更新**: 仅对变更文件重建索引

#### 2. 智能缓存
- **搜索工具实例缓存**: 避免重复初始化
- **索引数据缓存**: SQLite持久化存储
- **查询结果缓存**: 相同查询快速返回

#### 3. 并发优化
- **异步搜索**: 所有搜索操作异步执行
- **线程池管理**: 可配置的并发线程数
- **负载均衡**: 智能分配搜索任务

#### 4. 资源控制
- **文件大小限制**: 跳过超大文件避免内存溢出
- **仓库大小检测**: 大型仓库自动降级为grep搜索
- **超时控制**: 防止长时间搜索阻塞

## 🛠️ 开发指南

### 系统架构总览

系统采用分层架构设计，各层职责清晰，便于维护和扩展：

![系统架构图](上面已渲染的Mermaid图)

### 后端开发指南 (重点)

#### 1. 搜索工具扩展

**实现新的搜索工具**:

```python
from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.common.schema import CodeSnippet
from typing import List

class CustomSearchTool(SearchToolABC):
    """自定义搜索工具示例"""

    def __init__(self, repo_path: str, **kwargs):
        self.repo_path = repo_path
        # 初始化自定义参数

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """实现搜索逻辑"""
        snippets = []
        # 自定义搜索实现
        # 1. 解析查询
        # 2. 执行搜索
        # 3. 构建CodeSnippet对象
        return snippets

    @property
    def description(self) -> str:
        return "自定义搜索工具的描述"

    @property
    def examples(self) -> str:
        return "使用示例"
```

**注册新工具**:

```python
# 在 search_factory.py 中添加
def _create_search_tool_instance(search_tool: SearchToolEnum, repo_path: str, **kwargs):
    if search_tool == SearchToolEnum.CUSTOM:
        from modules.integrations.tools.search.custom_search import CustomSearchTool
        return CustomSearchTool(repo_path, **kwargs)
    # ... 其他工具
```

#### 2. 代码分块策略扩展

**实现新的分块策略**:

```python
from modules.chunks.IChunk import IChunk
from modules.common.schema import Chunk
from typing import List, Tuple

class CustomChunk(IChunk):
    """自定义分块策略"""

    def chunk_file(self, file_path: str, file_content: str, **kwargs) -> Tuple[str, List[Chunk]]:
        """
        实现自定义分块逻辑

        Returns:
            Tuple[str, List[Chunk]]: (文件关键结构, 代码块列表)
        """
        chunks = []
        key_structure = ""

        # 自定义分块实现
        # 1. 分析文件内容
        # 2. 确定分块边界
        # 3. 生成Chunk对象

        return key_structure, chunks
```

#### 3. API路由开发

```python
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from modules.searchrouter.search_router import SearchRouter

router = APIRouter(prefix="/api/v1")

class CustomSearchRequest(BaseModel):
    query: str
    workspace_name: str
    custom_params: dict = {}

@router.post("/custom-search")
async def custom_search(request: CustomSearchRequest):
    """自定义搜索接口"""
    try:
        # 初始化SearchRouter
        search_router = SearchRouter(
            repo_path=f"./data/repos/{request.workspace_name}",
            search_tool=SearchToolEnum.CUSTOM
        )

        # 执行搜索
        result = await search_router.search(request.query)

        return {
            "query": result.original_query,
            "code_snippets": [snippet.model_dump() for snippet in result.code_snippets]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 前端开发指南 (简要)

#### 组件开发规范

```typescript
// 组件接口定义
interface SearchComponentProps {
  onSearch: (query: string) => void;
  results: CodeSnippet[];
  loading: boolean;
}

// 组件实现
export function SearchComponent({ onSearch, results, loading }: SearchComponentProps) {
  const [query, setQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query);
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="输入搜索查询..."
      />
      <button type="submit" disabled={loading}>
        {loading ? '搜索中...' : '搜索'}
      </button>
    </form>
  );
}
```

### 后端开发

#### API 路由开发

```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter(prefix="/api/v1")

class RequestModel(BaseModel):
    field: str

class ResponseModel(BaseModel):
    result: str

@router.post("/endpoint", response_model=ResponseModel)
async def endpoint(request: RequestModel):
    """API 端点描述"""
    try:
        # 业务逻辑处理
        result = process_request(request.field)
        return ResponseModel(result=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### 搜索工具扩展

```python
from modules.common.search_tool import SearchToolABC
from modules.common.schema import CodeSnippet

class CustomSearchTool(SearchToolABC):
    """自定义搜索工具"""

    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def search(self, query: str) -> List[CodeSnippet]:
        """实现搜索逻辑"""
        # 自定义搜索实现
        return snippets
```

## 🧪 测试

### 运行测试

```bash
# 后端测试
cd backend/python
pytest tests/

# 前端测试
cd frontend
npm test
```

### 测试覆盖

- **配置系统测试**: 验证配置加载和验证逻辑
- **搜索功能测试**: 测试各种搜索场景
- **API 接口测试**: 验证 API 响应和错误处理
- **组件单元测试**: 前端组件功能测试

## 🚀 部署

### 生产环境部署

#### 1. 前端构建

```bash
cd frontend
npm run build
# 构建产物在 dist/ 目录
```

#### 2. 后端部署

```bash
cd backend/python

# 生产环境配置
cp config/config.yaml config/config.prod.yaml
# 编辑生产环境配置

# 启动生产服务
python main.py --env prod
```

#### 3. Docker 部署 (推荐)

```dockerfile
# 后端 Dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY backend/python/ .

RUN pip install -e .

EXPOSE 3451
CMD ["python", "main.py", "--env", "prod"]
```

```dockerfile
# 前端 Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY frontend/ .

RUN npm install && npm run build

FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
EXPOSE 80
```

#### 4. Docker Compose

```yaml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: deploy/backend.dockerfile
    ports:
      - "3451:3451"
    volumes:
      - ./data:/app/data
    environment:
      - ENV=prod

  frontend:
    build:
      context: .
      dockerfile: deploy/frontend.dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
```

### 性能优化

#### 1. 后端优化
- **并发配置**: 调整 uvicorn workers 数量
- **内存管理**: 配置合适的文件大小限制
- **缓存策略**: 实现搜索结果缓存

#### 2. 前端优化
- **代码分割**: 使用 React.lazy 进行路由级别的代码分割
- **虚拟滚动**: 大文件列表使用虚拟滚动
- **缓存策略**: 实现文件内容缓存

## 🔧 故障排除

### 常见问题

#### 1. 后端启动失败

**问题**: `ImportError: Cannot import API module`
**解决方案**:
```bash
# 检查 Python 版本
python --version  # 需要 >= 3.12

# 重新安装依赖
cd backend/python
uv sync  # 或 pip install -e .

# 检查配置文件
python -c "from core.config import get_config; print(get_config())"
```

#### 2. SearchRouter初始化失败

**问题**: `Repo preparation failed` 或索引构建错误
**解决方案**:
```bash
# 检查仓库路径权限
ls -la data/repos/

# 清理数据库重新索引
rm data/database.db

# 检查仓库大小 (超过50MB会自动降级)
du -sh data/repos/*

# 查看详细日志
tail -f logs/app.log | grep "SearchRouter"
```

#### 3. 搜索工具异常

**问题**: 特定搜索工具报错
**解决方案**:
```bash
# 检查grep命令可用性
which grep

# 检查SQLite数据库
sqlite3 data/database.db ".tables"

# 测试搜索工具
python -c "
from modules.integrations.tools.search.search_factory import get_search_tool_instance
from modules.common.constants import SearchToolEnum
tool = get_search_tool_instance(SearchToolEnum.GREP, './data/repos/test-repo')
print(tool.search('test'))
"
```

#### 4. LLM调用失败

**问题**: `LLM API call failed` 或超时
**解决方案**:
```yaml
# 检查配置文件 config/config.yaml
llm:
  base_url: "正确的API地址"
  api_key: "有效的API密钥"
  model: "支持的模型名称"
  timeout: 60  # 增加超时时间
  max_concurrent_requests: 5  # 降低并发数
```

```bash
# 测试LLM连接
python -c "
from modules.llm.llm_client import default_llm_client
import asyncio
result = asyncio.run(default_llm_client.call_async('test', 'test'))
print(result)
"
```

#### 5. 代码分块失败

**问题**: `Chunk processing failed`
**解决方案**:
```bash
# 检查分块配置
grep -A 10 "chunk:" config/config.yaml

# 测试分块服务
python -c "
from modules.chunks.chunk_factory import getChunkService
chunker = getChunkService('line')()
result = chunker.chunk_file('test.py', 'print(\"hello\")')
print(result)
"

# 切换分块策略 (在config.yaml中)
chunk:
  name: "line"  # 改为 "ast" 如果line模式有问题
```

#### 6. 数据库连接问题

**问题**: SQLite数据库锁定或损坏
**解决方案**:
```bash
# 检查数据库文件
ls -la data/database.db

# 检查数据库完整性
sqlite3 data/database.db "PRAGMA integrity_check;"

# 重建数据库
rm data/database.db
# 重启服务会自动重建

# 检查数据库统计
python -c "
from modules.integrations.database.sqlite.client import get_sqlite_client
client = get_sqlite_client()
print(client.get_database_stats())
"
```

### 日志分析

#### 后端日志

```bash
# 查看实时日志
tail -f backend/python/logs/app.log

# 搜索错误日志
grep "ERROR" backend/python/logs/app.log

# 搜索特定功能日志
grep "DeepSearch" backend/python/logs/app.log
```

#### 前端调试

```javascript
// 开启详细日志
localStorage.setItem('debug', 'true');

// 查看网络请求
// 打开浏览器开发者工具 -> Network 标签

// 查看状态管理
// 安装 Redux DevTools 扩展
```

## 🤝 贡献指南

### 开发流程

1. **Fork 项目**
2. **创建功能分支**: `git checkout -b feature/new-feature`
3. **提交更改**: `git commit -m 'Add new feature'`
4. **推送分支**: `git push origin feature/new-feature`
5. **创建 Pull Request**

### 代码规范

#### Python 代码规范
- 遵循 PEP 8 标准
- 使用类型注解
- 编写文档字符串
- 单元测试覆盖率 > 80%

#### TypeScript 代码规范
- 使用 ESLint 配置
- 严格的类型检查
- 组件 Props 接口定义
- 遵循 React Hooks 规范

### 提交信息规范

```
type(scope): description

feat(search): add deep search functionality
fix(ui): resolve file tree rendering issue
docs(readme): update installation guide
test(api): add search endpoint tests
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的 Python Web 框架
- [React](https://reactjs.org/) - 用户界面构建库
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Zustand](https://github.com/pmndrs/zustand) - 轻量级状态管理
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-repo/issues)
- 发送邮件至: <EMAIL>
- 项目讨论: [Discussions](https://github.com/your-repo/discussions)

---

**⭐ 如果这个项目对你有帮助，请给它一个 Star！**